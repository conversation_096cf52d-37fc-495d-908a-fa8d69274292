/**
 * <PERSON>ling AI 任务查询示例
 *
 * 这个文件展示了如何在项目中使用 Kling AI 任务查询功能
 */

// 加载环境变量
require('dotenv').config({ path: '.env.local' });
require('dotenv').config(); // 备用加载 .env 文件

const { getTaskList, getTaskDetail } = require('./test-kling-tasks.js');

// 示例1: 查询最近的成功任务
async function getRecentSuccessTasks() {
  console.log('📝 示例1: 查询最近的成功任务');
  
  try {
    const result = await getTaskList({
      status: 'succeed',
      size: 5,
      page: 1
    });
    
    if (result.code === 0 && result.data && result.data.tasks) {
      console.log(`✅ 找到 ${result.data.tasks.length} 个成功任务:`);
      
      result.data.tasks.forEach((task, index) => {
        console.log(`${index + 1}. ${task.task_id} - ${new Date(task.created_at * 1000).toLocaleString()}`);
      });
      
      return result.data.tasks;
    } else {
      console.log('❌ 没有找到成功任务');
      return [];
    }
  } catch (error) {
    console.error('❌ 查询失败:', error.message);
    return [];
  }
}

// 示例2: 查询特定任务的详细信息
async function getTaskDetails(taskId) {
  console.log(`\n📝 示例2: 查询任务详情 - ${taskId}`);
  
  try {
    const result = await getTaskDetail(taskId);
    
    if (result.code === 0 && result.data) {
      const task = result.data;
      console.log('✅ 任务详情:');
      console.log(`   ID: ${task.task_id}`);
      console.log(`   状态: ${task.task_status}`);
      console.log(`   创建时间: ${new Date(task.created_at * 1000).toLocaleString()}`);
      console.log(`   更新时间: ${new Date(task.updated_at * 1000).toLocaleString()}`);
      
      if (task.task_result && task.task_result.videos) {
        console.log(`   生成视频: ${task.task_result.videos.length} 个`);
        task.task_result.videos.forEach((video, index) => {
          console.log(`     视频 ${index + 1}: ${video.url}`);
        });
      }
      
      return task;
    } else {
      console.log('❌ 任务不存在或查询失败');
      return null;
    }
  } catch (error) {
    console.error('❌ 查询失败:', error.message);
    return null;
  }
}

// 示例3: 监控任务状态变化
async function monitorTaskStatus(taskId, maxAttempts = 10, interval = 5000) {
  console.log(`\n📝 示例3: 监控任务状态 - ${taskId}`);
  
  let attempts = 0;
  
  while (attempts < maxAttempts) {
    try {
      const result = await getTaskDetail(taskId);
      
      if (result.code === 0 && result.data) {
        const task = result.data;
        const status = task.task_status;
        const timestamp = new Date().toLocaleString();
        
        console.log(`[${timestamp}] 任务状态: ${status}`);
        
        // 如果任务完成（成功或失败），停止监控
        if (status === 'succeed' || status === 'failed') {
          console.log(`✅ 任务已完成，最终状态: ${status}`);
          
          if (status === 'succeed' && task.task_result && task.task_result.videos) {
            console.log('🎬 生成的视频:');
            task.task_result.videos.forEach((video, index) => {
              console.log(`   视频 ${index + 1}: ${video.url}`);
            });
          }
          
          return task;
        }
        
        // 等待下次检查
        if (attempts < maxAttempts - 1) {
          console.log(`⏳ 等待 ${interval/1000} 秒后再次检查...`);
          await new Promise(resolve => setTimeout(resolve, interval));
        }
        
      } else {
        console.log('❌ 查询任务失败');
        break;
      }
      
      attempts++;
    } catch (error) {
      console.error('❌ 监控出错:', error.message);
      break;
    }
  }
  
  console.log('⏰ 监控超时或出错，停止监控');
  return null;
}

// 示例4: 获取任务统计信息
async function getTaskStatistics() {
  console.log('\n📝 示例4: 获取任务统计信息');
  
  const statuses = ['submitted', 'processing', 'succeed', 'failed'];
  const stats = {};
  
  try {
    for (const status of statuses) {
      const result = await getTaskList({ status, size: 1 });
      if (result.code === 0 && result.data) {
        stats[status] = result.data.total || 0;
      } else {
        stats[status] = 0;
      }
    }
    
    console.log('📊 任务统计:');
    console.log(`   已提交: ${stats.submitted}`);
    console.log(`   处理中: ${stats.processing}`);
    console.log(`   已成功: ${stats.succeed}`);
    console.log(`   已失败: ${stats.failed}`);
    
    const total = Object.values(stats).reduce((sum, count) => sum + count, 0);
    console.log(`   总计: ${total}`);
    
    return stats;
  } catch (error) {
    console.error('❌ 获取统计信息失败:', error.message);
    return null;
  }
}

// 示例5: 查找最新的视频URL
async function getLatestVideoUrls(limit = 5) {
  console.log(`\n📝 示例5: 获取最新的 ${limit} 个视频URL`);
  
  try {
    const result = await getTaskList({
      status: 'succeed',
      size: limit
    });
    
    if (result.code === 0 && result.data && result.data.tasks) {
      const videoUrls = [];
      
      for (const task of result.data.tasks) {
        const taskDetail = await getTaskDetail(task.task_id);
        
        if (taskDetail.code === 0 && taskDetail.data && 
            taskDetail.data.task_result && taskDetail.data.task_result.videos) {
          
          taskDetail.data.task_result.videos.forEach(video => {
            videoUrls.push({
              taskId: task.task_id,
              url: video.url,
              createdAt: new Date(task.created_at * 1000)
            });
          });
        }
      }
      
      // 按创建时间排序
      videoUrls.sort((a, b) => b.createdAt - a.createdAt);
      
      console.log(`🎬 找到 ${videoUrls.length} 个视频:`);
      videoUrls.forEach((video, index) => {
        console.log(`${index + 1}. [${video.taskId}] ${video.url}`);
        console.log(`   创建时间: ${video.createdAt.toLocaleString()}`);
      });
      
      return videoUrls;
    } else {
      console.log('❌ 没有找到成功的任务');
      return [];
    }
  } catch (error) {
    console.error('❌ 获取视频URL失败:', error.message);
    return [];
  }
}

// 主函数 - 运行所有示例
async function runExamples() {
  console.log('🚀 Kling AI 任务查询示例');
  console.log('='.repeat(50));
  
  try {
    // 运行示例1
    const successTasks = await getRecentSuccessTasks();
    
    // 如果有成功任务，运行示例2
    if (successTasks.length > 0) {
      await getTaskDetails(successTasks[0].task_id);
    }
    
    // 运行示例4
    await getTaskStatistics();
    
    // 运行示例5
    await getLatestVideoUrls(3);
    
    console.log('\n✅ 所有示例运行完成!');
    
  } catch (error) {
    console.error('❌ 示例运行失败:', error.message);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runExamples();
}

module.exports = {
  getRecentSuccessTasks,
  getTaskDetails,
  monitorTaskStatus,
  getTaskStatistics,
  getLatestVideoUrls
};
