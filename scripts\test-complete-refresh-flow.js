#!/usr/bin/env node

/**
 * 测试完整的视频刷新流程
 * 
 * 模拟前端刷新视频按钮的完整逻辑：
 * 1. 通过项目ID和镜头ID查询最新任务
 * 2. 如果任务已完成，直接使用数据库中的视频
 * 3. 如果任务未完成，通过Kling任务ID查询API状态
 * 4. 如果API显示完成，更新数据库并返回视频
 */

async function testCompleteRefreshFlow() {
  console.log('🔄 测试完整的视频刷新流程');
  console.log('='.repeat(50));
  
  // 获取测试数据
  const { PrismaClient } = require('../lib/generated/prisma');
  const prisma = new PrismaClient();
  
  try {
    await prisma.$connect();
    
    // 查找一个有Kling任务ID且状态为succeed的任务
    const completedTask = await prisma.video_generation_tasks.findFirst({
      where: {
        AND: [
          { kling_task_id: { not: null } },
          { project_id: { not: null } },
          { shot_id: { not: null } }
        ]
      },
      include: {
        generated_videos: true
      },
      orderBy: {
        created_at: 'desc'
      }
    });
    
    if (!completedTask) {
      console.log('❌ 没有找到合适的测试任务');
      return;
    }
    
    console.log('📋 测试任务信息:');
    console.log(`   任务ID: ${completedTask.id}`);
    console.log(`   项目ID: ${completedTask.project_id}`);
    console.log(`   镜头ID: ${completedTask.shot_id}`);
    console.log(`   Kling任务ID: ${completedTask.kling_task_id}`);
    console.log(`   当前状态: ${completedTask.status}`);
    console.log(`   现有视频数量: ${completedTask.generated_videos.length}`);
    
    // 模拟前端刷新逻辑
    await simulateRefreshFlow(completedTask.project_id, completedTask.shot_id);
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

async function simulateRefreshFlow(projectId, shotId) {
  console.log('\n🔄 开始模拟前端刷新流程');
  console.log('-'.repeat(40));
  
  try {
    // 步骤1: 通过项目ID和镜头ID查询数据库中的最新任务
    console.log('🗄️ 步骤1: 查询数据库中的最新任务');
    console.log(`   项目ID: ${projectId}`);
    console.log(`   镜头ID: ${shotId}`);
    
    const dbResponse = await fetch(`http://localhost:3001/api/save-video-generation?shotId=${shotId}`);
    
    if (!dbResponse.ok) {
      throw new Error('数据库查询失败');
    }
    
    const dbResult = await dbResponse.json();
    
    if (!dbResult.success || !dbResult.tasks || dbResult.tasks.length === 0) {
      console.log('❌ 未找到相关任务');
      return;
    }
    
    const latestTask = dbResult.tasks[0]; // 最新任务
    console.log(`✅ 找到最新任务: ${latestTask.id}`);
    console.log(`   状态: ${latestTask.status}`);
    console.log(`   Kling任务ID: ${latestTask.kling_task_id || '无'}`);
    console.log(`   现有视频数量: ${latestTask.generated_videos?.length || 0}`);
    
    // 步骤2: 检查是否已完成且有视频
    if (latestTask.status === 'completed' && latestTask.generated_videos && latestTask.generated_videos.length > 0) {
      console.log('✅ 数据库中已有完成的视频，直接使用');
      console.log('   视频列表:');
      latestTask.generated_videos.forEach((video, index) => {
        console.log(`     视频${index + 1}: ${video.video_url}`);
      });
      return;
    }
    
    // 步骤3: 如果任务未完成，通过Kling任务ID查询API状态
    if (!latestTask.kling_task_id) {
      console.log('⚠️ 任务没有Kling任务ID，无法查询API状态');
      return;
    }
    
    console.log('\n🔍 步骤2: 通过Kling任务ID查询API状态');
    console.log(`   Kling任务ID: ${latestTask.kling_task_id}`);
    
    const statusResponse = await fetch('http://localhost:3001/api/check-task-status', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ taskId: latestTask.kling_task_id })
    });
    
    if (!statusResponse.ok) {
      throw new Error('Kling API查询失败');
    }
    
    const statusResult = await statusResponse.json();
    
    if (!statusResult.success || !statusResult.data) {
      throw new Error('Kling API返回无效数据');
    }
    
    const taskStatus = statusResult.data.task_status;
    console.log(`📊 Kling任务状态: ${taskStatus}`);
    
    // 步骤4: 根据API状态处理
    if (taskStatus === 'succeed' && statusResult.data.task_result?.videos) {
      console.log('✅ 视频生成完成，更新数据库');
      
      // 更新数据库
      const updateResponse = await fetch('http://localhost:3001/api/save-video-generation', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          taskId: latestTask.id,
          status: 'completed',
          completedAt: new Date().toISOString(),
          apiResponseData: statusResult.data,
          videos: statusResult.data.task_result.videos.map((video) => ({
            videoUrl: video.url,
            videoDurationSeconds: 5,
            videoFormat: 'mp4',
            storageProvider: 'kling',
            metadata: video
          }))
        })
      });
      
      if (updateResponse.ok) {
        const updateResult = await updateResponse.json();
        console.log('✅ 数据库更新成功');
        console.log(`   保存的视频数量: ${updateResult.videos?.length || 0}`);
        
        // 显示视频信息
        if (statusResult.data.task_result.videos) {
          console.log('   生成的视频:');
          statusResult.data.task_result.videos.forEach((video, index) => {
            console.log(`     视频${index + 1}: ${video.url}`);
          });
        }
      } else {
        console.log('⚠️ 数据库更新失败，但视频已生成');
      }
      
    } else if (taskStatus === 'processing') {
      console.log('⏳ 视频仍在生成中');
      
    } else if (taskStatus === 'failed') {
      console.log('❌ 视频生成失败');
      console.log(`   错误信息: ${statusResult.data.task_status_msg || '未知错误'}`);
      
    } else {
      console.log(`❓ 未知状态: ${taskStatus}`);
    }
    
  } catch (error) {
    console.error('❌ 刷新流程失败:', error.message);
  }
}

// 运行测试
testCompleteRefreshFlow().catch(console.error);
