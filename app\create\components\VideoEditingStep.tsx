"use client"

import { useState, useEffect, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Slider } from "@/components/ui/slider"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { Progress } from "@/components/ui/progress"
import {
  Video,
  Play,
  Pause,
  Volume2,
  VolumeX,
  SkipBack,
  SkipForward,
  Scissors,
  Upload,
  Download,
  Save,
  Eye,
  Palette,
  Clock,
  Music,
  Type,
  Sparkles,
  Settings,
  MoreHorizontal,
  Trash2,
  Copy,
  Move,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  RotateCw,
} from "lucide-react"

interface VideoClip {
  id: string
  name: string
  duration: number
  thumbnail: string
  videoUrl?: string
  startTime: number
  endTime: number
  track: number
  sceneData?: any // 保存场景的完整数据
}

interface AudioClip {
  id: string
  name: string
  duration: number
  startTime: number
  endTime: number
  volume: number
}

interface VideoEditingStepProps {
  projectId?: string
  userId?: string
  scriptData?: any
  shotsWithImages?: any[]
}

export function VideoEditingStep({
  projectId,
  userId,
  scriptData,
  shotsWithImages,
}: VideoEditingStepProps) {
  const [isPlaying, setIsPlaying] = useState(false)
  const [currentTime, setCurrentTime] = useState(0)
  const [duration, setDuration] = useState(100)
  const [volume, setVolume] = useState([80])
  const [isMuted, setIsMuted] = useState(false)
  const [selectedClip, setSelectedClip] = useState<VideoClip | null>(null)
  const [zoomLevel, setZoomLevel] = useState(1)
  const [isSmartMode, setIsSmartMode] = useState(true)

  // 图片序列播放相关状态
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [imagePlaybackTimer, setImagePlaybackTimer] = useState<NodeJS.Timeout | null>(null)
  
  // 视频片段数据
  const [videoClips, setVideoClips] = useState<VideoClip[]>([])
  const [audioClips, setAudioClips] = useState<AudioClip[]>([])

  // 输出设置
  const [outputResolution, setOutputResolution] = useState("1920x1080")
  const [outputFormat, setOutputFormat] = useState("mp4")

  // 智能剪辑：根据场景和图片生成视频片段
  const handleSmartEditing = () => {
    console.log('智能剪辑开始，shotsWithImages:', shotsWithImages)

    if (!shotsWithImages || shotsWithImages.length === 0) {
      console.log('没有场景数据可用于智能剪辑')
      return
    }

    const newVideoClips: VideoClip[] = shotsWithImages.map((shot, index) => {
      // 每个场景默认持续时间为5秒
      const duration = 5
      const startTime = index * duration

      // 获取主要图片作为缩略图
      const primaryImage = shot.images?.find((img: any) => img.isPrimary) || shot.images?.[0]
      const thumbnailUrl = primaryImage?.url || '/placeholder.svg'

      console.log(`处理镜头 ${shot.shotNumber || index + 1}:`, {
        shotNumber: shot.shotNumber,
        hasImages: shot.hasImages,
        imageCount: shot.imageCount,
        primaryImage: primaryImage,
        thumbnailUrl: thumbnailUrl
      })

      return {
        id: `scene-${shot.shotId || shot.shotNumber || index}`,
        name: `镜头 ${shot.shotNumber || index + 1}`,
        duration: duration,
        thumbnail: thumbnailUrl, // 使用场景生成的图片作为缩略图
        videoUrl: shot.videoUrl, // 如果有视频URL
        startTime: startTime,
        endTime: startTime + duration,
        track: 0,
        sceneData: shot // 保存完整的场景数据
      }
    })

    setVideoClips(newVideoClips)
    console.log('智能剪辑完成，生成了', newVideoClips.length, '个视频片段:', newVideoClips)
  }
  const [aiEnhancement, setAiEnhancement] = useState([75])

  const videoRef = useRef<HTMLVideoElement>(null)

  // 初始化视频片段数据
  useEffect(() => {
    console.log('VideoEditingStep useEffect - shotsWithImages:', shotsWithImages)

    if (shotsWithImages && shotsWithImages.length > 0) {
      const clips: VideoClip[] = shotsWithImages.map((shot, index) => {
        // 获取主要图片作为缩略图
        const primaryImage = shot.images?.find((img: any) => img.isPrimary) || shot.images?.[0]
        const thumbnailUrl = primaryImage?.url || '/placeholder.svg'

        console.log(`初始化镜头 ${shot.shotNumber || index + 1}:`, {
          shotNumber: shot.shotNumber,
          hasImages: shot.hasImages,
          imageCount: shot.imageCount,
          thumbnailUrl: thumbnailUrl
        })

        return {
          id: `clip-${shot.shotId || shot.shotNumber || index}`,
          name: `镜头 ${shot.shotNumber || index + 1}`,
          duration: 5, // 默认5秒
          thumbnail: thumbnailUrl,
          videoUrl: shot.videoUrl,
          startTime: index * 5,
          endTime: (index + 1) * 5,
          track: 0,
          sceneData: shot
        }
      })
      setVideoClips(clips)
      setDuration(clips.length * 5)
      console.log('初始化完成，生成了', clips.length, '个视频片段')
    }
  }, [shotsWithImages])

  const handlePlayPause = () => {
    const newIsPlaying = !isPlaying
    setIsPlaying(newIsPlaying)

    if (selectedClip?.videoUrl) {
      // 如果有视频URL，使用视频播放
      if (videoRef.current) {
        if (newIsPlaying) {
          videoRef.current.play()
        } else {
          videoRef.current.pause()
        }
      }
    } else {
      // 如果没有视频URL，播放图片序列
      if (newIsPlaying) {
        startImageSequencePlayback()
      } else {
        stopImageSequencePlayback()
      }
    }
  }

  // 开始图片序列播放
  const startImageSequencePlayback = () => {
    if (!videoClips || videoClips.length === 0) return

    // 清除之前的定时器
    if (imagePlaybackTimer) {
      clearInterval(imagePlaybackTimer)
    }

    // 计算总时长和当前应该显示的图片
    const totalDuration = videoClips.reduce((sum, clip) => sum + clip.duration, 0)
    setDuration(totalDuration)

    const timer = setInterval(() => {
      setCurrentTime(prevTime => {
        const newTime = prevTime + 0.1 // 每100ms更新一次

        // 计算当前应该显示哪个图片
        let accumulatedTime = 0
        let targetImageIndex = 0

        for (let i = 0; i < videoClips.length; i++) {
          if (newTime >= accumulatedTime && newTime < accumulatedTime + videoClips[i].duration) {
            targetImageIndex = i
            break
          }
          accumulatedTime += videoClips[i].duration
        }

        setCurrentImageIndex(targetImageIndex)

        // 如果播放完毕，停止播放
        if (newTime >= totalDuration) {
          setIsPlaying(false)
          stopImageSequencePlayback()
          return 0
        }

        return newTime
      })
    }, 100)

    setImagePlaybackTimer(timer)
  }

  // 停止图片序列播放
  const stopImageSequencePlayback = () => {
    if (imagePlaybackTimer) {
      clearInterval(imagePlaybackTimer)
      setImagePlaybackTimer(null)
    }
  }

  // 跳转到指定场景
  const handleSkipToScene = (direction: 'prev' | 'next') => {
    if (videoClips.length === 0) return

    let targetIndex = currentImageIndex
    if (direction === 'prev') {
      targetIndex = Math.max(0, currentImageIndex - 1)
    } else {
      targetIndex = Math.min(videoClips.length - 1, currentImageIndex + 1)
    }

    // 计算目标场景的开始时间
    let targetTime = 0
    for (let i = 0; i < targetIndex; i++) {
      targetTime += videoClips[i].duration
    }

    setCurrentTime(targetTime)
    setCurrentImageIndex(targetIndex)
  }

  // 重置播放到开始
  const handleResetPlayback = () => {
    setCurrentTime(0)
    setCurrentImageIndex(0)
    setIsPlaying(false)
    stopImageSequencePlayback()
  }

  // 清理定时器
  useEffect(() => {
    return () => {
      if (imagePlaybackTimer) {
        clearInterval(imagePlaybackTimer)
      }
    }
  }, [imagePlaybackTimer])

  const handleTimeUpdate = (newTime: number) => {
    setCurrentTime(newTime)

    if (selectedClip?.videoUrl && videoRef.current) {
      // 如果有视频，更新视频时间
      videoRef.current.currentTime = newTime
    } else {
      // 如果是图片序列，计算应该显示的图片
      let accumulatedTime = 0
      let targetImageIndex = 0

      for (let i = 0; i < videoClips.length; i++) {
        if (newTime >= accumulatedTime && newTime < accumulatedTime + videoClips[i].duration) {
          targetImageIndex = i
          break
        }
        accumulatedTime += videoClips[i].duration
      }

      setCurrentImageIndex(targetImageIndex)
    }
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = Math.floor(seconds % 60)
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  return (
    <>
      <style jsx>{`
        .text-shadow-sm {
          text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        }
      `}</style>
      <div className="h-full flex bg-gradient-to-br from-slate-50/50 to-blue-50/30">
      {/* 左侧媒体库 */}
      <div className="w-80 bg-white/80 backdrop-blur-sm border-r border-slate-200/60 overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center space-x-3 mb-6">
            <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-emerald-500 to-teal-600 flex items-center justify-center">
              <Video className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-lg font-bold text-slate-800">媒体库</h2>
              <p className="text-sm text-slate-600">拖拽素材到时间轴</p>
            </div>
          </div>

          {/* 视频素材 */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-sm font-semibold text-slate-700">视频素材</h3>
              <Badge variant="secondary" className="text-xs">
                {videoClips.length} 个
              </Badge>
            </div>
            <div className="space-y-3">
              {videoClips.map((clip) => (
                <div
                  key={clip.id}
                  className="group relative bg-slate-50 rounded-lg p-3 border border-slate-200 hover:border-emerald-300 hover:bg-emerald-50/50 transition-all cursor-pointer"
                  onClick={() => setSelectedClip(clip)}
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-16 h-12 bg-gradient-to-br from-slate-300 to-slate-400 rounded-md flex items-center justify-center overflow-hidden">
                      {clip.thumbnail ? (
                        <img 
                          src={clip.thumbnail} 
                          alt={clip.name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <Video className="w-4 h-4 text-slate-600" />
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-slate-800 truncate">
                        {clip.name}
                      </p>
                      <p className="text-xs text-slate-500">
                        {formatTime(clip.duration)} | 1920x1080
                      </p>
                    </div>
                  </div>
                  <div className="mt-2 flex space-x-1">
                    <Button 
                      size="sm" 
                      className="h-6 px-2 text-xs bg-emerald-500 hover:bg-emerald-600"
                    >
                      添加到时间轴
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 音频素材 */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-sm font-semibold text-slate-700">音频素材</h3>
              <Button size="sm" variant="outline" className="h-7 px-2 text-xs">
                <Upload className="w-3 h-3 mr-1" />
                上传
              </Button>
            </div>
            <div className="p-4 border-2 border-dashed border-slate-300 rounded-lg text-center">
              <Music className="w-8 h-8 text-slate-400 mx-auto mb-2" />
              <p className="text-xs text-slate-500">
                拖拽音频文件到此处
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* 中央内容区域 */}
      <div className="flex-1 flex flex-col">
        {/* 顶部工具栏 */}
        <div className="bg-white/80 backdrop-blur-sm border-b border-slate-200/60 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <h1 className="text-xl font-bold text-slate-800">智能剪辑</h1>
              <Badge className="bg-gradient-to-r from-emerald-100 to-green-100 text-emerald-800 border-emerald-200">
                AI内容生成项目
              </Badge>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handlePlayPause}
                disabled={videoClips.length === 0}
              >
                <Eye className="w-4 h-4 mr-1" />
                {isPlaying ? '暂停预览' : '开始预览'}
              </Button>
              <Button
                size="sm"
                className="bg-emerald-500 hover:bg-emerald-600"
                onClick={handleSmartEditing}
              >
                <Sparkles className="w-4 h-4 mr-1" />
                智能剪辑
              </Button>
            </div>
          </div>
        </div>

        {/* 预览区域 */}
        <div className="flex-1 p-6 pb-0">
          <Card className="h-full">
            <CardContent className="p-6 h-full flex flex-col">
              <div className="flex-1 bg-black rounded-lg flex items-center justify-center mb-4 relative overflow-hidden">
                {selectedClip?.videoUrl ? (
                  // 视频播放
                  <video
                    ref={videoRef}
                    src={selectedClip.videoUrl}
                    className="w-full h-full object-contain"
                    onTimeUpdate={(e) => setCurrentTime(e.currentTarget.currentTime)}
                    onLoadedMetadata={(e) => setDuration(e.currentTarget.duration)}
                  />
                ) : videoClips.length > 0 ? (
                  // 图片序列播放
                  <div className="w-full h-full relative flex items-center justify-center">
                    {videoClips[currentImageIndex]?.thumbnail && videoClips[currentImageIndex].thumbnail !== '/placeholder.svg' ? (
                      <>
                        <img
                          src={videoClips[currentImageIndex].thumbnail}
                          alt={videoClips[currentImageIndex].name}
                          className="max-w-full max-h-full object-contain"
                        />
                        {/* 场景信息覆盖层 */}
                        <div className="absolute bottom-4 left-4 bg-black/70 text-white px-4 py-2 rounded-lg backdrop-blur-sm">
                          <div className="text-sm font-medium">{videoClips[currentImageIndex].name}</div>
                          <div className="text-xs opacity-80">
                            {videoClips[currentImageIndex].sceneData?.location} - {videoClips[currentImageIndex].sceneData?.action}
                          </div>
                        </div>
                        {/* 播放进度指示器 */}
                        <div className="absolute top-4 right-4 bg-black/70 text-white px-3 py-1 rounded-full text-xs font-mono">
                          {Math.floor(currentTime)}s / {Math.floor(duration)}s
                        </div>
                      </>
                    ) : (
                      <div className="text-white text-center">
                        <div className="text-4xl mb-4">📷</div>
                        <p className="text-lg mb-2">{videoClips[currentImageIndex]?.name || '场景预览'}</p>
                        <p className="text-sm opacity-70">暂无图片</p>
                      </div>
                    )}
                  </div>
                ) : (
                  // 默认状态
                  <div className="text-white text-center">
                    <div className="text-6xl mb-4">🎬</div>
                    <p className="text-xl mb-2">视频预览区域</p>
                    <p className="text-sm opacity-70">点击"智能剪辑"开始预览</p>
                  </div>
                )}
              </div>

              {/* 播放控制 */}
              <div className="flex items-center space-x-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handlePlayPause}
                  className="w-10 h-10 p-0"
                >
                  {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  className="w-10 h-10 p-0"
                  onClick={handleResetPlayback}
                  disabled={videoClips.length === 0}
                  title="重置到开始"
                >
                  <RotateCcw className="w-4 h-4" />
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  className="w-10 h-10 p-0"
                  onClick={() => handleSkipToScene('prev')}
                  disabled={videoClips.length === 0}
                >
                  <SkipBack className="w-4 h-4" />
                </Button>

                <Button
                  variant="outline"
                  size="sm"
                  className="w-10 h-10 p-0"
                  onClick={() => handleSkipToScene('next')}
                  disabled={videoClips.length === 0}
                >
                  <SkipForward className="w-4 h-4" />
                </Button>

                <div className="flex-1 flex items-center space-x-2">
                  <span className="text-sm text-slate-600 min-w-[40px]">
                    {formatTime(currentTime)}
                  </span>
                  <div
                    className="flex-1 bg-slate-200 rounded-full h-2 relative cursor-pointer"
                    onClick={(e) => {
                      const rect = e.currentTarget.getBoundingClientRect()
                      const clickX = e.clientX - rect.left
                      const percentage = clickX / rect.width
                      const newTime = Math.max(0, Math.min(duration, percentage * duration))
                      handleTimeUpdate(newTime)
                    }}
                  >
                    <div
                      className="bg-emerald-500 rounded-full h-2 transition-all"
                      style={{ width: `${(currentTime / duration) * 100}%` }}
                    />
                    <input
                      type="range"
                      min="0"
                      max={duration}
                      value={currentTime}
                      onChange={(e) => handleTimeUpdate(Number(e.target.value))}
                      className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    />
                  </div>
                  <span className="text-sm text-slate-600 min-w-[40px]">
                    {formatTime(duration)}
                  </span>
                </div>

                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsMuted(!isMuted)}
                  className="w-10 h-10 p-0"
                >
                  {isMuted ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 时间轴编辑器 */}
        <div className="p-6 pt-0">
          <Card>
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">时间轴编辑器</CardTitle>
                <div className="flex items-center space-x-4">
                  <div className="flex items-center space-x-2">
                    <Switch
                      checked={isSmartMode}
                      onCheckedChange={setIsSmartMode}
                      className="data-[state=checked]:bg-emerald-500"
                    />
                    <Label className="text-sm text-slate-600">智能模式</Label>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Button variant="outline" size="sm" className="w-8 h-8 p-0">
                      <ZoomOut className="w-3 h-3" />
                    </Button>
                    <Button variant="outline" size="sm" className="w-8 h-8 p-0">
                      <ZoomIn className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-3">
                {/* 视频轨道 */}
                <div className="flex items-center space-x-4">
                  <div className="w-20 text-sm font-medium text-slate-700">视频轨道</div>
                  <div className="flex-1 h-16 bg-slate-50 rounded-lg border-2 border-slate-200 flex items-center px-3 gap-1 overflow-x-auto relative">
                    {videoClips.map((clip, index) => (
                      <div
                        key={clip.id}
                        className="h-12 bg-white border-2 border-emerald-300 rounded-md flex-shrink-0 flex items-center justify-center text-emerald-700 text-xs font-medium cursor-pointer hover:border-emerald-400 hover:shadow-md transition-all relative group overflow-hidden"
                        style={{ width: `${Math.max(clip.duration * 20, 80)}px` }}
                        onClick={() => {
                          setSelectedClip(clip)
                          // 跳转到该片段的开始时间
                          handleTimeUpdate(clip.startTime)
                        }}
                      >
                        {clip.thumbnail && clip.thumbnail !== '/placeholder.svg' ? (
                          <>
                            <img
                              src={clip.thumbnail}
                              alt={clip.name}
                              className="absolute inset-0 w-full h-full object-cover rounded-sm"
                            />
                            <div className="absolute inset-0 bg-black/20 rounded-sm"></div>
                            <span className="relative z-10 truncate px-1 text-white text-shadow-sm font-semibold">
                              {clip.name}
                            </span>
                          </>
                        ) : (
                          <>
                            <div className="absolute inset-0 bg-gradient-to-r from-emerald-100 to-emerald-200 rounded-sm"></div>
                            <span className="relative z-10 truncate px-1 text-emerald-700">
                              {clip.name}
                            </span>
                          </>
                        )}
                        <div className="absolute -top-1 -right-1 opacity-0 group-hover:opacity-100 transition-opacity">
                          <Button size="sm" variant="destructive" className="w-5 h-5 p-0">
                            <Trash2 className="w-2 h-2" />
                          </Button>
                        </div>
                      </div>
                    ))}

                    {/* 播放进度指示器 */}
                    {videoClips.length > 0 && (
                      <div
                        className="absolute top-0 bottom-0 w-0.5 bg-red-500 z-10 transition-all duration-100"
                        style={{
                          left: `${12 + (currentTime / duration) * (videoClips.reduce((sum, clip) => sum + Math.max(clip.duration * 20, 80), 0))}px`
                        }}
                      >
                        <div className="absolute -top-1 -left-1 w-3 h-3 bg-red-500 rounded-full"></div>
                      </div>
                    )}
                  </div>
                </div>

                {/* 音频轨道 */}
                <div className="flex items-center space-x-4">
                  <div className="w-20 text-sm font-medium text-slate-700">音频轨道</div>
                  <div className="flex-1 h-16 bg-slate-50 rounded-lg border-2 border-slate-200 flex items-center px-3 gap-1">
                    {audioClips.length > 0 ? (
                      audioClips.map((audio, index) => (
                        <div
                          key={audio.id}
                          className="h-12 bg-gradient-to-r from-blue-400 to-blue-500 rounded-md flex-shrink-0 flex items-center justify-center text-white text-xs font-medium cursor-pointer hover:from-blue-500 hover:to-blue-600 transition-all"
                          style={{ width: `${audio.duration * 20}px` }}
                        >
                          {audio.name}
                        </div>
                      ))
                    ) : (
                      <div className="flex-1 flex items-center justify-center text-slate-400 text-sm">
                        拖拽音频文件到此处
                      </div>
                    )}
                  </div>
                </div>

                {/* 字幕轨道 */}
                <div className="flex items-center space-x-4">
                  <div className="w-20 text-sm font-medium text-slate-700">字幕轨道</div>
                  <div className="flex-1 h-16 bg-slate-50 rounded-lg border-2 border-slate-200 flex items-center px-3 gap-1">
                    <div className="flex-1 flex items-center justify-center text-slate-400 text-sm">
                      点击添加字幕
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* 右侧属性面板 */}
      <div className="w-80 bg-white/80 backdrop-blur-sm border-l border-slate-200/60 overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center space-x-3 mb-6">
            <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
              <Settings className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-lg font-bold text-slate-800">属性面板</h2>
              <p className="text-sm text-slate-600">编辑参数设置</p>
            </div>
          </div>

          {/* 视频设置 */}
          <div className="mb-8">
            <h3 className="text-sm font-semibold text-slate-700 mb-4">视频设置</h3>
            <div className="space-y-4">
              <div>
                <Label className="text-xs text-slate-600 mb-2 block">项目标题</Label>
                <input
                  type="text"
                  defaultValue="AI生成内容"
                  className="w-full px-3 py-2 text-sm border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent"
                />
              </div>
              <div>
                <Label className="text-xs text-slate-600 mb-2 block">描述</Label>
                <textarea
                  placeholder="输入项目描述..."
                  rows={3}
                  className="w-full px-3 py-2 text-sm border border-slate-200 rounded-md focus:outline-none focus:ring-2 focus:ring-emerald-500 focus:border-transparent resize-none"
                />
              </div>
            </div>
          </div>

          {/* AI参数 */}
          <div className="mb-8">
            <h3 className="text-sm font-semibold text-slate-700 mb-4">AI参数</h3>
            <div className="space-y-4">
              <div>
                <div className="flex items-center justify-between mb-2">
                  <Label className="text-xs text-slate-600">创意度</Label>
                  <span className="text-xs text-slate-500">{aiEnhancement[0]}%</span>
                </div>
                <Slider
                  value={aiEnhancement}
                  onValueChange={setAiEnhancement}
                  max={100}
                  step={1}
                  className="w-full"
                />
              </div>
              <div className="flex items-center justify-between">
                <Label className="text-xs text-slate-600">智能模式</Label>
                <Switch
                  checked={isSmartMode}
                  onCheckedChange={setIsSmartMode}
                />
              </div>
            </div>
          </div>

          {/* 输出设置 */}
          <div className="mb-8">
            <h3 className="text-sm font-semibold text-slate-700 mb-4">输出设置</h3>
            <div className="space-y-4">
              <div>
                <Label className="text-xs text-slate-600 mb-2 block">分辨率</Label>
                <Select value={outputResolution} onValueChange={setOutputResolution}>
                  <SelectTrigger className="w-full h-9 text-sm">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1920x1080">1920x1080 (HD)</SelectItem>
                    <SelectItem value="3840x2160">3840x2160 (4K)</SelectItem>
                    <SelectItem value="1280x720">1280x720 (720p)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label className="text-xs text-slate-600 mb-2 block">格式</Label>
                <Select value={outputFormat} onValueChange={setOutputFormat}>
                  <SelectTrigger className="w-full h-9 text-sm">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="mp4">MP4</SelectItem>
                    <SelectItem value="mov">MOV</SelectItem>
                    <SelectItem value="avi">AVI</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="space-y-3">
            <Button className="w-full bg-emerald-500 hover:bg-emerald-600">
              <Sparkles className="w-4 h-4 mr-2" />
              应用AI增强
            </Button>
            <Button variant="outline" className="w-full">
              <Save className="w-4 h-4 mr-2" />
              保存项目
            </Button>
            <Button variant="outline" className="w-full">
              <Download className="w-4 h-4 mr-2" />
              导出视频
            </Button>
          </div>
        </div>
      </div>
      </div>
    </>
  )
}
