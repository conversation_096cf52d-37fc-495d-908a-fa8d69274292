import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET /api/scripts - 获取用户的所有剧本
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')

    if (!userId) {
      return NextResponse.json({
        success: false,
        error: '用户ID不能为空'
      }, { status: 400 })
    }

    const scripts = await prisma.script_projects.findMany({
      where: {
        user_id: userId
      },
      include: {
        script_characters: true,
        script_shots: {
          orderBy: {
            shot_number: 'asc'
          }
        },
        script_generation_configs: true,
        image_generation_tasks: {
          include: {
            generated_images: {
              where: {
                is_primary: true
              },
              take: 1
            }
          },
          orderBy: {
            created_at: 'desc'
          }
        }
      },
      orderBy: {
        created_at: 'desc'
      }
    })

    // 转换数据格式以匹配gallery页面的Project接口
    const projects = scripts.map(script => {
      // 获取主要图片作为缩略图
      const primaryImage = script.image_generation_tasks
        .flatMap(task => task.generated_images)
        .find(img => img.is_primary) || script.image_generation_tasks
        .flatMap(task => task.generated_images)[0]

      // 计算当前步骤（基于项目状态和数据完整性）
      let currentStep = 1
      if (script.script_content) currentStep = 2
      if (script.image_generation_tasks.length > 0) currentStep = 3
      if (script.status === 'completed') currentStep = 7

      return {
        id: script.id,
        title: script.title,
        description: script.description || '',
        thumbnail: primaryImage?.cdn_url || primaryImage?.image_url || '/placeholder.svg?height=200&width=300',
        status: script.status === 'completed' ? 'completed' as const :
                script.status === 'draft' ? 'draft' as const : 'in-progress' as const,
        createdAt: script.created_at?.toISOString().split('T')[0] || '',
        updatedAt: script.updated_at?.toISOString().split('T')[0] || '',
        duration: formatDuration(script.duration),
        scenes: script.script_shots.length,
        currentStep,
        totalSteps: 7
      }
    })

    return NextResponse.json({
      success: true,
      scripts,
      projects // 添加转换后的项目数据
    })

  } catch (error) {
    console.error('Error fetching user scripts:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '获取用户脚本时发生未知错误'
    }, { status: 500 })
  }
}

// 辅助函数：将秒数转换为分:秒格式
function formatDuration(seconds: number): string {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}