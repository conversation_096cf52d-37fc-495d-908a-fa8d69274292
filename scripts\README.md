# Kling AI 任务查询测试脚本

这个脚本用于测试 Kling AI 的任务查询功能，包括查询任务列表和单个任务详情。

## 功能特性

- 🔍 **查询任务列表** - 获取所有视频生成任务
- 📋 **查询任务详情** - 获取特定任务的详细信息
- 🎯 **状态过滤** - 按任务状态筛选任务
- 📊 **格式化显示** - 友好的任务信息展示
- ⏰ **时间范围** - 支持按时间范围查询

## 环境配置

脚本会自动读取项目根目录下的环境变量文件，支持以下方式：

### 方式1：使用 .env.local 文件 (推荐)

在项目根目录创建或编辑 `.env.local` 文件：

```env
KLING_ACCESS_KEY=your_kling_access_key
KLING_SECRET_KEY=your_kling_secret_key
```

### 方式2：使用 .env 文件

在项目根目录创建 `.env` 文件：

```env
KLING_ACCESS_KEY=your_kling_access_key
KLING_SECRET_KEY=your_kling_secret_key
```

### 方式3：系统环境变量

```bash
export KLING_ACCESS_KEY="your_kling_access_key"
export KLING_SECRET_KEY="your_kling_secret_key"
```

**注意**：脚本会按以下优先级加载环境变量：
1. 系统环境变量（最高优先级）
2. `.env.local` 文件
3. `.env` 文件（最低优先级）

## 使用方法

### 1. 基础测试脚本

```bash
# 运行完整测试（使用新API格式）
npm run test:kling

# 测试新API格式
npm run test:new-api

# 或者直接运行
node scripts/test-kling-tasks.js
node scripts/test-new-api-format.js
```

### 2. 单任务查询 (新增)

```bash
# 查询特定任务详情
npm run kling:query task_12345

# 使用外部任务ID查询
npm run kling:query -- --external-id my_custom_id_123

# 显示详细信息
npm run kling:query task_12345 -- --verbose

# 生成对应的curl命令
npm run kling:query task_12345 -- --curl

# 或者直接运行
node scripts/query-single-task.js task_12345
node scripts/query-single-task.js --external-id my_custom_id_123
```

### 3. 任务管理器

```bash
# 查询任务列表
npm run kling:list

# 交互式模式
npm run kling:interactive

# 查询成功任务
npm run kling:succeed

# 查询失败任务
npm run kling:failed

# 或者使用命令行参数
node scripts/kling-task-manager.js --list --status succeed --size 5
node scripts/kling-task-manager.js --detail task_12345
```

### 4. 示例脚本

```bash
# 运行所有示例
node scripts/example-usage.js
```

### 2. 在代码中使用

```javascript
const { getTaskList, getTaskDetail } = require('./scripts/test-kling-tasks.js');

// 查询任务列表
const tasks = await getTaskList({
  status: 'succeed',  // 可选：筛选状态
  page: 1,           // 可选：页码
  size: 10,          // 可选：每页数量
  start_time: '2024-01-01T00:00:00Z',  // 可选：开始时间
  end_time: '2024-12-31T23:59:59Z'     // 可选：结束时间
});

// 查询单个任务详情
const taskDetail = await getTaskDetail('task_id_here');
```

## API 参数说明

### 查询任务列表参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| `status` | string | 否 | 任务状态：`submitted`, `processing`, `succeed`, `failed` |
| `page` | number | 否 | 页码，默认为 1（对应API的pageNum参数） |
| `size` | number | 否 | 每页数量，默认为 20，最大 100（对应API的pageSize参数） |
| `start_time` | string | 否 | 开始时间，ISO 8601 格式 |
| `end_time` | string | 否 | 结束时间，ISO 8601 格式 |

### 任务状态说明

- `submitted` - 已提交
- `processing` - 处理中
- `succeed` - 成功
- `failed` - 失败

## 输出示例

```
🚀 [KLING-TEST] 开始测试 Kling AI 任务查询功能
🚀 [KLING-TEST] ==========================================

📝 [TEST-1] 查询最近的任务列表 (最多10个)
🔍 [KLING-TEST] 查询任务列表...
🔍 [KLING-TEST] URL: https://api-beijing.klingai.com/v1/videos/image2video?pageSize=10&pageNum=1
📡 [KLING-TEST] HTTP状态: 200 OK
✅ [KLING-TEST] 查询成功

📊 [RESULT] 任务列表结果:
📊 [RESULT] 返回码: 0
📊 [RESULT] 任务总数: 25
📊 [RESULT] 当前页任务数: 10

📋 [TASK-1] ID: task_12345
📋 [TASK-1] 状态: succeed
📋 [TASK-1] 创建时间: 2024-01-15 14:30:25

📝 [TEST-2] 查询任务详情: task_12345
🔍 [KLING-TEST] 查询任务详情...
📡 [KLING-TEST] HTTP状态: 200 OK
✅ [KLING-TEST] 查询成功

📋 [TASK-INFO] ==========================================
📋 [TASK-INFO] 任务ID: task_12345
📋 [TASK-INFO] 状态: succeed
📋 [TASK-INFO] 创建时间: 2024-01-15 14:30:25
📋 [TASK-INFO] 更新时间: 2024-01-15 14:32:10
📋 [TASK-INFO] 生成视频数量: 1
📋 [TASK-INFO] 视频 1: https://kling-video-url.com/video.mp4
📋 [TASK-INFO] ==========================================

✅ [KLING-TEST] 测试完成!
```

## 错误处理

脚本包含完整的错误处理：

- ❌ 环境变量未设置
- ❌ JWT Token 生成失败
- ❌ HTTP 请求失败
- ❌ API 返回错误

## 注意事项

1. **API 限制**：请注意 Kling AI 的 API 调用频率限制
2. **Token 有效期**：JWT Token 有效期为 30 分钟
3. **时区**：时间显示使用本地时区
4. **网络**：确保网络可以访问 `api-beijing.klingai.com`

## 脚本文件说明

### `test-kling-tasks.js`
基础测试脚本，包含核心的API调用功能：
- `getTaskList()` - 查询任务列表
- `getTaskDetail()` - 查询任务详情
- `displayTaskInfo()` - 格式化显示任务信息

### `query-single-task.js`
专门的单任务查询工具，支持多种查询方式：
- 支持 task_id 和 external_task_id 查询
- 详细的任务信息显示
- 生成对应的 curl 命令
- 完整的错误处理和建议

### `kling-task-manager.js`
高级任务管理器，提供命令行界面和交互式模式：
- 支持命令行参数
- 交互式菜单操作
- 任务状态统计
- 友好的输出格式

### `example-usage.js`
实用示例脚本，展示常见使用场景：
- 查询最近成功任务
- 监控任务状态变化
- 获取任务统计信息
- 提取最新视频URL

## 🎬 视频生成任务状态查询功能

### 功能说明
在图生视频步骤中，当点击某个镜头后，如果该镜头已经生成过但还没有视频输出，系统会自动查询Kling AI的任务状态，检查视频是否生成完毕。

### 实现特性
- ✅ **自动状态检查**: 点击场景卡片时自动查询任务状态
- ✅ **状态显示**: 实时显示任务状态（已提交、处理中、成功、失败）
- ✅ **手动刷新**: 提供多种刷新按钮和方式
- ✅ **批量刷新**: 支持批量刷新所有待处理视频
- ✅ **防重复查询**: 30秒内不重复查询同一任务
- ✅ **视频自动更新**: 任务完成后自动更新视频URL
- ✅ **智能状态图标**: 视觉化显示视频生成状态

### 使用方法

#### **基础使用**
1. 在视频生成步骤中生成视频
2. 如果视频是异步生成，会显示任务ID和状态
3. 系统会自动跟踪任务状态并显示相应图标

#### **刷新功能**
- **自动刷新**: 点击场景卡片时自动检查状态（30秒防重复）
- **单个刷新**: 点击场景卡片上的"刷新"按钮
- **当前场景刷新**: 在右侧操作区点击"刷新状态"按钮
- **批量刷新**: 点击"刷新所有待处理视频"按钮

#### **状态指示**
- 🟢 **绿色图标**: 视频已生成完成
- 🟡 **黄色旋转图标**: 正在生成中
- 🔴 **红色图标**: 生成失败
- 🔵 **蓝色图标**: 未开始或等待中

### 测试页面
- **`/test-task-status`**: 测试任务状态查询API功能
- **`/test-video-refresh`**: 测试视频刷新功能的完整演示
- **`/test-video-settings`**: 测试新的视频生成设置布局

## 🎨 视频生成设置布局优化

### 改进说明
将原来分离的"生成设置"页面整合到"视频提示词编辑"区域，并重命名为"视频生成设置"，提供更统一和便捷的用户体验。

### 主要变化
- ✅ **整合布局**: 将生成设置移入右侧设置面板
- 🏷️ **重命名**: "视频提示词编辑" → "视频生成设置"
- 📱 **紧凑设计**: 使用网格布局，节省空间
- 🎯 **集中管理**: 所有视频相关设置集中在一个区域
- ⚡ **批量操作**: 保留批量生成和刷新功能

### 设置项目
- **运动描述**: 视频提示词编辑区域
- **基础设置**: 视频比例、质量、帧率、转场效果
- **动画控制**: 动画强度、运动幅度滑块
- **其他选项**: 随机种子、视频时长显示

## 快速开始

1. **设置环境变量**：
   ```bash
   export KLING_ACCESS_KEY="your_key"
   export KLING_SECRET_KEY="your_secret"
   ```

2. **运行交互式管理器**：
   ```bash
   npm run kling:interactive
   ```

3. **查看最新成功任务**：
   ```bash
   npm run kling:succeed
   ```

## API 格式更新说明

### 🔄 **新旧格式对比**

根据最新的 Kling AI API 文档，查询格式已更新：

#### **旧格式** ❌
```
GET /v1/videos/image2video/tasks?page=1&size=10
GET /v1/videos/image2video/tasks/{taskId}
```

#### **新格式** ✅
```
GET /v1/videos/image2video?pageNum=1&pageSize=30
GET /v1/videos/image2video/{taskId}
```

### 📋 **主要变化**

1. **参数名称变化**：
   - `page` → `pageNum`
   - `size` → `pageSize`

2. **URL路径变化**：
   - 移除了 `/tasks` 路径段
   - 任务列表查询直接使用 `/v1/videos/image2video`（注意：末尾无斜杠）

3. **默认参数调整**：
   - 默认 `pageSize` 建议使用 30 而不是 10

### 🧪 **测试新格式**

```bash
# 测试新API格式
npm run test:new-api

# 对比测试
npm run test:kling  # 使用新格式的基础测试
```

## 相关文档

- [Kling AI API 文档](https://app.klingai.com/cn/dev/document-api/apiReference/model/imageToVideo)
- [JWT 认证说明](https://app.klingai.com/cn/dev/document-api/authentication)
