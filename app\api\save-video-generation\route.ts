import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'
import {
  createVideoGenerationTask,
  updateVideoGenerationTask,
  createGeneratedVideo,
  getVideoGenerationTasksByProject,
  getVideoGenerationTasksByShot,
  getVideoGenerationTasksByProjectShotUser,
  getVideoGenerationTaskByKlingId
} from '@/lib/prisma-video-operations'

// 保存视频生成任务的数据验证
const saveVideoTaskSchema = z.object({
  projectId: z.string().uuid().optional(),
  shotId: z.string().uuid().optional(),
  userId: z.string().uuid().optional(),
  taskName: z.string().optional(),
  prompt: z.string().min(1, "Prompt is required"),
  negativePrompt: z.string().optional(),
  modelName: z.string().default('kling-v1'),
  aspectRatio: z.string().default('16:9'),
  duration: z.number().min(1).max(10).default(5),
  imageReference: z.string().default('subject'),
  imageFidelity: z.number().min(0).max(1).default(0.5),
  baseImageUrl: z.string().url().optional(),
  klingTaskId: z.string().optional(),
  apiRequestPayload: z.any().optional(),
  apiResponseData: z.any().optional(),
})

// 更新视频生成任务的数据验证
const updateVideoTaskSchema = z.object({
  status: z.enum(['pending', 'queued', 'processing', 'completed', 'failed', 'cancelled']).optional(),
  klingTaskId: z.string().optional(),
  apiResponseData: z.any().optional(),
  errorMessage: z.string().optional(),
  errorCode: z.string().optional(),
  startedAt: z.string().optional(),
  completedAt: z.string().optional(),
  videos: z.array(z.object({
    videoUrl: z.string().url(),
    videoFilename: z.string().optional(),
    videoSizeBytes: z.number().optional(),
    videoWidth: z.number().optional(),
    videoHeight: z.number().optional(),
    videoFormat: z.string().optional(),
    videoDuration: z.number().optional(),
    qualityScore: z.number().optional(),
    storageProvider: z.string().optional(),
    storagePath: z.string().optional(),
    cdnUrl: z.string().optional(),
    generationTimeSeconds: z.number().optional(),
    metadata: z.any().optional(),
  })).optional(),
})

// POST /api/save-video-generation - 保存视频生成任务
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // 验证请求数据
    const validatedData = saveVideoTaskSchema.parse(body)
    
    console.log('🎬 [API] Saving video generation task:', {
      projectId: validatedData.projectId,
      userId: validatedData.userId,
      prompt: validatedData.prompt.substring(0, 100) + '...',
      duration: validatedData.duration
    })
    
    // 创建视频生成任务记录
    const task = await createVideoGenerationTask(validatedData)
    
    console.log('✅ [API] Video generation task saved:', task.id)
    
    return NextResponse.json({
      success: true,
      taskId: task.id,
      task
    })
    
  } catch (error) {
    console.error('❌ [API] Failed to save video generation task:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid request data',
          details: error.errors
        },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// PUT /api/save-video-generation - 更新视频生成任务
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    const { taskId, ...updateData } = body
    
    if (!taskId) {
      return NextResponse.json(
        { 
          success: false,
          error: 'taskId is required' 
        },
        { status: 400 }
      )
    }
    
    // 验证更新数据
    const validatedData = updateVideoTaskSchema.parse(updateData)
    const { videos, ...taskUpdateData } = validatedData
    
    console.log('🎬 [API] Updating video generation task:', taskId)
    
    // 更新任务状态
    const updatedTask = await updateVideoGenerationTask(taskId, {
      ...taskUpdateData,
      startedAt: taskUpdateData.startedAt ? new Date(taskUpdateData.startedAt) : undefined,
      completedAt: taskUpdateData.completedAt ? new Date(taskUpdateData.completedAt) : undefined,
    })
    
    // 保存生成的视频记录
    const savedVideos = []
    if (videos && videos.length > 0) {
      for (const videoData of videos) {
        const savedVideo = await createGeneratedVideo({
          ...videoData,
          taskId,
        })
        savedVideos.push(savedVideo)
      }
    }
    
    console.log('✅ [API] Video generation task updated:', {
      taskId,
      status: updatedTask.status,
      videosCount: savedVideos.length
    })
    
    return NextResponse.json({
      success: true,
      task: updatedTask,
      videos: savedVideos
    })
    
  } catch (error) {
    console.error('❌ [API] Failed to update video generation task:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid request data',
          details: error.errors
        },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// 验证UUID格式的辅助函数
function isValidUUID(uuid: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  return uuidRegex.test(uuid)
}

// GET /api/save-video-generation - 获取视频生成任务列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const projectId = searchParams.get('projectId')
    const shotId = searchParams.get('shotId')
    const userId = searchParams.get('userId')
    const klingTaskId = searchParams.get('klingTaskId')

    console.log('🔍 [API] GET video generation tasks:', { projectId, shotId, userId, klingTaskId })

    // 验证UUID格式
    if (projectId && !isValidUUID(projectId)) {
      return NextResponse.json({
        success: false,
        error: `Invalid projectId format: ${projectId}. Expected UUID format.`
      }, { status: 400 })
    }

    if (shotId && !isValidUUID(shotId)) {
      return NextResponse.json({
        success: false,
        error: `Invalid shotId format: ${shotId}. Expected UUID format.`
      }, { status: 400 })
    }

    if (userId && !isValidUUID(userId)) {
      return NextResponse.json({
        success: false,
        error: `Invalid userId format: ${userId}. Expected UUID format.`
      }, { status: 400 })
    }

    if (klingTaskId) {
      // 根据 Kling Task ID 查询单个任务
      const task = await getVideoGenerationTaskByKlingId(klingTaskId)

      if (!task) {
        return NextResponse.json({
          success: false,
          error: 'Task not found'
        }, { status: 404 })
      }

      return NextResponse.json({
        success: true,
        task
      })
    }

    // 组合查询：项目ID + 镜头ID + 用户ID
    if (projectId && shotId && userId) {
      console.log('🔍 [API] 执行组合查询: 项目ID + 镜头ID + 用户ID')
      const tasks = await getVideoGenerationTasksByProjectShotUser(projectId, shotId, userId)

      return NextResponse.json({
        success: true,
        tasks
      })
    }

    if (shotId) {
      // 根据镜头ID查询任务列表
      const tasks = await getVideoGenerationTasksByShot(shotId)

      return NextResponse.json({
        success: true,
        tasks
      })
    }

    if (projectId) {
      // 根据项目ID查询任务列表
      const tasks = await getVideoGenerationTasksByProject(projectId)

      return NextResponse.json({
        success: true,
        tasks
      })
    }

    return NextResponse.json(
      {
        success: false,
        error: 'projectId, shotId, userId combination or klingTaskId is required'
      },
      { status: 400 }
    )

  } catch (error) {
    console.error('❌ [API] Failed to get video generation tasks:', error)

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
