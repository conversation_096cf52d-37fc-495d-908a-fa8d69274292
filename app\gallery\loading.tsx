export default function Loading() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/50">
      {/* 顶部导航骨架 */}
      <header className="bg-white/80 backdrop-blur-xl border-b border-slate-200/60 shadow-sm">
        <div className="container mx-auto px-6 py-5">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-2">
                <div className="w-5 h-5 bg-slate-200 rounded animate-pulse" />
                <div className="h-5 w-20 bg-slate-200 rounded animate-pulse" />
              </div>
              <div className="w-px h-6 bg-slate-300"></div>
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-slate-200 to-gray-300 animate-pulse" />
                <div>
                  <div className="h-6 w-32 bg-slate-200 rounded animate-pulse mb-1" />
                  <div className="h-3 w-24 bg-slate-200 rounded animate-pulse" />
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <div className="h-9 w-24 bg-slate-200 rounded-lg animate-pulse" />
              <div className="h-9 w-28 bg-gradient-to-r from-slate-200 to-gray-300 rounded-lg animate-pulse" />
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-6 py-8">
        {/* 筛选栏骨架 */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-slate-200/60 p-8 mb-8 shadow-lg">
          <div className="flex flex-col lg:flex-row gap-6 items-start lg:items-center justify-between">
            <div className="flex flex-col sm:flex-row gap-4 flex-1">
              <div className="h-12 w-80 bg-gradient-to-r from-slate-200 to-gray-300 rounded-xl animate-pulse" />
              <div className="h-12 w-44 bg-gradient-to-r from-slate-200 to-gray-300 rounded-xl animate-pulse" />
              <div className="h-12 w-44 bg-gradient-to-r from-slate-200 to-gray-300 rounded-xl animate-pulse" />
            </div>
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-2 bg-gradient-to-r from-blue-50 to-indigo-50 px-4 py-2 rounded-xl border border-blue-200">
                <div className="w-2 h-2 rounded-full bg-blue-300 animate-pulse"></div>
                <div className="h-4 w-20 bg-blue-200 rounded animate-pulse" />
              </div>
              <div className="flex items-center space-x-2 bg-gradient-to-r from-emerald-50 to-green-50 px-4 py-2 rounded-xl border border-emerald-200">
                <div className="w-2 h-2 rounded-full bg-emerald-300 animate-pulse"></div>
                <div className="h-4 w-20 bg-emerald-200 rounded animate-pulse" />
              </div>
            </div>
          </div>
        </div>

        {/* 项目卡片骨架 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {Array.from({ length: 8 }).map((_, i) => (
            <div key={i} className="bg-white/80 backdrop-blur-sm border-slate-200/60 rounded-xl overflow-hidden shadow-lg">
              {/* 缩略图骨架 */}
              <div className="aspect-video bg-gradient-to-br from-slate-200 to-gray-300 animate-pulse relative">
                <div className="absolute top-4 left-4">
                  <div className="h-6 w-16 bg-white/50 rounded-full animate-pulse" />
                </div>
                <div className="absolute top-4 right-4">
                  <div className="w-8 h-8 bg-white/50 rounded-lg animate-pulse" />
                </div>
              </div>

              {/* 内容骨架 */}
              <div className="p-6 space-y-4">
                <div>
                  <div className="h-5 bg-slate-200 rounded animate-pulse mb-2" />
                  <div className="h-4 bg-slate-200 rounded animate-pulse w-3/4" />
                </div>

                <div className="flex items-center justify-between">
                  <div className="bg-slate-50 px-3 py-1.5 rounded-lg">
                    <div className="h-3 w-16 bg-slate-200 rounded animate-pulse" />
                  </div>
                  <div className="bg-slate-50 px-3 py-1.5 rounded-lg">
                    <div className="h-3 w-12 bg-slate-200 rounded animate-pulse" />
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="bg-blue-50 px-3 py-1.5 rounded-lg">
                    <div className="h-3 w-14 bg-blue-200 rounded animate-pulse" />
                  </div>
                  <div className="bg-purple-50 px-3 py-1.5 rounded-lg">
                    <div className="h-3 w-12 bg-purple-200 rounded animate-pulse" />
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="w-full bg-slate-200 rounded-full h-2 animate-pulse" />
                  <div className="flex justify-between items-center">
                    <div className="h-3 w-16 bg-slate-200 rounded animate-pulse" />
                    <div className="h-4 w-12 bg-blue-100 rounded-full animate-pulse" />
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
