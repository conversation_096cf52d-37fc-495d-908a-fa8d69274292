-- 创建用户表
CREATE TABLE public.users (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  email character varying NOT NULL,
  username character varying,
  avatar_url text,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT users_pkey PRIMARY KEY (id),
  CONSTRAINT users_email_key UNIQUE (email)
);

-- 创建用户偏好设置表
CREATE TABLE public.user_preferences (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid,
  default_style character varying,
  default_duration integer DEFAULT 90,
  favorite_tags text[],
  ui_settings jsonb,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT user_preferences_pkey PRIMARY KEY (id),
  CONSTRAINT user_preferences_user_id_key UNIQUE (user_id),
  CONSTRAINT user_preferences_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id)
);

-- 创建剧本项目表
CREATE TABLE public.script_projects (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  user_id uuid,
  title character varying NOT NULL,
  description text,
  creation_mode character varying NOT NULL,
  style character varying NOT NULL,
  duration integer NOT NULL,
  status character varying DEFAULT 'draft',
  script_content text,
  script_data jsonb,
  created_at timestamp with time zone DEFAULT now(),
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT script_projects_pkey PRIMARY KEY (id),
  CONSTRAINT script_projects_creation_mode_check CHECK (creation_mode IN ('keywords', 'template', 'free')),
  CONSTRAINT script_projects_status_check CHECK (status IN ('draft', 'completed', 'archived')),
  CONSTRAINT script_projects_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id)
);

-- 创建剧本角色表
CREATE TABLE public.script_characters (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  project_id uuid,
  name character varying NOT NULL,
  role character varying NOT NULL,
  background text,
  sort_order integer DEFAULT 0,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT script_characters_pkey PRIMARY KEY (id),
  CONSTRAINT script_characters_role_check CHECK (role IN ('主角', '配角', '反派', '路人')),
  CONSTRAINT script_characters_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.script_projects(id)
);

-- 创建剧本生成配置表
CREATE TABLE public.script_generation_configs (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  project_id uuid,
  theme text,
  selected_tags text[],
  custom_keywords text,
  selected_mood character varying,
  selected_template character varying,
  background_setting text,
  character1 text,
  character2 text,
  supporting_character text,
  required_scenes text,
  avoid_elements text,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT script_generation_configs_pkey PRIMARY KEY (id),
  CONSTRAINT script_generation_configs_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.script_projects(id)
);

-- 创建剧本镜头表
CREATE TABLE public.script_shots (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  project_id uuid,
  shot_number integer NOT NULL,
  duration integer NOT NULL,
  shot_type character varying NOT NULL,
  location text NOT NULL,
  characters text[] NOT NULL,
  action text NOT NULL,
  dialogue text,
  camera_movement character varying NOT NULL,
  lighting character varying NOT NULL,
  props text[],
  mood character varying NOT NULL,
  sound_effect text NOT NULL,
  transition character varying NOT NULL,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT script_shots_pkey PRIMARY KEY (id),
  CONSTRAINT script_shots_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.script_projects(id)
);

-- 创建图像生成任务表
CREATE TABLE public.image_generation_tasks (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  project_id uuid,
  shot_id uuid,
  user_id uuid,
  task_name character varying,
  prompt text NOT NULL,
  negative_prompt text,
  model_name character varying DEFAULT 'kling-v1-5',
  aspect_ratio character varying DEFAULT '16:9',
  image_count integer DEFAULT 1,
  image_fidelity numeric DEFAULT 0.5,
  human_fidelity numeric DEFAULT 0.45,
  cfg_scale numeric DEFAULT 7.5,
  seed bigint,
  reference_image_url text,
  reference_image_strength numeric DEFAULT 0.7,
  status character varying DEFAULT 'pending',
  kling_task_id character varying,
  api_request_payload jsonb,
  api_response_data jsonb,
  error_message text,
  error_code character varying,
  created_at timestamp with time zone DEFAULT now(),
  started_at timestamp with time zone,
  completed_at timestamp with time zone,
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT image_generation_tasks_pkey PRIMARY KEY (id),
  CONSTRAINT image_generation_tasks_kling_task_id_key UNIQUE (kling_task_id),
  CONSTRAINT image_generation_tasks_image_count_check CHECK (image_count >= 1 AND image_count <= 4),
  CONSTRAINT image_generation_tasks_image_fidelity_check CHECK (image_fidelity >= 0 AND image_fidelity <= 1),
  CONSTRAINT image_generation_tasks_human_fidelity_check CHECK (human_fidelity >= 0 AND human_fidelity <= 1),
  CONSTRAINT image_generation_tasks_cfg_scale_check CHECK (cfg_scale >= 1 AND cfg_scale <= 20),
  CONSTRAINT image_generation_tasks_reference_image_strength_check CHECK (reference_image_strength >= 0 AND reference_image_strength <= 1),
  CONSTRAINT image_generation_tasks_status_check CHECK (status IN ('pending', 'queued', 'processing', 'completed', 'failed', 'cancelled')),
  CONSTRAINT image_generation_tasks_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.script_projects(id),
  CONSTRAINT image_generation_tasks_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id),
  CONSTRAINT image_generation_tasks_shot_id_fkey FOREIGN KEY (shot_id) REFERENCES public.script_shots(id)
);

-- 创建生成图像表
CREATE TABLE public.generated_images (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  task_id uuid,
  image_url text NOT NULL,
  image_filename character varying,
  image_size_bytes bigint,
  image_width integer,
  image_height integer,
  image_format character varying DEFAULT 'png',
  quality_score numeric,
  is_primary boolean DEFAULT false,
  storage_provider character varying DEFAULT 'supabase',
  storage_path text,
  cdn_url text,
  generation_time_seconds numeric,
  actual_prompt text,
  metadata jsonb,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT generated_images_pkey PRIMARY KEY (id),
  CONSTRAINT generated_images_task_id_fkey FOREIGN KEY (task_id) REFERENCES public.image_generation_tasks(id)
);

ALTER TABLE generated_images
-- 添加 MinIO 对象名字段（存储 MinIO 中的唯一标识）
ADD COLUMN minio_object_name VARCHAR(255);

-- 添加存储桶名字段（可选，如果使用固定桶名可省略）
ADD COLUMN minio_bucket_name VARCHAR(100) DEFAULT 'images';

-- 修改 storage_provider 默认值
ALTER TABLE generated_images 
ALTER COLUMN storage_provider SET DEFAULT 'minio';

-- 修改 image_url 为可空（不再存储完整 URL）
ALTER TABLE generated_images 
ALTER COLUMN image_url DROP NOT NULL;

-- 新增 CDN 基础路径字段（可选）
ADD COLUMN cdn_base_url VARCHAR(255);


ALTER TABLE image_generation_tasks
ADD COLUMN reference_image_object_name VARCHAR(255);

-- 创建视频生成任务表
CREATE TABLE public.video_generation_tasks (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  project_id uuid,
  shot_id uuid,
  user_id uuid,
  task_name character varying,
  prompt text NOT NULL,
  negative_prompt text,
  model_name character varying DEFAULT 'kling-v1',
  aspect_ratio character varying DEFAULT '16:9',
  duration integer DEFAULT 5,
  image_reference character varying DEFAULT 'subject',
  image_fidelity numeric DEFAULT 0.5,
  base_image_url text,
  status character varying DEFAULT 'pending',
  kling_task_id character varying,
  api_request_payload jsonb,
  api_response_data jsonb,
  error_message text,
  error_code character varying,
  created_at timestamp with time zone DEFAULT now(),
  started_at timestamp with time zone,
  completed_at timestamp with time zone,
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT video_generation_tasks_pkey PRIMARY KEY (id),
  CONSTRAINT video_generation_tasks_kling_task_id_key UNIQUE (kling_task_id),
  CONSTRAINT video_generation_tasks_duration_check CHECK (duration >= 1 AND duration <= 10),
  CONSTRAINT video_generation_tasks_image_fidelity_check CHECK (image_fidelity >= 0 AND image_fidelity <= 1),
  CONSTRAINT video_generation_tasks_status_check CHECK (status IN ('pending', 'queued', 'processing', 'completed', 'failed', 'cancelled')),
  CONSTRAINT video_generation_tasks_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.script_projects(id),
  CONSTRAINT video_generation_tasks_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id),
  CONSTRAINT video_generation_tasks_shot_id_fkey FOREIGN KEY (shot_id) REFERENCES public.script_shots(id)
);

-- 创建生成视频表
CREATE TABLE public.generated_videos (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  task_id uuid,
  video_url text NOT NULL,
  video_filename character varying,
  video_size_bytes bigint,
  video_width integer,
  video_height integer,
  video_format character varying DEFAULT 'mp4',
  video_duration numeric,
  quality_score numeric,
  storage_provider character varying DEFAULT 'kling',
  storage_path text,
  cdn_url text,
  generation_time_seconds numeric,
  metadata jsonb,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT generated_videos_pkey PRIMARY KEY (id),
  CONSTRAINT generated_videos_task_id_fkey FOREIGN KEY (task_id) REFERENCES public.video_generation_tasks(id)
);