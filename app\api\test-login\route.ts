import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import jwt from 'jsonwebtoken'

// POST /api/test-login - 测试登录（自动登录测试用户）
export async function POST(request: NextRequest) {
  try {
    // 查找测试用户
    const testUser = await prisma.users.findUnique({
      where: { email: '<EMAIL>' }
    })

    if (!testUser) {
      return NextResponse.json({
        success: false,
        error: '测试用户不存在，请先创建测试数据'
      }, { status: 404 })
    }

    // 生成JWT token
    const token = jwt.sign(
      { userId: testUser.id, email: testUser.email },
      process.env.JWT_SECRET || 'test-secret',
      { expiresIn: '7d' }
    )

    return NextResponse.json({
      success: true,
      user: {
        id: testUser.id,
        email: testUser.email,
        username: testUser.username,
        avatar_url: testUser.avatar_url
      },
      token
    })

  } catch (error) {
    console.error('Test login error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '测试登录时发生未知错误'
    }, { status: 500 })
  }
}
