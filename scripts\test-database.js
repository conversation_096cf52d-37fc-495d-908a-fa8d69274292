#!/usr/bin/env node

/**
 * 图生视频数据库连接测试脚本
 * 用于验证数据库连接和相关表的状态
 */

// 加载环境变量
require('dotenv').config({ path: '.env.local' });
require('dotenv').config(); // 备用加载 .env 文件

console.log('🔧 图生视频数据库连接测试');
console.log('='.repeat(60));

// 检查数据库相关的环境变量
const dbEnvVars = [
  'DATABASE_URL',
  'NEXT_PUBLIC_SUPABASE_URL',
  'NEXT_PUBLIC_SUPABASE_ANON_KEY'
];

console.log('\n📋 数据库相关环境变量:');
let allDbVarsSet = true;

dbEnvVars.forEach(varName => {
  const value = process.env[varName];
  if (value) {
    // 只显示前几个字符，保护敏感信息
    const maskedValue = value.length > 30 ? 
      `${value.substring(0, 20)}...${value.substring(value.length - 8)}` : 
      value.substring(0, 20) + '...';
    console.log(`✅ ${varName}: ${maskedValue}`);
  } else {
    console.log(`❌ ${varName}: 未设置`);
    allDbVarsSet = false;
  }
});

// 测试数据库连接
async function testDatabaseConnection() {
  if (!allDbVarsSet) {
    console.log('\n❌ 缺少必需的数据库环境变量，无法进行连接测试');
    return;
  }

  console.log('\n🔍 测试数据库连接...');
  
  try {
    // 动态导入 Prisma 客户端
    const { PrismaClient } = require('../lib/generated/prisma');
    const prisma = new PrismaClient();

    console.log('📡 正在连接数据库...');
    
    // 测试基本连接
    await prisma.$connect();
    console.log('✅ 数据库连接成功');

    // 测试图生视频相关表
    console.log('\n🔍 检查图生视频相关表...');
    
    // 1. 检查 script_projects 表
    const projectCount = await prisma.script_projects.count();
    console.log(`📊 script_projects 表: ${projectCount} 条记录`);

    // 2. 检查 script_shots 表
    const shotCount = await prisma.script_shots.count();
    console.log(`📊 script_shots 表: ${shotCount} 条记录`);

    // 3. 检查 image_generation_tasks 表
    const taskCount = await prisma.image_generation_tasks.count();
    console.log(`📊 image_generation_tasks 表: ${taskCount} 条记录`);

    // 4. 检查 generated_images 表
    const imageCount = await prisma.generated_images.count();
    console.log(`📊 generated_images 表: ${imageCount} 条记录`);

    // 5. 检查最近的图像生成任务
    console.log('\n🔍 最近的图像生成任务:');
    const recentTasks = await prisma.image_generation_tasks.findMany({
      take: 5,
      orderBy: { created_at: 'desc' },
      include: {
        generated_images: true,
        script_shots: {
          include: {
            script_projects: true
          }
        }
      }
    });

    if (recentTasks.length === 0) {
      console.log('📝 暂无图像生成任务记录');
    } else {
      recentTasks.forEach((task, index) => {
        console.log(`\n📋 任务 ${index + 1}:`);
        console.log(`   ID: ${task.id}`);
        console.log(`   名称: ${task.task_name || '未命名'}`);
        console.log(`   状态: ${task.status}`);
        console.log(`   项目: ${task.script_shots?.script_projects?.title || '未知项目'}`);
        console.log(`   镜头: ${task.script_shots?.shot_number || '未知镜头'}`);
        console.log(`   生成图片数: ${task.generated_images.length}`);
        console.log(`   创建时间: ${task.created_at?.toLocaleString() || '未知'}`);
        
        if (task.error_message) {
          console.log(`   ❌ 错误信息: ${task.error_message}`);
        }
      });
    }

    // 6. 检查 Supabase 连接（如果配置了）
    if (process.env.NEXT_PUBLIC_SUPABASE_URL && process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      console.log('\n🔍 测试 Supabase 连接...');
      try {
        const { createClient } = require('@supabase/supabase-js');
        const supabase = createClient(
          process.env.NEXT_PUBLIC_SUPABASE_URL,
          process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
        );

        // 测试 Storage 连接
        const { data: buckets, error } = await supabase.storage.listBuckets();
        if (error) {
          console.log(`⚠️  Supabase Storage 连接警告: ${error.message}`);
        } else {
          console.log(`✅ Supabase Storage 连接成功，发现 ${buckets.length} 个存储桶`);
          buckets.forEach(bucket => {
            console.log(`   📦 存储桶: ${bucket.name} (${bucket.public ? '公开' : '私有'})`);
          });
        }
      } catch (supabaseError) {
        console.log(`❌ Supabase 连接失败: ${supabaseError.message}`);
      }
    }

    await prisma.$disconnect();
    console.log('\n✅ 数据库连接测试完成');

  } catch (error) {
    console.error('\n❌ 数据库连接测试失败:', error.message);
    console.error('详细错误信息:', error);
    
    // 提供一些常见问题的解决建议
    console.log('\n🔧 可能的解决方案:');
    console.log('1. 检查 DATABASE_URL 是否正确配置');
    console.log('2. 确保数据库服务正在运行');
    console.log('3. 检查网络连接');
    console.log('4. 运行 `npx prisma generate` 生成客户端');
    console.log('5. 运行 `npx prisma db push` 同步数据库结构');
  }
}

// 运行测试
testDatabaseConnection().catch(console.error);
