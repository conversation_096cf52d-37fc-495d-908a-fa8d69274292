#!/usr/bin/env node

/**
 * 测试新的刷新视频逻辑
 * 
 * 测试流程：
 * 1. 查询project_id、shot_id、user_id
 * 2. 根据status进行不同处理：
 *    - completed: 获取视频地址并展示
 *    - failed: 显示错误信息
 *    - processing: 查询Kling API并更新
 */

async function testNewRefreshLogic() {
  console.log('🔄 测试新的刷新视频逻辑');
  console.log('='.repeat(60));
  
  // 从数据库获取测试数据
  const { PrismaClient } = require('../lib/generated/prisma');
  const prisma = new PrismaClient();
  
  try {
    await prisma.$connect();
    
    // 获取不同状态的任务进行测试
    const tasks = await prisma.video_generation_tasks.findMany({
      where: {
        AND: [
          { project_id: { not: null } },
          { shot_id: { not: null } },
          { user_id: { not: null } }
        ]
      },
      include: {
        generated_videos: true
      },
      orderBy: { created_at: 'desc' },
      take: 5
    });
    
    if (tasks.length === 0) {
      console.log('❌ 没有找到合适的测试任务');
      return;
    }
    
    console.log(`📋 找到 ${tasks.length} 个测试任务`);
    
    // 按状态分组测试
    const tasksByStatus = {
      completed: tasks.filter(t => t.status === 'completed'),
      failed: tasks.filter(t => t.status === 'failed'),
      processing: tasks.filter(t => t.status === 'processing')
    };
    
    console.log('📊 任务状态分布:');
    console.log(`   completed: ${tasksByStatus.completed.length} 个`);
    console.log(`   failed: ${tasksByStatus.failed.length} 个`);
    console.log(`   processing: ${tasksByStatus.processing.length} 个`);
    
    // 测试1: completed状态
    if (tasksByStatus.completed.length > 0) {
      console.log('\n🟢 测试1: completed状态任务');
      await testCompletedTask(tasksByStatus.completed[0]);
    }
    
    // 测试2: failed状态
    if (tasksByStatus.failed.length > 0) {
      console.log('\n🔴 测试2: failed状态任务');
      await testFailedTask(tasksByStatus.failed[0]);
    }
    
    // 测试3: processing状态
    if (tasksByStatus.processing.length > 0) {
      console.log('\n🟡 测试3: processing状态任务');
      await testProcessingTask(tasksByStatus.processing[0]);
    }
    
    // 测试4: 组合查询API
    console.log('\n🔍 测试4: 组合查询API');
    await testCombinedQuery(tasks[0]);
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

async function testCompletedTask(task) {
  console.log(`   任务ID: ${task.id}`);
  console.log(`   状态: ${task.status}`);
  console.log(`   视频数量: ${task.generated_videos.length}`);
  
  if (task.generated_videos.length > 0) {
    console.log('   ✅ 预期行为: 直接获取视频地址并前端展示');
    console.log('   视频列表:');
    task.generated_videos.forEach((video, index) => {
      console.log(`     视频${index + 1}: ${video.video_url.substring(0, 60)}...`);
    });
  } else {
    console.log('   ⚠️ 任务已完成但没有视频记录');
  }
}

async function testFailedTask(task) {
  console.log(`   任务ID: ${task.id}`);
  console.log(`   状态: ${task.status}`);
  console.log(`   错误信息: ${task.error_message || '无'}`);
  console.log(`   错误代码: ${task.error_code || '无'}`);
  
  console.log('   ✅ 预期行为: 显示错误信息alert，告诉用户生成报错');
  console.log(`   Alert内容: "视频生成失败\\n错误: ${task.error_message || '未知错误'}"`);
}

async function testProcessingTask(task) {
  console.log(`   任务ID: ${task.id}`);
  console.log(`   状态: ${task.status}`);
  console.log(`   Kling任务ID: ${task.kling_task_id || '无'}`);
  
  if (task.kling_task_id) {
    console.log('   ✅ 预期行为: 查询Kling API获取最新状态');
    
    // 实际测试Kling API查询
    try {
      const response = await fetch('http://localhost:3001/api/check-task-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ taskId: task.kling_task_id })
      });
      
      if (response.ok) {
        const result = await response.json();
        console.log(`   📊 Kling API状态: ${result.data?.task_status}`);
        
        if (result.data?.task_status === 'succeed') {
          console.log('   ✅ 如果成功: 更新数据库为completed，获取视频地址展示');
        } else if (result.data?.task_status === 'processing') {
          console.log('   ⏳ 如果仍在处理: 提示用户稍后再试');
        } else if (result.data?.task_status === 'failed') {
          console.log('   ❌ 如果失败: 更新数据库为failed，显示错误信息');
        }
      } else {
        console.log('   ❌ Kling API查询失败');
      }
    } catch (error) {
      console.log(`   ❌ Kling API查询异常: ${error.message}`);
    }
  } else {
    console.log('   ⚠️ 缺少Kling任务ID，无法查询API');
  }
}

async function testCombinedQuery(task) {
  console.log(`   测试组合查询: 项目ID + 镜头ID + 用户ID`);
  console.log(`   项目ID: ${task.project_id}`);
  console.log(`   镜头ID: ${task.shot_id}`);
  console.log(`   用户ID: ${task.user_id}`);
  
  try {
    const queryUrl = `http://localhost:3001/api/save-video-generation?projectId=${task.project_id}&shotId=${task.shot_id}&userId=${task.user_id}`;
    console.log(`   查询URL: ${queryUrl}`);
    
    const response = await fetch(queryUrl);
    
    if (response.ok) {
      const result = await response.json();
      console.log(`   ✅ 查询成功: 找到 ${result.tasks?.length || 0} 个任务`);
      
      if (result.tasks && result.tasks.length > 0) {
        const latestTask = result.tasks[0];
        console.log(`   最新任务状态: ${latestTask.status}`);
        console.log(`   视频数量: ${latestTask.generated_videos?.length || 0}`);
      }
    } else {
      const error = await response.json();
      console.log(`   ❌ 查询失败: ${error.error}`);
    }
  } catch (error) {
    console.log(`   ❌ 查询异常: ${error.message}`);
  }
}

// 显示新逻辑的完整流程
function showNewLogicFlow() {
  console.log('\n🎯 新刷新逻辑完整流程');
  console.log('='.repeat(60));
  
  console.log('📋 步骤1: 检查必要参数');
  console.log('   • 检查 project_id、shot_id、user_id 是否存在');
  console.log('   • 如果缺少任何一个，显示详细错误信息');
  
  console.log('\n📋 步骤2: 查询数据库最新任务');
  console.log('   • 使用组合查询: project_id + shot_id + user_id');
  console.log('   • 获取最新的任务记录（按创建时间倒序）');
  
  console.log('\n📋 步骤3: 根据任务状态处理');
  console.log('   🟢 status = "completed":');
  console.log('     • 查询 generated_videos 表获取视频地址');
  console.log('     • 前端展示视频');
  console.log('     • 显示成功消息');
  
  console.log('   🔴 status = "failed":');
  console.log('     • 获取错误信息和错误代码');
  console.log('     • 显示详细的错误alert');
  console.log('     • 提供解决建议');
  
  console.log('   🟡 status = "processing":');
  console.log('     • 检查是否有 kling_task_id');
  console.log('     • 调用 Kling API 查询最新状态');
  console.log('     • 根据API结果更新数据库和前端');
  
  console.log('\n💡 优势:');
  console.log('   ✅ 逻辑清晰，状态处理明确');
  console.log('   ✅ 减少不必要的API调用');
  console.log('   ✅ 提供详细的用户反馈');
  console.log('   ✅ 自动同步数据库状态');
}

// 运行所有测试
async function runAllTests() {
  await testNewRefreshLogic();
  showNewLogicFlow();
  console.log('\n✅ 新刷新逻辑测试完成！');
}

runAllTests().catch(console.error);
