import { NextRequest, NextResponse } from 'next/server'
import { KlingAIClient } from '@/lib/kling-ai'
import { z } from 'zod'
import { createVideoGenerationTask, updateVideoGenerationTask, createGeneratedVideo } from '@/lib/prisma-video-operations'

const generateVideoSchema = z.object({
  prompt: z.string().min(1, "Prompt is required"),
  negativePrompt: z.string().optional(),
  image: z.string().url().optional(),
  imageReference: z.enum(['subject', 'face']).default('subject'),
  imageFidelity: z.number().min(0).max(1).default(0.5),
  duration: z.number().min(1).max(10).default(5),
  aspectRatio: z.enum(['1:1', '3:4', '4:3', '16:9', '9:16']).default('16:9'),
  mode: z.enum(['std', 'pro']).default('std'),
  modelName: z.enum(['kling-v1', 'kling-v1-5', 'kling-v1-6', 'kling-v2-master', 'kling-v2-1', 'kling-v2-1-master']).default('kling-v1'),
  // 数据库相关字段
  projectId: z.string().uuid().optional(),
  shotId: z.string().uuid().optional(),
  userId: z.string().uuid().optional(),
  taskName: z.string().optional(),
})

export async function POST(request: NextRequest) {
  console.log('🎬 [API] POST request received for video generation')

  let pollingTimeout: NodeJS.Timeout | null = null

  try {
    // 步骤1: 解析请求体
    console.log('📝 [API] Step 1: Parsing request body...')
    const body = await request.json()
    console.log('📝 [API] Raw request body:', JSON.stringify(body, null, 2))

    // 步骤2: 验证数据
    console.log('✅ [API] Step 2: Validating request data...')
    const validatedData = generateVideoSchema.parse(body)
    console.log('✅ [API] Validated data:', JSON.stringify(validatedData, null, 2))

    // 步骤3: 检查环境变量
    console.log('🔧 [API] Step 3: Checking environment variables...')
    const accessKey = process.env.KLING_ACCESS_KEY
    const secretKey = process.env.KLING_SECRET_KEY

    if (!accessKey || !secretKey) {
      console.error('❌ [API] Missing Kling AI credentials')
      return NextResponse.json(
        { error: 'Kling AI credentials not configured' },
        { status: 500 }
      )
    }

    console.log('🔧 [API] Environment variables OK')

    // 步骤4: 初始化客户端
    console.log('🔧 [API] Step 4: Initializing Kling AI client...')
    let klingClient
    try {
      klingClient = new KlingAIClient({ accessKey, secretKey })
      console.log('🔧 [API] Client initialized successfully')
    } catch (clientError) {
      console.error('❌ [API] Client initialization failed:', clientError)
      throw new Error(`Client initialization failed: ${clientError instanceof Error ? clientError.message : 'Unknown error'}`)
    }

    // 步骤5: 处理图片URL（如果提供）
    let finalImageUrl = validatedData.image
    if (validatedData.image) {
      console.log('🔍 [API] Step 5: Processing image URL...')
      console.log('🔍 [API] Original image URL:', validatedData.image)

      // 检查是否是内网URL（MinIO或其他内网地址）
      const isInternalUrl = validatedData.image.includes('***********') ||
                           validatedData.image.includes('localhost') ||
                           validatedData.image.includes('127.0.0.1') ||
                           validatedData.image.includes('192.168.') ||
                           validatedData.image.includes('10.') ||
                           validatedData.image.startsWith('http://172.') ||
                           validatedData.image.startsWith('http://192.168.')

      const isSupabaseUrl = validatedData.image.includes(process.env.NEXT_PUBLIC_SUPABASE_URL || '') &&
                            validatedData.image.includes('/storage/v1/object/public/generated_images/')

      console.log('🔍 [API] Image source analysis:')
      console.log('🔍 [API] - Is internal URL:', isInternalUrl)
      console.log('🔍 [API] - Is Supabase Storage URL:', isSupabaseUrl)

      if (isInternalUrl) {
        console.log('📤 [API] Internal URL detected, uploading to Supabase Storage...')

        // 检查Supabase配置
        const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
        const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

        if (!supabaseUrl || !supabaseAnonKey) {
          console.error('❌ [API] Supabase configuration not complete')
          return NextResponse.json({
            success: false,
            error: 'Supabase 配置未完成，无法上传内网图片',
            userMessage: 'Supabase 配置未完成，无法上传内网图片'
          }, { status: 500 })
        }

        // 导入Supabase客户端
        const { createClient } = await import('@supabase/supabase-js')
        const supabase = createClient(supabaseUrl, supabaseAnonKey)

        try {
          // 下载内网图片
          console.log('📥 [API] Downloading image from internal URL...')
          const imageResponse = await fetch(validatedData.image)
          if (!imageResponse.ok) {
            throw new Error(`Failed to download image: ${imageResponse.status} ${imageResponse.statusText}`)
          }

          const imageBuffer = await imageResponse.arrayBuffer()
          const buffer = Buffer.from(imageBuffer)

          console.log('📤 [API] Uploading to Supabase Storage...')

          // 生成文件名和路径
          const urlParts = validatedData.image.split('.')
          const fileExtension = urlParts.length > 1 ? urlParts.pop() : 'jpg'
          const fileName = `video-generation-${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExtension}`
          const filePath = `video-generation/${fileName}`

          // 上传到Supabase Storage
          const { data: uploadData, error: uploadError } = await supabase.storage
            .from('generated-images')
            .upload(filePath, buffer, {
              contentType: imageResponse.headers.get('content-type') || 'image/jpeg',
              cacheControl: '3600',
              upsert: false
            })

          if (uploadError) {
            console.error('❌ [API] Supabase Storage upload failed:', uploadError)
            throw new Error(`Supabase Storage upload failed: ${uploadError.message}`)
          }

          // 获取公共URL
          const { data: urlData } = supabase.storage
            .from('generated-images')
            .getPublicUrl(filePath)

          finalImageUrl = urlData.publicUrl
          console.log('✅ [API] Successfully uploaded to Supabase Storage:', finalImageUrl)

        } catch (uploadError) {
          console.error('❌ [API] Failed to upload internal image to Supabase Storage:', uploadError)
          return NextResponse.json({
            success: false,
            error: '内网图片上传到Supabase Storage失败',
            userMessage: '内网图片上传到Supabase Storage失败，请重试',
            details: {
              originalUrl: validatedData.image,
              error: uploadError instanceof Error ? uploadError.message : String(uploadError)
            }
          }, { status: 500 })
        }
      } else if (isSupabaseUrl) {
        console.log('✅ [API] Supabase Storage URL detected, using directly')
        finalImageUrl = validatedData.image
      } else {
        console.log('⚠️ [API] External URL detected, verifying accessibility...')
        try {
          const imageVerifyResponse = await fetch(validatedData.image, { method: 'HEAD' })
          if (!imageVerifyResponse.ok) {
            console.error('❌ [API] External image URL verification failed:', imageVerifyResponse.status, imageVerifyResponse.statusText)
            return NextResponse.json({
              success: false,
              error: '外部图片URL无法访问，请重新上传图片',
              userMessage: '外部图片URL无法访问，请重新上传图片',
              details: {
                imageUrl: validatedData.image,
                status: imageVerifyResponse.status,
                statusText: imageVerifyResponse.statusText
              }
            }, { status: 400 })
          }
          console.log('✅ [API] External image URL verification successful')
        } catch (verifyError) {
          console.error('❌ [API] External image URL verification error:', verifyError)
          return NextResponse.json({
            success: false,
            error: '外部图片URL验证失败，请重新上传图片',
            userMessage: '外部图片URL验证失败，请重新上传图片',
            details: {
              imageUrl: validatedData.image,
              error: verifyError instanceof Error ? verifyError.message : String(verifyError)
            }
          }, { status: 400 })
        }
      }

      console.log('🔍 [API] Final image URL for Kling:', finalImageUrl)
    }

    // 步骤6: 生成视频
    console.log('🎬 [API] Step 6: Generating video...')
    console.log('🎬 [API] Generation params:', {
      prompt: validatedData.prompt,
      negativePrompt: validatedData.negativePrompt,
      image: finalImageUrl,
      imageReference: validatedData.imageReference,
      imageFidelity: validatedData.imageFidelity,
      duration: validatedData.duration,
      aspectRatio: validatedData.aspectRatio,
      modelName: validatedData.modelName,
    })
    console.log('🎬 [API] Final image URL for Kling:', finalImageUrl)

    let taskId
    let dbTaskId: string | null = null

    // 步骤6.1: 先保存任务到数据库（不包含 Kling Task ID）
    console.log('💾 [API] Step 6.1: Saving initial task to database...')
    try {
      const dbTask = await createVideoGenerationTask({
        projectId: validatedData.projectId,
        shotId: validatedData.shotId,
        userId: validatedData.userId,
        taskName: validatedData.taskName,
        prompt: validatedData.prompt,
        negativePrompt: validatedData.negativePrompt,
        imageUrl: finalImageUrl || '',  // 提供默认值，避免 null
        modelName: validatedData.modelName,
        aspectRatio: validatedData.aspectRatio,
        duration: validatedData.duration,
        cfgScale: validatedData.imageFidelity,  // 映射到 cfg_scale
        apiRequestPayload: {
          prompt: validatedData.prompt,
          negativePrompt: validatedData.negativePrompt,
          image: finalImageUrl,
          imageReference: validatedData.imageReference,
          imageFidelity: validatedData.imageFidelity,
          duration: validatedData.duration,
          aspectRatio: validatedData.aspectRatio,
          modelName: validatedData.modelName,
        }
      })
      dbTaskId = dbTask.id
      console.log('✅ [API] Initial task saved to database with ID:', dbTaskId)
    } catch (dbError) {
      console.error('⚠️ [API] Failed to save initial task to database:', dbError)
    }

    // 步骤6.2: 调用 Kling AI 生成视频
    try {
      console.log('🎬 [API] Calling Kling AI generateVideo...')
      taskId = await klingClient.generateVideo({
        prompt: validatedData.prompt,
        negativePrompt: validatedData.negativePrompt,
        image: finalImageUrl,
        imageReference: validatedData.imageReference,
        imageFidelity: validatedData.imageFidelity,
        duration: validatedData.duration,
        aspectRatio: validatedData.aspectRatio,
        mode: validatedData.mode,
        modelName: validatedData.modelName,
      })
      console.log('🎬 [API] Task created successfully with ID:', taskId)

      // 步骤6.3: 更新数据库中的 Kling Task ID
      if (dbTaskId && taskId) {
        try {
          await updateVideoGenerationTask(dbTaskId, {
            klingTaskId: taskId,
            status: 'processing',
            startedAt: new Date()
          })
          console.log('✅ [API] Database updated with Kling Task ID:', taskId)
        } catch (updateError) {
          console.error('⚠️ [API] Failed to update task with Kling ID:', updateError)
        }
      }

    } catch (generateError) {
      console.error('❌ [API] ===== VIDEO GENERATION FAILED =====')
      console.error('❌ [API] Error type:', generateError instanceof Error ? generateError.constructor.name : typeof generateError)
      console.error('❌ [API] Error message:', generateError instanceof Error ? generateError.message : String(generateError))
      console.error('❌ [API] Error stack:', generateError instanceof Error ? generateError.stack : 'No stack trace')
      console.error('❌ [API] Request params:', JSON.stringify(validatedData, null, 2))
      console.error('❌ [API] ===== END VIDEO GENERATION ERROR =====')

      // 更新数据库中的错误状态
      if (dbTaskId) {
        try {
          await updateVideoGenerationTask(dbTaskId, {
            status: 'failed',
            errorMessage: generateError instanceof Error ? generateError.message : String(generateError),
            completedAt: new Date()
          })
          console.log('✅ [API] Database updated with error status')
        } catch (updateError) {
          console.error('⚠️ [API] Failed to update task with error status:', updateError)
        }
      }

      throw new Error(JSON.stringify({
        message: `Video generation failed: ${generateError instanceof Error ? generateError.message : 'Unknown error'}`,
        details: {
          originalError: generateError instanceof Error ? generateError.message : String(generateError),
          params: validatedData
        },
        userMessage: '视频生成请求失败，请检查参数或重试'
      }))
    }

    // 步骤7: 轮询任务状态
    console.log('⏳ [API] Step 7: Polling task status...')
    let attempts = 0
    const maxAttempts = 60 // 最多等待120秒 (视频生成通常需要更长时间)
    
    const pollTaskStatus = async (): Promise<any> => {
      attempts++
      console.log(`⏳ [API] Polling attempt ${attempts}/${maxAttempts} for task ${taskId}`)

      try {
        const taskStatus = await klingClient.getTaskStatus(taskId, 'video')
        console.log(`⏳ [API] Task ${taskId} status response:`, JSON.stringify(taskStatus, null, 2))

        if (taskStatus.data.task_status === 'succeed') {
          console.log('✅ [API] Task completed successfully')
          console.log('✅ [API] Task result:', JSON.stringify(taskStatus.data.task_result, null, 2))

          // 更新数据库任务状态为完成
          if (dbTaskId) {
            try {
              await updateVideoGenerationTask(dbTaskId, {
                status: 'completed',
                completedAt: new Date(),
                apiResponseData: taskStatus.data
              })

              // 保存生成的视频到数据库
              if (taskStatus.data.task_result?.videos) {
                for (const video of taskStatus.data.task_result.videos) {
                  await createGeneratedVideo({
                    taskId: dbTaskId,
                    videoUrl: video.url,
                    videoDurationSeconds: validatedData.duration,
                    videoFormat: 'mp4',
                    storageProvider: 'kling',
                    metadata: video
                  })
                }
              }

              console.log('✅ [API] Database updated successfully for completed task')
            } catch (dbError) {
              console.error('⚠️ [API] Failed to update database for completed task:', dbError)
            }
          }

          return taskStatus.data.task_result
        } else if (taskStatus.data.task_status === 'failed') {
          console.error('❌ [API] ===== TASK FAILED - DETAILED ERROR LOG =====')
          console.error('❌ [API] Task ID:', taskId)
          console.error('❌ [API] Full task status response:', JSON.stringify(taskStatus, null, 2))
          console.error('❌ [API] Task status:', taskStatus.data.task_status)
          console.error('❌ [API] Error message:', taskStatus.message || 'No error message provided')
          console.error('❌ [API] Task status message:', taskStatus.data.task_status_msg || 'No status message')
          console.error('❌ [API] Task data:', JSON.stringify(taskStatus.data, null, 2))
          console.error('❌ [API] ===== END DETAILED ERROR LOG =====')

          // 更新数据库任务状态为失败
          if (dbTaskId) {
            try {
              await updateVideoGenerationTask(dbTaskId, {
                status: 'failed',
                completedAt: new Date(),
                errorMessage: taskStatus.data.task_status_msg || taskStatus.message || 'Task failed',
                apiResponseData: taskStatus.data
              })
              console.log('✅ [API] Database updated for failed task')
            } catch (dbError) {
              console.error('⚠️ [API] Failed to update database for failed task:', dbError)
            }
          }

          // 创建一个特殊的错误类型来标识任务失败，避免被 catch 块重试
          const taskFailedError = new Error('TASK_FAILED')
          taskFailedError.name = 'TaskFailedError'
          ;(taskFailedError as any).taskFailureDetails = {
            taskId,
            status: taskStatus.data.task_status,
            message: taskStatus.message,
            statusMessage: taskStatus.data.task_status_msg,
            code: taskStatus.code,
            fullResponse: taskStatus
          }

          throw taskFailedError
        } else if (attempts >= maxAttempts) {
          console.error('❌ [API] ===== TASK TIMEOUT - DETAILED LOG =====')
          console.error('❌ [API] Task ID:', taskId)
          console.error('❌ [API] Max attempts reached:', maxAttempts)
          console.error('❌ [API] Last known status:', taskStatus.data.task_status)
          console.error('❌ [API] Total wait time:', maxAttempts * 2, 'seconds')
          console.error('❌ [API] ===== END TIMEOUT LOG =====')

          throw new Error(JSON.stringify({
            message: 'Video generation timeout',
            details: {
              taskId,
              attempts: maxAttempts,
              lastStatus: taskStatus.data.task_status,
              waitTime: maxAttempts * 2
            },
            userMessage: '视频生成超时，请稍后重试'
          }))
        } else {
          console.log(`⏳ [API] Task ${taskId} still processing (${taskStatus.data.task_status}), waiting 2 seconds...`)
          // 等待2秒后重试
          await new Promise(resolve => {
            pollingTimeout = setTimeout(resolve, 2000)
          })
          return pollTaskStatus()
        }
      } catch (pollError) {
        // 检查是否是任务失败错误，如果是则不重试
        if (pollError instanceof Error && pollError.name === 'TaskFailedError') {
          console.error('❌ [API] Task failed, stopping polling')
          throw pollError
        }

        console.error('❌ [API] ===== POLLING ERROR - DETAILED LOG =====')
        console.error('❌ [API] Task ID:', taskId)
        console.error('❌ [API] Attempt:', attempts)
        console.error('❌ [API] Error type:', pollError instanceof Error ? pollError.constructor.name : typeof pollError)
        console.error('❌ [API] Error message:', pollError instanceof Error ? pollError.message : String(pollError))
        console.error('❌ [API] Error stack:', pollError instanceof Error ? pollError.stack : 'No stack trace')
        console.error('❌ [API] ===== END POLLING ERROR LOG =====')

        if (attempts >= maxAttempts) {
          console.error('❌ [API] Max polling attempts reached, giving up')
          throw pollError
        }
        // 等待2秒后重试
        console.log(`⏳ [API] Retrying in 2 seconds... (attempt ${attempts + 1}/${maxAttempts})`)
        await new Promise(resolve => {
          pollingTimeout = setTimeout(resolve, 2000)
        })
        return pollTaskStatus()
      }
    }

    const result = await pollTaskStatus()

    // 清理定时器
    if (pollingTimeout) {
      clearTimeout(pollingTimeout)
      pollingTimeout = null
    }

    console.log('🎬 [API] Final result:', JSON.stringify(result, null, 2))

    // 步骤7: 返回结果
    return NextResponse.json({
      success: true,
      taskId,
      videos: result.videos || [],
      message: 'Video generated successfully'
    })

  } catch (error) {
    // 清理定时器防止内存泄漏
    if (pollingTimeout) {
      clearTimeout(pollingTimeout)
      pollingTimeout = null
    }

    console.error('💥 [API] ===== MAIN ERROR HANDLER =====')
    console.error('💥 [API] Error type:', error instanceof Error ? error.constructor.name : typeof error)
    console.error('💥 [API] Error message:', error instanceof Error ? error.message : String(error))
    console.error('💥 [API] Error stack:', error instanceof Error ? error.stack : 'No stack trace')
    console.error('💥 [API] ===== END MAIN ERROR HANDLER =====')

    if (error instanceof z.ZodError) {
      console.error('💥 [API] Zod validation error details:', error.errors)
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request data',
          details: error.errors,
          userMessage: '请求参数无效，请检查输入数据'
        },
        { status: 400 }
      )
    }

    // 尝试解析结构化错误信息
    let errorResponse = {
      success: false,
      error: 'Video generation failed',
      userMessage: '视频生成失败，请重试',
      details: null as any
    }

    if (error instanceof Error) {
      // 处理任务失败错误
      if (error.name === 'TaskFailedError') {
        const taskDetails = (error as any).taskFailureDetails
        errorResponse.error = 'Video generation task failed'

        // 根据具体的失败原因提供更精确的用户消息
        let userMessage = '视频生成失败，请重试'
        if (taskDetails?.statusMessage) {
          const statusMsg = taskDetails.statusMessage.toLowerCase()
          if (statusMsg.includes('fail to read file') || statusMsg.includes('url')) {
            userMessage = '无法读取图片文件，请检查图片是否有效或重新上传图片'
          } else if (statusMsg.includes('timeout')) {
            userMessage = '视频生成超时，请稍后重试'
          } else if (statusMsg.includes('invalid') || statusMsg.includes('format')) {
            userMessage = '图片格式不支持，请上传JPG、PNG或WebP格式的图片'
          } else if (statusMsg.includes('size') || statusMsg.includes('resolution')) {
            userMessage = '图片尺寸不符合要求，请调整图片大小后重试'
          } else {
            userMessage = '视频生成失败，请检查图片质量或重试'
          }
        }

        errorResponse.userMessage = userMessage
        errorResponse.details = taskDetails
      } else {
        try {
          // 尝试解析JSON格式的错误信息
          const parsedError = JSON.parse(error.message)
          errorResponse.error = parsedError.message || error.message
          errorResponse.userMessage = parsedError.userMessage || '视频生成失败，请重试'
          errorResponse.details = parsedError.details || null
        } catch (parseError) {
          // 如果不是JSON格式，使用原始错误信息
          errorResponse.error = error.message
          errorResponse.userMessage = error.message.includes('timeout') ? '视频生成超时，请稍后重试' :
                                     error.message.includes('failed') ? '视频生成失败，请检查图片质量或重试' :
                                     '视频生成失败，请重试'
        }
      }
    }

    // 在开发环境中添加堆栈信息
    if (process.env.NODE_ENV === 'development' && error instanceof Error) {
      errorResponse.details = {
        ...errorResponse.details,
        stack: error.stack
      }
    }

    return NextResponse.json(errorResponse, { status: 500 })
  }
}

export async function GET() {
  console.log('🔍 [API] GET request received - Health check')
  
  // 环境变量检查
  const accessKey = process.env.KLING_ACCESS_KEY
  const secretKey = process.env.KLING_SECRET_KEY
  
  console.log('🔍 [API] Health check - Access key exists:', !!accessKey)
  console.log('🔍 [API] Health check - Secret key exists:', !!secretKey)
  
  return NextResponse.json(
    { 
      message: 'Video generation API is working',
      hasAccessKey: !!accessKey,
      hasSecretKey: !!secretKey,
      timestamp: new Date().toISOString()
    },
    { status: 200 }
  )
}
