import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export interface Project {
  id: string
  title: string
  description: string
  thumbnail: string
  status: "completed" | "in-progress" | "draft"
  createdAt: string
  updatedAt: string
  duration: string
  scenes: number
  currentStep: number
  totalSteps: number
}

// GET /api/projects - 获取用户的所有项目（用于gallery页面）
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')

    if (!userId) {
      return NextResponse.json({
        success: false,
        error: '用户ID不能为空'
      }, { status: 400 })
    }

    const scripts = await prisma.script_projects.findMany({
      where: {
        user_id: userId
      },
      include: {
        script_characters: true,
        script_shots: {
          orderBy: {
            shot_number: 'asc'
          }
        },
        script_generation_configs: true,
        image_generation_tasks: {
          include: {
            generated_images: {
              where: {
                is_primary: true
              },
              take: 1,
              orderBy: {
                created_at: 'desc'
              }
            }
          },
          where: {
            status: 'completed'
          },
          orderBy: {
            created_at: 'desc'
          }
        }
      },
      orderBy: {
        updated_at: 'desc'
      }
    })

    // 转换数据格式以匹配gallery页面的Project接口
    const projects: Project[] = scripts.map(script => {
      // 获取主要图片作为缩略图
      const primaryImage = script.image_generation_tasks
        .flatMap(task => task.generated_images)
        .find(img => img.is_primary)

      // 如果没有主要图片，获取最新的图片
      const latestImage = script.image_generation_tasks
        .flatMap(task => task.generated_images)
        .sort((a, b) => new Date(b.created_at || 0).getTime() - new Date(a.created_at || 0).getTime())[0]

      const thumbnailImage = primaryImage || latestImage

      // 计算当前步骤（基于项目状态和数据完整性）
      let currentStep = 1
      if (script.script_content || script.script_shots.length > 0) currentStep = 2
      if (script.image_generation_tasks.length > 0) currentStep = 3
      if (script.status === 'completed') currentStep = 7

      // 确定项目状态
      let projectStatus: "completed" | "in-progress" | "draft" = 'draft'
      if (script.status === 'completed') {
        projectStatus = 'completed'
      } else if (script.script_content || script.script_shots.length > 0 || script.image_generation_tasks.length > 0) {
        projectStatus = 'in-progress'
      } else if (script.status === 'archived') {
        projectStatus = 'draft' // 将archived状态映射为draft显示
      }

      return {
        id: script.id,
        title: script.title,
        description: script.description || '',
        thumbnail: thumbnailImage?.cdn_url || thumbnailImage?.image_url || '/placeholder.svg?height=200&width=300',
        status: projectStatus,
        createdAt: script.created_at?.toISOString().split('T')[0] || '',
        updatedAt: script.updated_at?.toISOString().split('T')[0] || '',
        duration: formatDuration(script.duration),
        scenes: script.script_shots.length,
        currentStep,
        totalSteps: 7
      }
    })

    return NextResponse.json({
      success: true,
      projects
    })

  } catch (error) {
    console.error('Error fetching user projects:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '获取用户项目时发生未知错误'
    }, { status: 500 })
  }
}

// 辅助函数：将秒数转换为分:秒格式
function formatDuration(seconds: number): string {
  if (!seconds || seconds === 0) return '0:00'
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
}
