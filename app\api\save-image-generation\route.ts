import { NextRequest, NextResponse } from 'next/server'
import { createImageGenerationTask, createGeneratedImage, updateImageGenerationTask } from '@/lib/prisma-image-operations'
import { z } from 'zod'

const saveImageTaskSchema = z.object({
  projectId: z.string().uuid().optional(),
  shotId: z.string().uuid().optional(),
  userId: z.string().uuid().optional(),
  taskName: z.string().optional(),
  prompt: z.string().min(1),
  negativePrompt: z.string().optional(),
  modelName: z.string().optional(),
  aspectRatio: z.string().optional(),
  imageCount: z.number().min(1).max(6).default(1),

  seed: z.string().optional(),
  klingTaskId: z.string().optional(),
  referenceImageUrl: z.string().optional(),
  referenceImageStrength: z.number().min(0).max(1).optional(),
  referenceImageObjectName: z.string().optional(),
})

const saveGeneratedImageSchema = z.object({
  taskId: z.string().uuid().optional(),
  imageUrl: z.string().url(),
  imageFilename: z.string().optional(),
  imageSizeBytes: z.number().optional(),
  imageWidth: z.number().optional(),
  imageHeight: z.number().optional(),
  imageFormat: z.string().default('jpg'),
  qualityScore: z.number().optional(),
  isPrimary: z.boolean().default(false),
  storagePath: z.string().optional(),
  minioObjectName: z.string().nullish(),
  minioBucketName: z.string().default('images'),
  cdnUrl: z.string().optional(),
  cdnBaseUrl: z.string().optional(),
  generationTimeSeconds: z.number().optional(),
  actualPrompt: z.string().optional(),
  metadata: z.any().optional(),
})

// POST /api/save-image-generation - 保存图像生成任务
export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // 验证请求数据
    const validatedData = saveImageTaskSchema.parse(body)
    
    console.log('📸 [API] Saving image generation task:', {
      projectId: validatedData.projectId,
      userId: validatedData.userId,
      prompt: validatedData.prompt.substring(0, 100) + '...',
      imageCount: validatedData.imageCount
    })
    
    // 创建图像生成任务记录
    const task = await createImageGenerationTask(validatedData)
    
    console.log('✅ [API] Image generation task saved:', task.id)
    
    return NextResponse.json({
      success: true,
      taskId: task.id,
      task
    })
    
  } catch (error) {
    console.error('❌ [API] Error saving image generation task:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid request data', 
          details: error.errors 
        },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : 'Failed to save image generation task' 
      },
      { status: 500 }
    )
  }
}

// PUT /api/save-image-generation - 更新任务状态并保存生成的图像
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json()
    
    const { taskId, images, ...updateData } = z.object({
      taskId: z.string().uuid(),
      images: z.array(saveGeneratedImageSchema).optional(),
      status: z.string().optional(),
      klingTaskId: z.string().optional(),
      apiResponseData: z.any().optional(),
      errorMessage: z.string().optional(),
      errorCode: z.string().optional(),
      startedAt: z.string().optional(),
      completedAt: z.string().optional(),
      referenceImageUrl: z.string().optional(),
      referenceImageStrength: z.number().min(0).max(1).optional(),
      referenceImageObjectName: z.string().optional(),
    }).parse(body)
    
    console.log('📸 [API] Updating image generation task:', taskId)
    
    // 更新任务状态
    const updatedTask = await updateImageGenerationTask(taskId, {
      ...updateData,
      startedAt: updateData.startedAt ? new Date(updateData.startedAt) : undefined,
      completedAt: updateData.completedAt ? new Date(updateData.completedAt) : undefined,
    })
    
    // 保存生成的图像记录
    const savedImages = []
    if (images && images.length > 0) {
      for (const imageData of images) {
        const savedImage = await createGeneratedImage({
          ...imageData,
          taskId,
        })
        savedImages.push(savedImage)
      }
    }
    
    console.log('✅ [API] Image generation task updated:', {
      taskId,
      status: updatedTask.status,
      imagesCount: savedImages.length
    })
    
    return NextResponse.json({
      success: true,
      task: updatedTask,
      images: savedImages
    })
    
  } catch (error) {
    console.error('❌ [API] Error updating image generation task:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Invalid request data', 
          details: error.errors 
        },
        { status: 400 }
      )
    }
    
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : 'Failed to update image generation task' 
      },
      { status: 500 }
    )
  }
}

// GET /api/save-image-generation - 获取图像生成任务列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const projectId = searchParams.get('projectId')
    const userId = searchParams.get('userId')
    
    if (!projectId && !userId) {
      return NextResponse.json(
        { 
          success: false,
          error: 'projectId or userId is required' 
        },
        { status: 400 }
      )
    }
    
    let tasks = []
    
    if (projectId) {
      const { getImageGenerationTasksByProject } = await import('@/lib/prisma-image-operations')
      tasks = await getImageGenerationTasksByProject(projectId)
    } else if (userId) {
      const { getGeneratedImagesByProject } = await import('@/lib/prisma-image-operations')
      tasks = await getGeneratedImagesByProject(userId)
    }
    
    return NextResponse.json({
      success: true,
      tasks
    })
    
  } catch (error) {
    console.error('❌ [API] Error fetching image generation tasks:', error)
    
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : 'Failed to fetch image generation tasks' 
      },
      { status: 500 }
    )
  }
}