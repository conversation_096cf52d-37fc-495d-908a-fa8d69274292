"use client"

import { useState, use<PERSON>em<PERSON>, useEffect } from "react"
import { useAuth } from "@/contexts/AuthContext"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { AuthForm } from "@/components/auth/AuthForm"
import {
  Search,
  Plus,
  MoreVertical,
  Edit,
  Trash2,
  Share2,
  Play,
  Clock,
  Calendar,
  Filter,
  Grid3X3,
  List,
  ArrowLeft,
  Video,
  FileText,
  ImageIcon,
  Music,
  Loader2,
} from "lucide-react"
import Link from "next/link"
import Image from "next/image"

interface Project {
  id: string
  title: string
  description: string
  thumbnail: string
  status: "completed" | "in-progress" | "draft"
  createdAt: string
  updatedAt: string
  duration: string
  scenes: number
  currentStep: number
  totalSteps: number
}

const statusConfig = {
  completed: { label: "已完成", color: "bg-green-100 text-green-800", variant: "secondary" as const },
  "in-progress": { label: "进行中", color: "bg-blue-100 text-blue-800", variant: "default" as const },
  draft: { label: "草稿", color: "bg-gray-100 text-gray-800", variant: "outline" as const },
}

export default function ProjectsPage() {
  const { user, loading: authLoading } = useAuth()
  const [projects, setProjects] = useState<Project[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [sortBy, setSortBy] = useState("updatedAt")
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [deleteProjectId, setDeleteProjectId] = useState<string | null>(null)
  const [showAuthDialog, setShowAuthDialog] = useState(false)

  // 测试登录函数
  const handleTestLogin = async () => {
    try {
      const response = await fetch('/api/test-login', {
        method: 'POST'
      })
      const data = await response.json()

      if (data.success) {
        localStorage.setItem('auth_token', data.token)
        window.location.reload() // 简单刷新页面来更新认证状态
      } else {
        alert('测试登录失败: ' + data.error)
      }
    } catch (error) {
      console.error('Test login error:', error)
      alert('测试登录时发生错误')
    }
  }

  // 获取项目数据
  useEffect(() => {
    const fetchProjects = async () => {
      if (!user?.id) {
        setLoading(false)
        return
      }

      try {
        const response = await fetch(`/api/projects?userId=${user.id}`)
        const data = await response.json()

        if (data.success) {
          setProjects(data.projects)
        } else {
          console.error('Failed to fetch projects:', data.error)
        }
      } catch (error) {
        console.error('Error fetching projects:', error)
      } finally {
        setLoading(false)
      }
    }

    if (!authLoading) {
      fetchProjects()
    }
  }, [user?.id, authLoading])

  // 筛选和排序项目
  const filteredProjects = useMemo(() => {
    const filtered = projects.filter((project: Project) => {
      const matchesSearch =
        project.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        project.description.toLowerCase().includes(searchQuery.toLowerCase())
      const matchesStatus = statusFilter === "all" || project.status === statusFilter
      return matchesSearch && matchesStatus
    })

    // 排序
    filtered.sort((a: Project, b: Project) => {
      if (sortBy === "createdAt") {
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      } else {
        return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
      }
    })

    return filtered
  }, [projects, searchQuery, statusFilter, sortBy])

  const handleDeleteProject = async (projectId: string) => {
    try {
      const response = await fetch(`/api/projects/${projectId}`, {
        method: 'DELETE'
      })

      const data = await response.json()

      if (data.success) {
        // 从本地状态中移除已删除的项目
        setProjects(prev => prev.filter(p => p.id !== projectId))
        setDeleteProjectId(null)
      } else {
        console.error('Failed to delete project:', data.error)
        alert('删除项目失败: ' + data.error)
      }
    } catch (error) {
      console.error('Error deleting project:', error)
      alert('删除项目时发生错误')
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("zh-CN", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  const getStepIcon = (step: number) => {
    const icons = [FileText, ImageIcon, Video, Music, Edit, Video, FileText]
    const Icon = icons[step - 1] || FileText
    return Icon
  }

  // 如果用户未登录，显示登录提示
  if (!authLoading && !user) {
    return (
      <>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <Video className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">请先登录</h3>
            <p className="text-gray-500 mb-6">登录后即可查看您的作品库</p>
            <div className="space-x-4">
              <Button
                className="bg-blue-600 hover:bg-blue-700"
                onClick={() => setShowAuthDialog(true)}
              >
                立即登录
              </Button>
              <Button
                variant="outline"
                onClick={handleTestLogin}
              >
                测试登录
              </Button>
            </div>
          </div>
        </div>

        <Dialog open={showAuthDialog} onOpenChange={setShowAuthDialog}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>欢迎</DialogTitle>
              <DialogDescription>
                登录或注册账号开始使用AI短剧生成器
              </DialogDescription>
            </DialogHeader>
            <AuthForm onSuccess={() => setShowAuthDialog(false)} />
          </DialogContent>
        </Dialog>
      </>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/50">
      {/* 顶部导航 */}
      <header className="bg-white/80 backdrop-blur-xl border-b border-slate-200/60 shadow-sm">
        <div className="container mx-auto px-6 py-5">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-6">
              <Link href="/" className="group flex items-center text-slate-600 hover:text-slate-900 transition-all duration-300 hover:scale-105">
                <ArrowLeft className="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform duration-300" />
                <span className="font-medium">返回首页</span>
              </Link>
              <div className="w-px h-6 bg-slate-300"></div>
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                  <Video className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold bg-gradient-to-r from-slate-800 via-blue-700 to-purple-700 bg-clip-text text-transparent">
                    我的作品库
                  </h1>
                  <p className="text-sm text-slate-600">管理您的AI短剧创作</p>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setViewMode(viewMode === "grid" ? "list" : "grid")}
                className="group bg-white/70 border-slate-300 hover:border-slate-400 hover:bg-white hover:shadow-md transition-all duration-300"
              >
                {viewMode === "grid" ? (
                  <List className="w-4 h-4 group-hover:scale-110 transition-transform duration-300" />
                ) : (
                  <Grid3X3 className="w-4 h-4 group-hover:scale-110 transition-transform duration-300" />
                )}
                <span className="ml-2 font-medium">
                  {viewMode === "grid" ? "列表视图" : "网格视图"}
                </span>
              </Button>
              <Link href="/create">
                <Button className="group bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 px-6 py-2.5">
                  <Plus className="w-4 h-4 mr-2 group-hover:rotate-90 transition-transform duration-300" />
                  新建项目
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-6 py-8">
        {/* 顶部筛选栏 */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-slate-200/60 p-8 mb-8 shadow-lg">
          <div className="flex flex-col lg:flex-row gap-6 items-start lg:items-center justify-between">
            <div className="flex flex-col sm:flex-row gap-4 flex-1">
              {/* 搜索框 */}
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
                <Input
                  placeholder="搜索项目名称或描述..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-12 h-12 bg-white/70 border-slate-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 text-base font-medium placeholder:text-slate-400 transition-all duration-300"
                />
              </div>

              {/* 状态筛选 */}
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-44 h-12 bg-white/70 border-slate-300 hover:border-slate-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-300">
                  <Filter className="w-4 h-4 mr-2 text-slate-500" />
                  <SelectValue className="font-medium" />
                </SelectTrigger>
                <SelectContent className="bg-white/95 backdrop-blur-sm border border-slate-200 shadow-xl rounded-xl">
                  <SelectItem value="all" className="font-medium">全部状态</SelectItem>
                  <SelectItem value="completed" className="font-medium">已完成</SelectItem>
                  <SelectItem value="in-progress" className="font-medium">进行中</SelectItem>
                  <SelectItem value="draft" className="font-medium">草稿</SelectItem>
                </SelectContent>
              </Select>

              {/* 排序方式 */}
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-44 h-12 bg-white/70 border-slate-300 hover:border-slate-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-300">
                  <Clock className="w-4 h-4 mr-2 text-slate-500" />
                  <SelectValue className="font-medium" />
                </SelectTrigger>
                <SelectContent className="bg-white/95 backdrop-blur-sm border border-slate-200 shadow-xl rounded-xl">
                  <SelectItem value="updatedAt" className="font-medium">修改时间</SelectItem>
                  <SelectItem value="createdAt" className="font-medium">创建时间</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* 统计信息 */}
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-2 bg-gradient-to-r from-blue-50 to-indigo-50 px-4 py-2 rounded-xl border border-blue-200">
                <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                <span className="text-sm font-semibold text-blue-700">
                  共 {filteredProjects.length} 个项目
                </span>
              </div>
              <div className="flex items-center space-x-2 bg-gradient-to-r from-emerald-50 to-green-50 px-4 py-2 rounded-xl border border-emerald-200">
                <div className="w-2 h-2 rounded-full bg-emerald-500"></div>
                <span className="text-sm font-semibold text-emerald-700">
                  已完成 {projects.filter((p: Project) => p.status === "completed").length} 个
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* 作品网格/列表 */}
        {loading || authLoading ? (
          <div className="flex flex-col items-center justify-center py-20">
            <div className="w-16 h-16 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center mb-6 animate-pulse">
              <Loader2 className="w-8 h-8 animate-spin text-white" />
            </div>
            <h3 className="text-lg font-semibold text-slate-700 mb-2">加载中...</h3>
            <p className="text-slate-500">正在获取您的作品数据</p>
          </div>
        ) : filteredProjects.length === 0 ? (
          <div className="text-center py-20">
            <div className="w-24 h-24 rounded-full bg-gradient-to-br from-slate-100 to-gray-200 flex items-center justify-center mx-auto mb-6">
              <Video className="w-12 h-12 text-slate-400" />
            </div>
            <h3 className="text-xl font-bold text-slate-800 mb-3">
              {searchQuery || statusFilter !== "all" ? "未找到匹配项目" : "暂无项目"}
            </h3>
            <p className="text-slate-600 mb-8 max-w-md mx-auto leading-relaxed">
              {searchQuery || statusFilter !== "all"
                ? "尝试调整搜索条件或筛选器，或者创建一个新项目开始您的创作之旅"
                : "开始创建您的第一个AI短剧作品，让创意在这里绽放"}
            </p>
            <Link href="/create">
              <Button className="group bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 px-8 py-3">
                <Plus className="w-5 h-5 mr-2 group-hover:rotate-90 transition-transform duration-300" />
                开始创作
              </Button>
            </Link>
          </div>
        ) : (
          <div
            className={
              viewMode === "grid" ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6" : "space-y-4"
            }
          >
            {filteredProjects.map((project) => (
              <ProjectCard
                key={project.id}
                project={project}
                viewMode={viewMode}
                onDelete={setDeleteProjectId}
                formatDate={formatDate}
                getStepIcon={getStepIcon}
              />
            ))}
          </div>
        )}
      </div>

      {/* 删除确认对话框 */}
      <AlertDialog open={deleteProjectId !== null} onOpenChange={() => setDeleteProjectId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除项目</AlertDialogTitle>
            <AlertDialogDescription>
              此操作将永久删除该项目及其所有相关数据，无法恢复。确定要继续吗？
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => deleteProjectId && handleDeleteProject(deleteProjectId)}
              className="bg-red-600 hover:bg-red-700"
            >
              删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
