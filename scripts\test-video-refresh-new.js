#!/usr/bin/env node

/**
 * 测试新的视频刷新逻辑
 * 
 * 测试通过项目ID和镜头ID查询最新任务的功能
 */

async function testNewRefreshLogic() {
  console.log('🔄 测试新的视频刷新逻辑');
  console.log('='.repeat(50));
  
  // 从数据库中获取一个现有的任务来测试
  const { PrismaClient } = require('../lib/generated/prisma');
  const prisma = new PrismaClient();
  
  try {
    await prisma.$connect();
    
    // 获取一个有项目ID和镜头ID的任务
    const existingTask = await prisma.video_generation_tasks.findFirst({
      where: {
        AND: [
          { project_id: { not: null } },
          { shot_id: { not: null } }
        ]
      },
      include: {
        generated_videos: true,
        script_projects: true,
        script_shots: true
      }
    });
    
    if (!existingTask) {
      console.log('❌ 没有找到有项目ID和镜头ID的任务，无法测试');
      return;
    }
    
    console.log('📋 找到测试任务:');
    console.log(`   任务ID: ${existingTask.id}`);
    console.log(`   项目ID: ${existingTask.project_id}`);
    console.log(`   镜头ID: ${existingTask.shot_id}`);
    console.log(`   Kling任务ID: ${existingTask.kling_task_id || '无'}`);
    console.log(`   状态: ${existingTask.status}`);
    
    // 测试1: 通过项目ID查询
    console.log('\n🔍 测试1: 通过项目ID查询任务');
    await testQueryByProject(existingTask.project_id);
    
    // 测试2: 通过镜头ID查询
    console.log('\n🔍 测试2: 通过镜头ID查询任务');
    await testQueryByShot(existingTask.shot_id);
    
    // 测试3: 通过Kling任务ID查询
    if (existingTask.kling_task_id) {
      console.log('\n🔍 测试3: 通过Kling任务ID查询任务');
      await testQueryByKlingId(existingTask.kling_task_id);
    }
    
    // 测试4: 组合查询
    console.log('\n🔍 测试4: 组合查询（项目ID + 镜头ID）');
    await testCombinedQuery(existingTask.project_id, existingTask.shot_id);
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

async function testQueryByProject(projectId) {
  try {
    const response = await fetch(`http://localhost:3001/api/save-video-generation?projectId=${projectId}`);
    
    if (response.ok) {
      const result = await response.json();
      console.log(`✅ 项目查询成功: 找到 ${result.tasks?.length || 0} 个任务`);
      if (result.tasks && result.tasks.length > 0) {
        console.log(`   最新任务: ${result.tasks[0].id} (${result.tasks[0].status})`);
      }
    } else {
      const error = await response.json();
      console.log(`❌ 项目查询失败: ${error.error}`);
    }
  } catch (error) {
    console.log(`❌ 项目查询请求失败: ${error.message}`);
  }
}

async function testQueryByShot(shotId) {
  try {
    const response = await fetch(`http://localhost:3001/api/save-video-generation?shotId=${shotId}`);
    
    if (response.ok) {
      const result = await response.json();
      console.log(`✅ 镜头查询成功: 找到 ${result.tasks?.length || 0} 个任务`);
      if (result.tasks && result.tasks.length > 0) {
        console.log(`   最新任务: ${result.tasks[0].id} (${result.tasks[0].status})`);
      }
    } else {
      const error = await response.json();
      console.log(`❌ 镜头查询失败: ${error.error}`);
    }
  } catch (error) {
    console.log(`❌ 镜头查询请求失败: ${error.message}`);
  }
}

async function testQueryByKlingId(klingTaskId) {
  try {
    const response = await fetch(`http://localhost:3001/api/save-video-generation?klingTaskId=${klingTaskId}`);
    
    if (response.ok) {
      const result = await response.json();
      console.log(`✅ Kling任务ID查询成功: 找到任务 ${result.task?.id}`);
      console.log(`   状态: ${result.task?.status}`);
      console.log(`   视频数量: ${result.task?.generated_videos?.length || 0}`);
    } else {
      const error = await response.json();
      console.log(`❌ Kling任务ID查询失败: ${error.error}`);
    }
  } catch (error) {
    console.log(`❌ Kling任务ID查询请求失败: ${error.message}`);
  }
}

async function testCombinedQuery(projectId, shotId) {
  try {
    const response = await fetch(`http://localhost:3001/api/save-video-generation?projectId=${projectId}&shotId=${shotId}`);
    
    if (response.ok) {
      const result = await response.json();
      console.log(`✅ 组合查询成功: 找到 ${result.tasks?.length || 0} 个任务`);
      if (result.tasks && result.tasks.length > 0) {
        console.log(`   最新任务: ${result.tasks[0].id} (${result.tasks[0].status})`);
      }
    } else {
      const error = await response.json();
      console.log(`❌ 组合查询失败: ${error.error}`);
    }
  } catch (error) {
    console.log(`❌ 组合查询请求失败: ${error.message}`);
  }
}

// 测试Kling API状态查询
async function testKlingStatusQuery() {
  console.log('\n🔍 测试Kling API状态查询');
  console.log('-'.repeat(40));
  
  // 获取一个有Kling任务ID的任务
  const { PrismaClient } = require('../lib/generated/prisma');
  const prisma = new PrismaClient();
  
  try {
    await prisma.$connect();
    
    const taskWithKlingId = await prisma.video_generation_tasks.findFirst({
      where: {
        kling_task_id: { not: null }
      }
    });
    
    if (!taskWithKlingId) {
      console.log('❌ 没有找到有Kling任务ID的任务');
      return;
    }
    
    console.log(`📋 测试Kling任务ID: ${taskWithKlingId.kling_task_id}`);
    
    const response = await fetch('http://localhost:3001/api/check-task-status', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ taskId: taskWithKlingId.kling_task_id })
    });
    
    if (response.ok) {
      const result = await response.json();
      console.log(`✅ Kling状态查询成功:`);
      console.log(`   状态: ${result.data?.task_status}`);
      console.log(`   消息: ${result.data?.task_status_msg || '无'}`);
    } else {
      const error = await response.json();
      console.log(`❌ Kling状态查询失败: ${error.error}`);
    }
    
  } catch (error) {
    console.error('❌ Kling状态查询测试失败:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行所有测试
async function runAllTests() {
  await testNewRefreshLogic();
  await testKlingStatusQuery();
  console.log('\n✅ 所有测试完成！');
}

runAllTests().catch(console.error);
