# 视频状态缓存实现说明

## 概述
为了避免重复查询视频生成状态，在 `VideoGenerationStep.tsx` 组件中实现了视频状态缓存机制。

## 主要功能

### 1. 缓存数据结构
- 使用 `Map<string, any>` 存储缓存数据
- 键：镜头ID (shotId)
- 值：包含状态信息、缓存时间和过期时间的对象

### 2. 缓存策略
- **已完成状态 (completed)**: 缓存5分钟
- **失败状态 (failed)**: 缓存5分钟
- **处理中状态 (processing)**: 缓存1分钟（较短时间，因为状态可能快速变化）

### 3. 自动缓存已有视频 🆕
- **页面加载时自动检测**：组件初始化时自动扫描所有场景
- **智能缓存策略**：只缓存有视频但未缓存的镜头
- **避免重复查询**：已有视频的镜头在自动刷新时会被跳过
- **实时缓存更新**：新生成的视频立即加入缓存

### 4. 缓存检查逻辑
在以下场景中会检查缓存：
- 自动刷新所有镜头状态时
- 手动刷新单个镜头状态时
- 批量处理processing状态任务时
- 页面加载时检查已有视频 🆕

### 5. 缓存更新时机
缓存会在以下情况下更新：
- 页面加载时发现已有视频 🆕
- 视频生成成功后立即缓存 🆕
- 数据库查询返回已完成的任务
- Kling API查询返回最新状态
- 任务状态发生变化时

### 6. 缓存清理
- 每分钟自动清理过期的缓存条目
- 组件重新加载时清空所有缓存

## 用户界面改进

### 缓存状态指示器
在场景卡片中显示缓存状态：
- 显示为黄色徽章，格式：💾 {秒数}s
- 鼠标悬停显示详细信息（状态和缓存时间）

### 缓存优化的用户体验
- 手动刷新时，如果缓存有效，直接显示缓存结果
- 自动刷新时，跳过已缓存的镜头，减少API调用
- 在控制台显示缓存使用情况

## 调试功能

### 开发环境调试
在开发环境下，可以使用以下方法查看缓存状态：
```javascript
// 在浏览器控制台中执行
window.logVideoStatusCache()
```

### 控制台日志
- `💾 [AUTO-REFRESH] 使用缓存数据` - 使用缓存时的日志
- `💾 [CACHE-DEBUG]` - 缓存调试相关日志
- 显示缓存条目数量和状态

## 性能优化效果

### 减少API调用
- 避免重复查询已知状态的镜头
- 特别是在页面刷新或重新进入时
- 减少对数据库和Kling API的压力

### 提升响应速度
- 缓存命中时立即返回结果
- 减少用户等待时间
- 改善整体用户体验

## 实现细节

### 核心函数
- `getCachedVideoStatus(shotId)` - 获取缓存状态
- `updateVideoStatusCache(shotId, status)` - 更新缓存
- `cleanExpiredCache()` - 清理过期缓存
- `isVideoStatusCacheValid(scene)` - 检查缓存有效性
- `cacheExistingVideoStatus()` - 智能缓存已有视频状态 🆕
- `forceCacheAllExistingVideos()` - 强制缓存所有已有视频 🆕

### 缓存数据格式
```javascript
{
  status: 'completed' | 'processing' | 'failed',
  taskId: string,
  videos?: Array, // 仅completed状态
  error?: string, // 仅failed状态
  cachedAt: number, // 缓存时间戳
  expiresAt: number // 过期时间戳
}
```

## 注意事项

1. **缓存一致性**: 缓存可能与实际状态有短暂延迟，这是可接受的权衡
2. **内存使用**: 缓存数据存储在内存中，页面刷新会清空
3. **过期策略**: 不同状态使用不同的过期时间，平衡性能和准确性
4. **错误处理**: 缓存失败不影响正常功能，会回退到直接查询

## 未来改进方向

1. **持久化缓存**: 考虑使用 localStorage 实现跨页面缓存
2. **智能刷新**: 根据任务创建时间智能调整缓存策略
3. **缓存统计**: 添加缓存命中率统计
4. **用户控制**: 允许用户手动清空缓存或禁用缓存
