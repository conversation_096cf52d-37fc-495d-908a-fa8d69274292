import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET /api/projects/[id] - 获取特定项目的详细信息
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const projectId = params.id

    const project = await prisma.script_projects.findUnique({
      where: {
        id: projectId
      },
      include: {
        script_characters: {
          orderBy: {
            sort_order: 'asc'
          }
        },
        script_shots: {
          orderBy: {
            shot_number: 'asc'
          },
          include: {
            image_generation_tasks: {
              include: {
                generated_images: {
                  orderBy: {
                    created_at: 'desc'
                  }
                }
              },
              orderBy: {
                created_at: 'desc'
              }
            }
          }
        },
        script_generation_configs: true,
        image_generation_tasks: {
          include: {
            generated_images: {
              orderBy: {
                created_at: 'desc'
              }
            }
          },
          orderBy: {
            created_at: 'desc'
          }
        }
      }
    })

    if (!project) {
      return NextResponse.json({
        success: false,
        error: '项目不存在'
      }, { status: 404 })
    }

    // 获取所有生成的图片
    const allImages = project.image_generation_tasks
      .flatMap(task => task.generated_images)
      .map(image => ({
        id: image.id,
        url: image.cdn_url || image.image_url,
        filename: image.image_filename,
        width: image.image_width,
        height: image.image_height,
        format: image.image_format,
        isPrimary: image.is_primary,
        createdAt: image.created_at,
        taskId: image.task_id,
        actualPrompt: image.actual_prompt,
        qualityScore: image.quality_score
      }))
      .sort((a, b) => new Date(b.createdAt || 0).getTime() - new Date(a.createdAt || 0).getTime())

    // 按场景组织图片
    const sceneImages = project.script_shots.map(shot => ({
      shotNumber: shot.shot_number,
      location: shot.location,
      action: shot.action,
      dialogue: shot.dialogue,
      images: shot.image_generation_tasks
        .flatMap(task => task.generated_images)
        .map(image => ({
          id: image.id,
          url: image.cdn_url || image.image_url,
          filename: image.image_filename,
          isPrimary: image.is_primary,
          createdAt: image.created_at,
          actualPrompt: image.actual_prompt
        }))
        .sort((a, b) => new Date(b.createdAt || 0).getTime() - new Date(a.createdAt || 0).getTime())
    }))

    const response = {
      success: true,
      project: {
        id: project.id,
        title: project.title,
        description: project.description,
        status: project.status,
        createdAt: project.created_at,
        updatedAt: project.updated_at,
        duration: project.duration,
        style: project.style,
        creationMode: project.creation_mode,
        scriptContent: project.script_content,
        scriptData: project.script_data,
        characters: project.script_characters,
        shots: project.script_shots,
        generationConfigs: project.script_generation_configs,
        allImages,
        sceneImages,
        imageCount: allImages.length,
        completedScenes: sceneImages.filter(scene => scene.images.length > 0).length
      }
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('Error fetching project details:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '获取项目详情时发生未知错误'
    }, { status: 500 })
  }
}

// DELETE /api/projects/[id] - 删除项目
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const projectId = params.id

    // 检查项目是否存在
    const project = await prisma.script_projects.findUnique({
      where: { id: projectId }
    })

    if (!project) {
      return NextResponse.json({
        success: false,
        error: '项目不存在'
      }, { status: 404 })
    }

    // 删除相关数据（由于外键约束，需要按顺序删除）
    await prisma.$transaction(async (tx) => {
      // 删除生成的图片
      await tx.generated_images.deleteMany({
        where: {
          image_generation_tasks: {
            project_id: projectId
          }
        }
      })

      // 删除图片生成任务
      await tx.image_generation_tasks.deleteMany({
        where: { project_id: projectId }
      })

      // 删除剧本镜头
      await tx.script_shots.deleteMany({
        where: { project_id: projectId }
      })

      // 删除角色
      await tx.script_characters.deleteMany({
        where: { project_id: projectId }
      })

      // 删除生成配置
      await tx.script_generation_configs.deleteMany({
        where: { project_id: projectId }
      })

      // 最后删除项目
      await tx.script_projects.delete({
        where: { id: projectId }
      })
    })

    return NextResponse.json({
      success: true,
      message: '项目删除成功'
    })

  } catch (error) {
    console.error('Error deleting project:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '删除项目时发生未知错误'
    }, { status: 500 })
  }
}
