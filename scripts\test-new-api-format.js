// 加载环境变量
require('dotenv').config({ path: '.env.local' });
require('dotenv').config(); // 备用加载 .env 文件

const { KlingAIClient } = require('../lib/kling-ai.ts');

// 测试新的API格式
async function testNewAPIFormat() {
  console.log('🚀 [NEW-API-TEST] 测试新的 Kling AI API 格式');
  console.log('='.repeat(60));

  // 检查环境变量
  const accessKey = process.env.KLING_ACCESS_KEY;
  const secretKey = process.env.KLING_SECRET_KEY;

  if (!accessKey || !secretKey) {
    console.error('❌ [NEW-API-TEST] 缺少必需的环境变量:');
    console.error('   KLING_ACCESS_KEY:', accessKey ? '已设置' : '未设置');
    console.error('   KLING_SECRET_KEY:', secretKey ? '已设置' : '未设置');
    console.error('\n请在 .env.local 文件中设置这些变量');
    process.exit(1);
  }

  try {
    // 初始化客户端
    const klingClient = new KlingAIClient({ accessKey, secretKey });

    // 测试1: 查询视频任务列表（新格式）
    console.log('\n📝 [TEST-1] 测试视频任务列表查询（新API格式）');
    console.log('URL格式: /v1/videos/image2video/?pageNum=1&pageSize=10');
    
    try {
      const taskList = await klingClient.getVideoTaskList({
        pageNum: 1,
        pageSize: 10
      });

      console.log('✅ [TEST-1] 视频任务列表查询成功');
      console.log('📊 [TEST-1] 返回码:', taskList.code);
      console.log('📊 [TEST-1] 消息:', taskList.message || '无');
      
      if (taskList.data && taskList.data.tasks) {
        console.log('📊 [TEST-1] 任务数量:', taskList.data.tasks.length);
        console.log('📊 [TEST-1] 总数:', taskList.data.total || '未提供');
        
        // 显示前3个任务的基本信息
        const tasksToShow = taskList.data.tasks.slice(0, 3);
        tasksToShow.forEach((task, index) => {
          console.log(`📋 [TASK-${index + 1}] ID: ${task.task_id}`);
          console.log(`📋 [TASK-${index + 1}] 状态: ${task.task_status}`);
          console.log(`📋 [TASK-${index + 1}] 创建时间: ${new Date(task.created_at * 1000).toLocaleString()}`);
        });

        // 测试2: 查询单个视频任务详情（新格式）
        if (taskList.data.tasks.length > 0) {
          const firstTaskId = taskList.data.tasks[0].task_id;
          console.log(`\n📝 [TEST-2] 测试视频任务详情查询（新API格式）`);
          console.log(`URL格式: /v1/videos/image2video/${firstTaskId}`);
          
          try {
            const taskDetail = await klingClient.getTaskStatus(firstTaskId, 'video');
            
            console.log('✅ [TEST-2] 视频任务详情查询成功');
            console.log('📊 [TEST-2] 返回码:', taskDetail.code);
            console.log('📊 [TEST-2] 任务ID:', taskDetail.data.task_id);
            console.log('📊 [TEST-2] 状态:', taskDetail.data.task_status);
            console.log('📊 [TEST-2] 状态消息:', taskDetail.data.task_status_msg || '无');
            
            if (taskDetail.data.task_result && taskDetail.data.task_result.videos) {
              console.log('📊 [TEST-2] 生成视频数量:', taskDetail.data.task_result.videos.length);
              taskDetail.data.task_result.videos.forEach((video, index) => {
                console.log(`🎬 [VIDEO-${index + 1}] URL: ${video.url}`);
              });
            }
            
          } catch (detailError) {
            console.error('❌ [TEST-2] 视频任务详情查询失败:', detailError.message);
          }
        }

      } else {
        console.log('📊 [TEST-1] 没有找到任务数据');
      }

    } catch (listError) {
      console.error('❌ [TEST-1] 视频任务列表查询失败:', listError.message);
    }

    // 测试3: 查询特定状态的任务
    console.log('\n📝 [TEST-3] 测试按状态筛选（成功任务）');
    console.log('URL格式: /v1/videos/image2video/?status=succeed&pageNum=1&pageSize=5');
    
    try {
      const successTasks = await klingClient.getVideoTaskList({
        status: 'succeed',
        pageNum: 1,
        pageSize: 5
      });

      console.log('✅ [TEST-3] 成功任务查询成功');
      console.log('📊 [TEST-3] 返回码:', successTasks.code);
      
      if (successTasks.data && successTasks.data.tasks) {
        console.log('📊 [TEST-3] 成功任务数量:', successTasks.data.tasks.length);
        
        successTasks.data.tasks.forEach((task, index) => {
          console.log(`✅ [SUCCESS-${index + 1}] ${task.task_id} - ${new Date(task.created_at * 1000).toLocaleString()}`);
        });
      }

    } catch (successError) {
      console.error('❌ [TEST-3] 成功任务查询失败:', successError.message);
    }

    // 测试4: 对比新旧API格式
    console.log('\n📝 [TEST-4] API格式对比总结');
    console.log('='.repeat(50));
    console.log('🔄 旧格式:');
    console.log('   任务列表: /v1/videos/image2video/tasks?page=1&size=10');
    console.log('   任务详情: /v1/videos/image2video/tasks/{taskId}');
    console.log('');
    console.log('✅ 新格式:');
    console.log('   任务列表: /v1/videos/image2video/?pageNum=1&pageSize=10');
    console.log('   任务详情: /v1/videos/image2video/{taskId}');
    console.log('');
    console.log('📋 参数变化:');
    console.log('   page → pageNum');
    console.log('   size → pageSize');
    console.log('   移除了 /tasks 路径段');

    console.log('\n✅ [NEW-API-TEST] 新API格式测试完成!');

  } catch (error) {
    console.error('❌ [NEW-API-TEST] 测试失败:', error.message);
    console.error('❌ [NEW-API-TEST] 错误详情:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testNewAPIFormat();
}

module.exports = {
  testNewAPIFormat
};
