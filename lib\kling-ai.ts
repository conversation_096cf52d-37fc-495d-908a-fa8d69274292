interface KlingAIConfig {
  accessKey: string
  secretKey: string
  baseUrl?: string
}

interface GenerateImageParams {
  prompt: string
  negativePrompt?: string
  image?: string
  imageReference?: 'subject' | 'face'
  imageFidelity?: number
  humanFidelity?: number
  n?: number
  aspectRatio?: string
  modelName?: 'kling-v1' | 'kling-v1-5' | 'kling-v2'
  callbackUrl?: string
}

interface GenerateVideoParams {
  prompt: string
  negativePrompt?: string
  image?: string
  imageReference?: 'subject' | 'face'
  imageFidelity?: number
  duration?: number
  aspectRatio?: string
  mode?: 'std' | 'pro'
  modelName?: 'kling-v1' | 'kling-v1-5' | 'kling-v1-6' | 'kling-v2-master' | 'kling-v2-1' | 'kling-v2-1-master'
  callbackUrl?: string
}

interface KlingAITaskResponse {
  code: number
  data: {
    task_id: string
    task_status: 'submitted' | 'processing' | 'succeed' | 'failed'
    task_result?: {
      images?: Array<{
        index: number
        url: string
      }>
      videos?: Array<{
        index: number
        url: string
      }>
    }
    task_info?: any
    task_status_msg?: string
    created_at?: number
    updated_at?: number
  }
  message?: string
  request_id?: string
}

export class KlingAIClient {
  private accessKey: string
  private secretKey: string
  private baseUrl: string

  constructor(config: KlingAIConfig) {
    this.accessKey = config.accessKey
    this.secretKey = config.secretKey
    this.baseUrl = config.baseUrl || 'https://api-beijing.klingai.com'
  }

  private encodeJWTToken(): string {
    // 确保我们在Node.js环境中运行
    if (typeof window !== 'undefined') {
      throw new Error('KlingAIClient can only be used server-side')
    }

    const jwt = require('jsonwebtoken')
    
    const headers = {
      alg: "HS256",
      typ: "JWT"
    }
    
    const payload = {
      iss: this.accessKey,
      exp: Math.floor(Date.now() / 1000) + 1800, // 有效时间30分钟
      nbf: Math.floor(Date.now() / 1000) - 5 // 开始生效时间（当前时间-5秒）
    }
    
    const token = jwt.sign(payload, this.secretKey, { 
      algorithm: 'HS256',
      header: headers
    })
    
    return token
  }

  async generateImage(params: GenerateImageParams): Promise<string> {
    const jwtToken = this.encodeJWTToken()
    const url = `${this.baseUrl}/v1/images/generations`

    const modelName = params.modelName || 'kling-v2'

    console.log('🎨 [KlingAI] ===== 图片生成请求开始 =====')
    console.log('🎨 [KlingAI] 输入参数:', JSON.stringify(params, null, 2))
    console.log('🎨 [KlingAI] 使用模型:', modelName)
    console.log('🎨 [KlingAI] API URL:', url)

    const body: any = {
      model_name: modelName,
      prompt: params.prompt,
      negative_prompt: params.negativePrompt || null,
      image: params.image || null,
      n: params.n || 1,
      aspect_ratio: params.aspectRatio || '16:9',
      callback_url: params.callbackUrl || null,
    }

    // For kling-v2, exclude fidelity parameters and image_reference
    if (modelName !== 'kling-v2') {
      body.image_reference = params.imageReference || null
      body.image_fidelity = params.imageFidelity || 0.5
      body.human_fidelity = params.humanFidelity || 0.45
      console.log('🎨 [KlingAI] 包含 fidelity 参数 (非 kling-v2 模型)')
      console.log('🎨 [KlingAI] - image_reference:', params.imageReference || null)
      console.log('🎨 [KlingAI] - image_fidelity:', params.imageFidelity || 0.5)
      console.log('🎨 [KlingAI] - human_fidelity:', params.humanFidelity || 0.45)
    } else {
      console.log('🎨 [KlingAI] 跳过 fidelity 和 image_reference 参数 (kling-v2 模型)')
    }

    console.log('🎨 [KlingAI] 最终请求体:', JSON.stringify(body, null, 2))

    console.log('🎨 [KlingAI] 发送请求到 Kling AI API...')

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${jwtToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    })

    console.log('🎨 [KlingAI] API 响应状态:', response.status, response.statusText)

    if (!response.ok) {
      const errorText = await response.text()
      console.error('❌ [KlingAI] API 错误响应:', errorText)
      throw new Error(`Kling AI API error: ${response.status} - ${errorText}`)
    }

    const data: KlingAITaskResponse = await response.json()
    console.log('🎨 [KlingAI] API 响应数据:', JSON.stringify(data, null, 2))

    if (data.code !== 0) {
      console.error('❌ [KlingAI] API 返回错误代码:', data.code, '错误信息:', data.message)
      throw new Error(`Kling AI API error: ${data.message || 'Unknown error'} (Code: ${data.code})`)
    }

    console.log('✅ [KlingAI] 图片生成任务创建成功, Task ID:', data.data.task_id)
    console.log('🎨 [KlingAI] ===== 图片生成请求结束 =====')

    return data.data.task_id
  }

  async generateVideo(params: GenerateVideoParams): Promise<string> {
    const jwtToken = this.encodeJWTToken()
    const url = `${this.baseUrl}/v1/videos/image2video`

    const modelName = params.modelName || 'kling-v1'

    console.log('🎬 [KlingAI] ===== 视频生成请求开始 =====')
    console.log('🎬 [KlingAI] 输入参数:', JSON.stringify(params, null, 2))
    console.log('🎬 [KlingAI] 使用模型:', modelName)
    console.log('🎬 [KlingAI] API URL:', url)

    const body: any = {
      model_name: modelName,
      prompt: params.prompt,
      negative_prompt: params.negativePrompt || null,
      image: params.image || null,
      image_reference: params.imageReference || 'subject',
      image_fidelity: params.imageFidelity || 0.5,
      duration: params.duration || 5,
      aspect_ratio: params.aspectRatio || '16:9',
      mode: params.mode || 'std',
      callback_url: params.callbackUrl || null,
    }

    // 移除空值
    Object.keys(body).forEach(key => {
      if (body[key] === null || body[key] === undefined) {
        delete body[key]
      }
    })

    console.log('🎬 [KlingAI] 请求体:', JSON.stringify(body, null, 2))

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${jwtToken}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(body),
    })

    console.log('🎬 [KlingAI] HTTP 状态码:', response.status)
    console.log('🎬 [KlingAI] HTTP 状态文本:', response.statusText)

    if (!response.ok) {
      const errorText = await response.text()
      console.error('❌ [KlingAI] HTTP 请求失败:', errorText)
      throw new Error(`HTTP ${response.status}: ${errorText}`)
    }

    const data: KlingAITaskResponse = await response.json()
    console.log('🎬 [KlingAI] API 响应数据:', JSON.stringify(data, null, 2))

    if (data.code !== 0) {
      console.error('❌ [KlingAI] API 返回错误代码:', data.code, '错误信息:', data.message)
      throw new Error(`Kling AI API error: ${data.message || 'Unknown error'} (Code: ${data.code})`)
    }

    console.log('✅ [KlingAI] 视频生成任务创建成功, Task ID:', data.data.task_id)
    console.log('🎬 [KlingAI] ===== 视频生成请求结束 =====')

    return data.data.task_id
  }

  async getTaskStatus(taskId: string, taskType: 'image' | 'video' = 'image'): Promise<KlingAITaskResponse> {
    const jwtToken = this.encodeJWTToken()

    // 根据任务类型构建不同的URL
    let url: string
    if (taskType === 'video') {
      url = `${this.baseUrl}/v1/videos/image2video/${taskId}`
    } else {
      url = `${this.baseUrl}/v1/images/generations/${taskId}`
    }

    console.log(`🔍 [KlingAI] 查询${taskType === 'video' ? '视频' : '图片'}任务状态: ${taskId}`)
    console.log(`🔍 [KlingAI] URL: ${url}`)

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${jwtToken}`,
      },
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error(`❌ [KlingAI] 查询任务状态失败: ${response.status} ${response.statusText}`)
      console.error(`❌ [KlingAI] 错误详情: ${errorText}`)
      throw new Error(`Kling AI API error: ${response.status} ${response.statusText} - ${errorText}`)
    }

    const result = await response.json()
    console.log(`✅ [KlingAI] 任务状态查询成功: ${result.data?.task_status || 'unknown'}`)

    return result
  }

  async getVideoTaskList(options: {
    status?: string
    pageNum?: number
    pageSize?: number
    start_time?: string
    end_time?: string
  } = {}): Promise<any> {
    const jwtToken = this.encodeJWTToken()

    // 构建查询参数
    const params = new URLSearchParams()

    if (options.status) params.append('status', options.status)
    if (options.pageNum) params.append('pageNum', options.pageNum.toString())
    if (options.pageSize) params.append('pageSize', options.pageSize.toString())
    if (options.start_time) params.append('start_time', options.start_time)
    if (options.end_time) params.append('end_time', options.end_time)

    const url = `${this.baseUrl}/v1/videos/image2video?${params.toString()}`

    console.log('🔍 [KlingAI] 查询视频任务列表...')
    console.log('🔍 [KlingAI] URL:', url)
    console.log('🔍 [KlingAI] 查询参数:', Object.fromEntries(params))

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${jwtToken}`,
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('❌ [KlingAI] 查询视频任务列表失败:', errorText)
      throw new Error(`Kling AI API error: ${response.status} ${response.statusText} - ${errorText}`)
    }

    const result = await response.json()
    console.log('✅ [KlingAI] 视频任务列表查询成功')

    return result
  }

  async waitForCompletion(taskId: string, timeoutMs: number = 60000): Promise<string[]> {
    const startTime = Date.now()

    while (Date.now() - startTime < timeoutMs) {
      const result = await this.getTaskStatus(taskId)

      if (result.data.task_status === 'succeed' && result.data.task_result?.images) {
        return result.data.task_result.images.map(img => img.url)
      }

      if (result.data.task_status === 'failed') {
        throw new Error('Image generation failed')
      }

      await new Promise(resolve => setTimeout(resolve, 2000))
    }

    throw new Error('Image generation timeout')
  }
}

export function mapStyleToKlingStyle(style: string): string {
  const styleMap: Record<string, string> = {
    'realistic': 'realistic',
    'anime': 'anime',
    'cartoon': 'cartoon',
    'oil': 'oil painting',
    'watercolor': 'watercolor',
    'sketch': 'sketch',
    'cyberpunk': 'cyberpunk',
    'fantasy': 'fantasy',
  }
  
  return styleMap[style] || 'realistic'
}

export function mapSizeToAspectRatio(size: string): string {
  const sizeMap: Record<string, string> = {
    '512x512': '1:1',
    '768x768': '1:1',
    '1024x1024': '1:1',
    '1024x768': '4:3',
    '768x1024': '3:4',
    '1280x720': '16:9',
    '720x1280': '9:16',
  }
  
  return sizeMap[size] || '16:9'
}