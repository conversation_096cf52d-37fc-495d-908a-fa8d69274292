-- 添加 human_fidelity 字段到 image_generation_tasks 表
-- 这个迁移脚本用于更新现有数据库以支持可灵AI的人物保真度参数

-- 添加 human_fidelity 字段
ALTER TABLE public.image_generation_tasks 
ADD COLUMN human_fidelity numeric DEFAULT 0.45;

-- 添加约束检查
ALTER TABLE public.image_generation_tasks 
ADD CONSTRAINT image_generation_tasks_human_fidelity_check 
CHECK (human_fidelity >= 0 AND human_fidelity <= 1);

-- 更新现有记录的 human_fidelity 值为默认值
UPDATE public.image_generation_tasks 
SET human_fidelity = 0.45 
WHERE human_fidelity IS NULL;

-- 添加注释
COMMENT ON COLUMN public.image_generation_tasks.human_fidelity IS '人物保真度参数，控制人物特征的保真度和一致性，范围0-1';
