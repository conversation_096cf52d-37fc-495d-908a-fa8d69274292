// 此文件已弃用，请使用 Next.js API routes 代替
// 相关API路由: /api/scripts, /api/scripts/save, /api/scripts/[id]
// 使用示例: fetch('/api/scripts/save', { method: 'POST', body: JSON.stringify(data) })

import { PrismaClient } from '@/lib/generated/prisma'

const prisma = new PrismaClient()

export interface ScriptShot {
  shotNumber: number
  duration: number
  shotType: string
  location: string
  characters: string[]
  action: string
  dialogue?: string
  cameraMovement: string
  lighting: string
  props?: string[]
  mood: string
  soundEffect: string
  transition: string
}

export interface ScriptData {
  title: string
  totalDuration: number
  style: string
  shotCount: number
  shots: ScriptShot[]
}

export interface SaveScriptParams {
  userId: string
  title: string
  description?: string
  creationMode: 'keywords' | 'template' | 'free'
  style: string
  duration: number
  scriptContent: string
  scriptData: ScriptData
  generationConfig?: {
    theme?: string
    selectedTags?: string[]
    customKeywords?: string
    selectedMood?: string
    selectedTemplate?: string
    characters?: Array<{
      name: string
      role: string
      background: string
    }>
    backgroundSetting?: string
    character1?: string
    character2?: string
    supportingCharacter?: string
    requiredScenes?: string
    avoidElements?: string
  }
}

export async function saveScriptToPrisma(params: SaveScriptParams) {
  try {
    console.log("Saving script with Prisma...")
    
    // 1. 创建剧本项目
    const project = await prisma.script_projects.create({
      data: {
        user_id: params.userId,
        title: params.title,
        description: params.description || '',
        creation_mode: params.creationMode,
        style: params.style,
        duration: params.duration,
        script_content: params.scriptContent,
        script_data: params.scriptData as any, // Prisma JSON field
        status: 'draft'
      }
    })

    const projectId = project.id

    // 2. 保存生成配置
    if (params.generationConfig) {
      const config = params.generationConfig
      await prisma.script_generation_configs.create({
        data: {
          project_id: projectId,
          theme: config.theme,
          selected_tags: config.selectedTags || [],
          custom_keywords: config.customKeywords,
          selected_mood: config.selectedMood,
          selected_template: config.selectedTemplate,
          background_setting: config.backgroundSetting,
          character1: config.character1,
          character2: config.character2,
          supporting_character: config.supportingCharacter,
          required_scenes: config.requiredScenes,
          avoid_elements: config.avoidElements
        }
      })
    }

    // 3. 保存角色信息
    if (params.generationConfig?.characters) {
      const charactersData = params.generationConfig.characters.map((char, index) => ({
        project_id: projectId,
        name: char.name,
        role: char.role,
        background: char.background,
        sort_order: index
      }))
      
      if (charactersData.length > 0) {
        await prisma.script_characters.createMany({
          data: charactersData
        })
      }
    }

    // 4. 保存分镜头信息
    const shotsData = params.scriptData.shots.map((shot, index) => ({
      project_id: projectId,
      shot_number: shot.shotNumber,
      duration: shot.duration,
      shot_type: shot.shotType,
      location: shot.location,
      characters: shot.characters,
      action: shot.action,
      dialogue: shot.dialogue || null,
      camera_movement: shot.cameraMovement,
      lighting: shot.lighting,
      props: shot.props || [],
      mood: shot.mood,
      sound_effect: shot.soundEffect,
      transition: shot.transition
    }))

    await prisma.script_shots.createMany({
      data: shotsData
    })

    return {
      success: true,
      projectId,
      project
    }
  } catch (error) {
    console.error('Error saving script to Prisma:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }
  }
}

export async function getUserScripts(userId: string) {
  try {
    const scripts = await prisma.script_projects.findMany({
      where: {
        user_id: userId
      },
      include: {
        script_characters: true,
        script_shots: {
          orderBy: {
            shot_number: 'asc'
          }
        },
        script_generation_configs: true
      },
      orderBy: {
        created_at: 'desc'
      }
    })

    return {
      success: true,
      scripts
    }
  } catch (error) {
    console.error('Error fetching user scripts:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }
  }
}

export async function getScriptById(projectId: string) {
  try {
    const script = await prisma.script_projects.findUnique({
      where: {
        id: projectId
      },
      include: {
        script_characters: {
          orderBy: {
            sort_order: 'asc'
          }
        },
        script_shots: {
          orderBy: {
            shot_number: 'asc'
          }
        },
        script_generation_configs: true
      }
    })

    if (!script) {
      throw new Error('Script not found')
    }

    return {
      success: true,
      script
    }
  } catch (error) {
    console.error('Error fetching script:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }
  }
}

export async function updateScript(projectId: string, updates: Partial<SaveScriptParams>) {
  try {
    const script = await prisma.script_projects.update({
      where: {
        id: projectId
      },
      data: {
        title: updates.title,
        description: updates.description,
        script_content: updates.scriptContent,
        script_data: updates.scriptData as any,
        updated_at: new Date()
      }
    })

    return {
      success: true,
      script
    }
  } catch (error) {
    console.error('Error updating script:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }
  }
}

export async function deleteScript(projectId: string) {
  try {
    // 使用事务确保数据一致性
    await prisma.$transaction(async (tx) => {
      // 删除相关记录（由于外键约束，顺序很重要）
      await tx.script_shots.deleteMany({
        where: { project_id: projectId }
      })
      
      await tx.script_characters.deleteMany({
        where: { project_id: projectId }
      })
      
      await tx.script_generation_configs.deleteMany({
        where: { project_id: projectId }
      })
      
      // 删除项目
      await tx.script_projects.delete({
        where: { id: projectId }
      })
    })

    return {
      success: true
    }
  } catch (error) {
    console.error('Error deleting script:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }
  }
}