#!/usr/bin/env node

/**
 * 生成Kling AI API的curl命令
 * 
 * 用法:
 *   node scripts/generate-curl.js list
 *   node scripts/generate-curl.js task <task_id>
 *   node scripts/generate-curl.js external <external_task_id>
 */

// 加载环境变量
require('dotenv').config({ path: '.env.local' });
require('dotenv').config();

const jwt = require('jsonwebtoken');

// 生成JWT Token
function generateJWTToken() {
  const accessKey = process.env.KLING_ACCESS_KEY;
  const secretKey = process.env.KLING_SECRET_KEY;

  if (!accessKey || !secretKey) {
    console.error('❌ 请设置 KLING_ACCESS_KEY 和 KLING_SECRET_KEY 环境变量');
    process.exit(1);
  }

  const token = jwt.sign(
    {
      iss: accessKey,
      exp: Math.floor(Date.now() / 1000) + 1800, // 30分钟有效期
      nbf: Math.floor(Date.now() / 1000) - 5     // 5秒前开始生效
    },
    secretKey,
    { algorithm: 'HS256' }
  );

  return token;
}

// 生成任务列表查询的curl命令
function generateListCurl() {
  const token = generateJWTToken();
  
  console.log('🔍 查询任务列表的curl命令:');
  console.log('='.repeat(60));
  
  // 基础查询
  console.log('\n📋 基础查询（最新10个任务）:');
  console.log(`curl -X GET "https://api-beijing.klingai.com/v1/videos/image2video?pageNum=1&pageSize=10" \\`);
  console.log(`  -H "Authorization: Bearer ${token}" \\`);
  console.log(`  -H "Content-Type: application/json"`);

  // 按状态查询
  console.log('\n✅ 查询成功任务:');
  console.log(`curl -X GET "https://api-beijing.klingai.com/v1/videos/image2video?status=succeed&pageNum=1&pageSize=10" \\`);
  console.log(`  -H "Authorization: Bearer ${token}" \\`);
  console.log(`  -H "Content-Type: application/json"`);

  // 查询失败任务
  console.log('\n❌ 查询失败任务:');
  console.log(`curl -X GET "https://api-beijing.klingai.com/v1/videos/image2video?status=failed&pageNum=1&pageSize=10" \\`);
  console.log(`  -H "Authorization: Bearer ${token}" \\`);
  console.log(`  -H "Content-Type: application/json"`);

  // 查询处理中任务
  console.log('\n⏳ 查询处理中任务:');
  console.log(`curl -X GET "https://api-beijing.klingai.com/v1/videos/image2video?status=processing&pageNum=1&pageSize=10" \\`);
  console.log(`  -H "Authorization: Bearer ${token}" \\`);
  console.log(`  -H "Content-Type: application/json"`);
  
  console.log('\n='.repeat(60));
}

// 生成单任务查询的curl命令
function generateTaskCurl(taskId) {
  const token = generateJWTToken();
  
  console.log(`🔍 查询任务详情的curl命令 (ID: ${taskId}):`);
  console.log('='.repeat(60));
  
  console.log(`curl -X GET "https://api-beijing.klingai.com/v1/videos/image2video/${taskId}" \\`);
  console.log(`  -H "Authorization: Bearer ${token}" \\`);
  console.log(`  -H "Content-Type: application/json" \\`);
  console.log(`  -v`);
  
  console.log('\n='.repeat(60));
}

// 生成外部任务ID查询的curl命令
function generateExternalTaskCurl(externalTaskId) {
  const token = generateJWTToken();
  
  console.log(`🔍 查询外部任务详情的curl命令 (External ID: ${externalTaskId}):`);
  console.log('='.repeat(60));
  
  console.log(`curl -X GET "https://api-beijing.klingai.com/v1/videos/image2video?external_task_id=${encodeURIComponent(externalTaskId)}" \\`);
  console.log(`  -H "Authorization: Bearer ${token}" \\`);
  console.log(`  -H "Content-Type: application/json" \\`);
  console.log(`  -v`);
  
  console.log('\n='.repeat(60));
}

// 显示帮助信息
function showHelp() {
  console.log(`
🛠️ Kling AI curl命令生成器

用法:
  node scripts/generate-curl.js list                    # 生成任务列表查询命令
  node scripts/generate-curl.js task <task_id>          # 生成任务详情查询命令
  node scripts/generate-curl.js external <external_id>  # 生成外部任务ID查询命令

示例:
  node scripts/generate-curl.js list
  node scripts/generate-curl.js task task_12345
  node scripts/generate-curl.js external my_custom_id_123

环境变量:
  KLING_ACCESS_KEY          Kling AI Access Key
  KLING_SECRET_KEY          Kling AI Secret Key

注意:
  - 生成的JWT Token有效期为30分钟
  - 请确保在有效期内使用生成的curl命令
  - 添加 -v 参数可以看到详细的请求和响应信息
`);
}

// 主函数
function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0 || args[0] === '--help' || args[0] === '-h') {
    showHelp();
    return;
  }
  
  const command = args[0];
  
  try {
    switch (command) {
      case 'list':
        generateListCurl();
        break;
        
      case 'task':
        if (!args[1]) {
          console.error('❌ 错误: 请提供任务ID');
          console.error('用法: node scripts/generate-curl.js task <task_id>');
          process.exit(1);
        }
        generateTaskCurl(args[1]);
        break;
        
      case 'external':
        if (!args[1]) {
          console.error('❌ 错误: 请提供外部任务ID');
          console.error('用法: node scripts/generate-curl.js external <external_task_id>');
          process.exit(1);
        }
        generateExternalTaskCurl(args[1]);
        break;
        
      default:
        console.error(`❌ 错误: 未知命令 "${command}"`);
        console.error('支持的命令: list, task, external');
        console.error('使用 --help 查看详细帮助');
        process.exit(1);
    }
    
    console.log('\n💡 提示:');
    console.log('- JWT Token有效期为30分钟');
    console.log('- 复制命令到终端执行即可');
    console.log('- 添加 | jq 可以格式化JSON输出');
    
  } catch (error) {
    console.error('❌ 生成curl命令失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  generateListCurl,
  generateTaskCurl,
  generateExternalTaskCurl,
  generateJWTToken
};
