"use client"

import { useState } from "react"
import React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Slider } from "@/components/ui/slider"
import {
  Video,
  Play,
  Pause,
  RefreshCw,
  Upload,
  Save,
  Download,
  Settings,
  Clock,
  Zap,
  Image as ImageIcon,
} from "lucide-react"

// Scene类型定义
interface Scene {
  id: number
  title: string
  description: string
  baseImage: string
  videoPrompt: string
  generatedVideos: any[]
  duration: number
  shotData: ShotWithImages | null
  primaryImage: GeneratedImage | null
  // 视频状态缓存相关字段
  videoTaskId?: string
  videoTaskStatus?: string
  lastStatusCheck?: number
  statusCacheExpiry?: number // 缓存过期时间戳
}

interface Shot {
  shotNumber: number
  location: string
  action: string
  characters: string[]
  lighting: string
  mood: string
  cameraMovement: string
  duration?: number
  shotType?: string
  dialogue?: string
  props?: string[]
  soundEffect?: string
  transition?: string
}

interface ScriptData {
  title?: string
  style?: string
  shots?: Shot[]
}

interface GeneratedImage {
  id?: string
  url: string
  filename?: string
  width?: number
  height?: number
  format?: string
  isPrimary?: boolean
  createdAt?: string
  actualPrompt?: string
  qualityScore?: number
  taskId?: string
  taskName?: string
}

interface ShotWithImages {
  shotNumber: number
  shotId?: string
  location: string
  action: string
  dialogue?: string
  mood: string
  duration?: number
  hasImages: boolean
  imageCount: number
  images: GeneratedImage[]
  tasks?: Array<{
    id: string
    taskName: string
    status: string
    createdAt: string
    completedAt?: string
    imageCount: number
  }>
}

interface VideoGenerationStepProps {
  isPlaying: boolean
  setIsPlaying: (playing: boolean) => void
  scriptData?: ScriptData
  projectId?: string
  userId?: string
  shotsWithImages?: ShotWithImages[]
}

export function VideoGenerationStep({
  isPlaying,
  setIsPlaying,
  scriptData,
  projectId,
  userId,
  shotsWithImages,
}: VideoGenerationStepProps) {
  const [currentScene, setCurrentScene] = useState(0)
  const [videoRatio, setVideoRatio] = useState("16:9")
  const [mode, setMode] = useState("std")
  const [animationStrength, setAnimationStrength] = useState([50])
  const [motionScale, setMotionScale] = useState([70])
  const [modelName, setModelName] = useState("kling-v1")
  const [seed, setSeed] = useState("")
  const [isGenerating, setIsGenerating] = useState(false)
  const [generationProgress, setGenerationProgress] = useState(0)
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [isUploadingToImgbb, setIsUploadingToImgbb] = useState(false)

  // 根据镜头数据和生成的图片生成场景
  const generateScenesFromShotsWithImages = React.useCallback((): Scene[] => {
    if (!shotsWithImages || shotsWithImages.length === 0) {
      // 如果没有镜头图片数据，使用脚本数据生成默认场景
      if (scriptData && scriptData.shots) {
        return scriptData.shots.map((shot: Shot) => ({
          id: shot.shotNumber,
          title: `镜头 ${shot.shotNumber}`,
          description: `${shot.location} - ${shot.action}`,
          baseImage: "/placeholder.svg",
          videoPrompt: generateVideoPrompt(shot),
          generatedVideos: [],
          duration: 5, // 统一设置为5秒
          shotData: null,
          primaryImage: null
        }))
      }

      // 默认场景
      return defaultScenes
    }

    // 使用镜头图片数据生成场景
    return shotsWithImages.map((shotWithImages: ShotWithImages) => {
      const primaryImage = shotWithImages.images.find(img => img.isPrimary) || shotWithImages.images[0]
      const shot = scriptData?.shots?.find(s => s.shotNumber === shotWithImages.shotNumber)

      return {
        id: shotWithImages.shotNumber,
        title: `镜头 ${shotWithImages.shotNumber}`,
        description: `${shotWithImages.location} - ${shotWithImages.action}`,
        baseImage: primaryImage?.url || "/placeholder.svg",
        videoPrompt: shot ? generateVideoPrompt(shot) : generateDefaultVideoPrompt(shotWithImages),
        generatedVideos: [],
        duration: 5, // 统一设置为5秒
        shotData: shotWithImages,
        primaryImage: primaryImage || null
      }
    })
  }, [shotsWithImages, scriptData])

  // 根据镜头数据生成视频提示词
  const generateVideoPrompt = (shot: Shot): string => {
    const basePrompt = `${shot.location}, ${shot.action}, ${shot.characters.join(' and ')}, ${shot.lighting}, ${shot.mood} atmosphere, ${shot.cameraMovement}`
    return `${basePrompt}, smooth camera movement, cinematic motion, high quality video`
  }

  // 根据镜头图片数据生成默认视频提示词
  const generateDefaultVideoPrompt = (shotWithImages: ShotWithImages): string => {
    const basePrompt = `${shotWithImages.location}, ${shotWithImages.action}, ${shotWithImages.mood} atmosphere`
    return `${basePrompt}, smooth camera movement, cinematic motion, high quality video`
  }

  // 默认场景数据
  const defaultScenes = [
    {
      id: 1,
      title: "开场场景",
      description: "城市街道，黄昏时分",
      baseImage: "/placeholder.svg",
      videoPrompt: "Smooth camera movement through a busy city street at golden hour, cinematic lighting, urban landscape with gentle motion",
      generatedVideos: [],
      duration: 5, // 统一设置为5秒
      shotData: null,
      primaryImage: null
    },
    {
      id: 2,
      title: "对话场景",
      description: "咖啡厅内部，温馨氛围",
      baseImage: "/placeholder.svg",
      videoPrompt: "Cozy coffee shop interior with steam rising from cups, warm ambient lighting, subtle camera movement",
      generatedVideos: [],
      duration: 5, // 统一设置为5秒
      shotData: null,
      primaryImage: null
    },
    {
      id: 3,
      title: "冲突场景",
      description: "公园长椅，雨天",
      baseImage: "/placeholder.svg",
      videoPrompt: "Rain falling on park bench, droplets on wet pavement, dramatic weather effects with moody atmosphere",
      generatedVideos: [],
      duration: 5, // 统一设置为5秒
      shotData: null,
      primaryImage: null
    },
    {
      id: 4,
      title: "转折场景",
      description: "办公室，明亮光线",
      baseImage: "/placeholder.svg",
      videoPrompt: "Modern office space with natural light streaming through windows, professional environment with subtle movement",
      generatedVideos: [],
      duration: 5, // 统一设置为5秒
      shotData: null,
      primaryImage: null
    },
    {
      id: 5,
      title: "高潮场景",
      description: "天台，夜景城市",
      baseImage: "/placeholder.svg",
      videoPrompt: "Rooftop terrace at night with city lights twinkling, urban nightscape with dramatic perspective",
      generatedVideos: [],
      duration: 5, // 统一设置为5秒
      shotData: null,
      primaryImage: null
    },
    {
      id: 6,
      title: "结尾场景",
      description: "海边，日出时分",
      baseImage: "/placeholder.svg",
      videoPrompt: "Beach at sunrise with gentle waves, golden light reflecting on water, peaceful ocean scene",
      generatedVideos: [],
      duration: 5, // 统一设置为5秒
      shotData: null,
      primaryImage: null
    },
  ]

  const [scenes, setScenes] = useState<Scene[]>(() => generateScenesFromShotsWithImages())
  const [videoStatusCache, setVideoStatusCache] = useState<Map<string, any>>(new Map())

  // 使用ref来跟踪是否已经执行过自动刷新
  const hasAutoRefreshed = React.useRef(false)

  // 缓存过期时间（5分钟）
  const CACHE_EXPIRY_TIME = 5 * 60 * 1000

  // 检查视频状态缓存是否有效
  const isVideoStatusCacheValid = (scene: Scene): boolean => {
    if (!scene.statusCacheExpiry || !scene.lastStatusCheck) {
      return false
    }
    return Date.now() < scene.statusCacheExpiry
  }

  // 获取缓存的视频状态
  const getCachedVideoStatus = (shotId: string) => {
    return videoStatusCache.get(shotId)
  }

  // 设置视频状态缓存
  const updateVideoStatusCache = (shotId: string, status: any) => {
    setVideoStatusCache(prev => {
      const newCache = new Map(prev)
      // 为processing状态设置较短的缓存时间（1分钟），其他状态使用默认时间（5分钟）
      const cacheTime = status.status === 'processing' ? 60 * 1000 : CACHE_EXPIRY_TIME
      newCache.set(shotId, {
        ...status,
        cachedAt: Date.now(),
        expiresAt: Date.now() + cacheTime
      })
      return newCache
    })
  }

  // 清理过期缓存
  const cleanExpiredCache = () => {
    setVideoStatusCache(prev => {
      const newCache = new Map(prev)
      const now = Date.now()
      for (const [key, value] of newCache.entries()) {
        if (now > value.expiresAt) {
          newCache.delete(key)
        }
      }
      return newCache
    })
  }

  // 定期清理过期缓存
  React.useEffect(() => {
    const cleanupInterval = setInterval(cleanExpiredCache, 60 * 1000) // 每分钟清理一次
    return () => clearInterval(cleanupInterval)
  }, [])

  // 调试函数：显示缓存状态
  const logCacheStatus = () => {
    console.log('💾 [CACHE-DEBUG] ===== 当前缓存状态 =====')
    console.log(`💾 [CACHE-DEBUG] 缓存条目数: ${videoStatusCache.size}`)
    const now = Date.now()
    videoStatusCache.forEach((value, key) => {
      const age = Math.round((now - value.cachedAt) / 1000)
      const remaining = Math.round((value.expiresAt - now) / 1000)
      console.log(`💾 [CACHE-DEBUG] 镜头 ${key}: 状态=${value.status}, 年龄=${age}s, 剩余=${remaining}s`)
    })
    console.log('💾 [CACHE-DEBUG] ===== 缓存状态结束 =====')
  }

  // 在开发环境下，将调试函数暴露到全局
  React.useEffect(() => {
    if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
      (window as any).logVideoStatusCache = logCacheStatus
      console.log('💾 [CACHE-DEBUG] 调试函数已暴露: window.logVideoStatusCache()')
    }
  }, [videoStatusCache])

  // 当镜头图片数据改变时更新场景
  React.useEffect(() => {
    setScenes(generateScenesFromShotsWithImages())
    setCurrentScene(0)
    // 重置自动刷新记录和缓存
    setVideoStatusCache(new Map())
    hasAutoRefreshed.current = false
  }, [shotsWithImages, scriptData, generateScenesFromShotsWithImages])

  // 自动刷新所有镜头的视频状态（页面进入时执行一次）
  React.useEffect(() => {

    const autoRefreshAllScenes = async () => {
      if (!projectId || !userId || scenes.length === 0) {
        return
      }

      // 如果已经执行过自动刷新，则跳过
      if (hasAutoRefreshed.current) {
        console.log('📝 [AUTO-REFRESH] 已执行过自动刷新，跳过')
        return
      }

      console.log('🔄 [AUTO-REFRESH] 开始批量自动刷新所有镜头的视频状态')

      // 标记为已执行
      hasAutoRefreshed.current = true

      // 收集所有需要刷新的镜头ID
      const shotIds = scenes
        .filter(scene => scene.shotData?.shotId)
        .map(scene => scene.shotData!.shotId!)

      if (shotIds.length === 0) {
        console.log('📝 [AUTO-REFRESH] 没有需要刷新的镜头')
        return
      }

      console.log(`🔍 [AUTO-REFRESH] 批量查询 ${shotIds.length} 个镜头的视频状态:`, shotIds)

      try {
        // 步骤1: 批量查询数据库中的最新任务
        const batchResponse = await fetch('/api/save-video-generation/batch', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            projectId,
            userId,
            shotIds
          })
        })

        if (!batchResponse.ok) {
          throw new Error(`批量查询失败: ${batchResponse.status} ${batchResponse.statusText}`)
        }

        const batchResult = await batchResponse.json()
        if (!batchResult.success) {
          throw new Error(batchResult.error || '批量查询失败')
        }

        console.log(`✅ [AUTO-REFRESH] 批量查询成功，找到 ${batchResult.totalShots} 个镜头的任务`)

        // 步骤2: 处理每个镜头的任务状态
        const processingTasks: Array<{shotId: string, task: any, scene: Scene}> = []

        for (const scene of scenes) {
          if (!scene.shotData?.shotId) {
            continue
          }

          // 检查缓存是否有效
          const cachedStatus = getCachedVideoStatus(scene.shotData.shotId)
          if (cachedStatus && Date.now() < cachedStatus.expiresAt) {
            console.log(`💾 [AUTO-REFRESH] 使用缓存数据，镜头 ${scene.shotData.shotId}`)
            continue
          }

          const shotTasks = batchResult.tasks[scene.shotData.shotId]
          if (!shotTasks || shotTasks.length === 0) {
            console.log(`📝 [AUTO-REFRESH] 镜头 ${scene.shotData.shotId} 未找到相关任务`)
            continue
          }

          const latestTask = shotTasks[0] // 最新任务
          console.log(`📊 [AUTO-REFRESH] 镜头 ${scene.shotData.shotId} 最新任务状态: ${latestTask.status}`)

          if (latestTask.status === 'completed') {
            // 任务已完成，直接更新前端并缓存状态
            await handleCompletedTask(scene, latestTask)
            updateVideoStatusCache(scene.shotData.shotId, {
              status: 'completed',
              taskId: latestTask.kling_task_id || latestTask.id,
              videos: latestTask.generated_videos
            })
          } else if (latestTask.status === 'processing') {
            // 任务处理中，收集需要查询Kling API的任务
            processingTasks.push({
              shotId: scene.shotData.shotId,
              task: latestTask,
              scene
            })
          } else if (latestTask.status === 'failed') {
            // 任务失败，静默记录并缓存失败状态
            console.error(`❌ [AUTO-REFRESH] 镜头 ${scene.shotData.shotId} 任务失败: ${latestTask.error_message || '未知错误'}`)
            updateVideoStatusCache(scene.shotData.shotId, {
              status: 'failed',
              taskId: latestTask.kling_task_id || latestTask.id,
              error: latestTask.error_message
            })
          }
        }

        // 步骤3: 批量查询Kling API状态（仅对processing状态的任务）
        if (processingTasks.length > 0) {
          await handleBatchProcessingTasks(processingTasks)
        }

        console.log('✅ [AUTO-REFRESH] 批量自动刷新完成')
        console.log(`💾 [AUTO-REFRESH] 当前缓存状态: ${videoStatusCache.size} 个镜头已缓存`)

      } catch (error) {
        console.error('❌ [AUTO-REFRESH] 批量自动刷新失败:', error)
      }
    }

    // 延迟执行自动刷新，确保组件完全加载
    const timer = setTimeout(() => {
      autoRefreshAllScenes()
    }, 2000)

    return () => clearTimeout(timer)
  }, [scenes, projectId, userId]) // 移除 autoRefreshedScenes 依赖

  const updateScenePrompt = (sceneId: number, newPrompt: string) => {
    setScenes(scenes.map(scene => 
      scene.id === sceneId ? { ...scene, videoPrompt: newPrompt } : scene
    ))
  }

  // 时长已统一设置为5秒，不再需要更新函数

  // 处理图片上传
  const handleImageUpload = async (file: File) => {
    setIsUploading(true)
    setUploadProgress(0)

    try {
      console.log('🖼️ [UPLOAD] 开始上传图片:', file.name)

      const formData = new FormData()
      formData.append('image', file)

      // 模拟上传进度
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval)
            return 90
          }
          return prev + 10
        })
      }, 200)

      const response = await fetch('/api/upload-image', {
        method: 'POST',
        body: formData,
      })

      clearInterval(progressInterval)

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || '上传失败')
      }

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || '上传失败')
      }

      console.log('✅ [UPLOAD] 图片上传成功:', result.data.url)
      console.log('✅ [UPLOAD] ImgBB验证状态:', result.data.verified ? '已验证' : '未验证')
      setUploadProgress(100)

      // 验证这是一个有效的Supabase Storage URL
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || ''
      const isValidSupabaseUrl = result.data.url && result.data.url.includes(supabaseUrl) &&
                                 result.data.url.includes('/storage/v1/object/public/generated_images/')
      if (!isValidSupabaseUrl) {
        console.error('❌ [UPLOAD] 返回的URL不是有效的Supabase Storage URL:', result.data.url)
        throw new Error('上传失败：返回的不是有效的Supabase Storage URL')
      }

      console.log('✅ [UPLOAD] Supabase Storage URL验证通过，准备用于视频生成')

      // 更新当前场景的基础图片
      const currentSceneData = scenes[currentScene]
      if (currentSceneData) {
        setScenes(scenes.map(scene =>
          scene.id === currentSceneData.id
            ? {
                ...scene,
                baseImage: result.data.url,
                primaryImage: {
                  id: result.data.id,
                  url: result.data.url,
                  filename: result.data.originalFilename,
                  width: result.data.width,
                  height: result.data.height,
                  format: result.data.type,
                  isPrimary: true,
                  verified: result.data.verified || false
                }
              }
            : scene
        ))
      }

      // 延迟重置状态
      setTimeout(() => {
        setIsUploading(false)
        setUploadProgress(0)
      }, 1000)

      return result.data

    } catch (error) {
      console.error('❌ [UPLOAD] 上传失败:', error)
      setIsUploading(false)
      setUploadProgress(0)
      throw error
    }
  }

  // 上传图片到Supabase Storage
  const handleUploadInternalImageToSupabase = async (imageUrl: string) => {
    setIsUploadingToImgbb(true)

    try {
      console.log('📤 [UPLOAD-TO-SUPABASE] 开始上传图片到Supabase Storage:', imageUrl)

      const response = await fetch('/api/upload-internal-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ imageUrl }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || '上传失败')
      }

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || '上传失败')
      }

      console.log('✅ [UPLOAD-TO-SUPABASE] 图片上传到Supabase Storage成功:', result.data.supabaseUrl || result.data.url)

      // 更新当前场景的图片URL
      const currentSceneData = scenes[currentScene]
      if (currentSceneData) {
        setScenes(scenes.map(scene =>
          scene.id === currentSceneData.id
            ? {
                ...scene,
                baseImage: result.data.url,
                primaryImage: {
                  ...scene.primaryImage,
                  url: result.data.url,
                  verified: true
                }
              }
            : scene
        ))
      }

      alert(`图片已成功上传到Supabase Storage！\n\n✅ 上传完成并验证可访问\n🔗 新的Supabase URL: ${result.data.url}\n📁 原始URL: ${result.data.originalUrl}\n\n现在可以正常生成视频了。`)

    } catch (error) {
      console.error('❌ [UPLOAD-TO-SUPABASE] 上传失败:', error)
      alert('图片上传到Supabase Storage失败: ' + (error instanceof Error ? error.message : '未知错误'))
    } finally {
      setIsUploadingToImgbb(false)
    }
  }

  // 处理已完成的任务 - 更新前端显示
  const handleCompletedTask = async (scene: Scene, latestTask: any) => {
    console.log(`✅ [AUTO-REFRESH] 处理已完成任务，场景: ${scene.title}`)

    if (latestTask.generated_videos && latestTask.generated_videos.length > 0) {
      const newVideos = latestTask.generated_videos.map((video: any, index: number) => ({
        id: `${latestTask.kling_task_id || latestTask.id}_${index}`,
        url: video.video_url,
        taskId: latestTask.kling_task_id || latestTask.id,
        status: 'completed',
        createdAt: video.created_at,
        duration: video.video_duration_seconds || scene.duration
      }))

      // 更新前端显示
      setScenes(prevScenes =>
        prevScenes.map(s =>
          s.id === scene.id
            ? {
                ...s,
                videoTaskId: latestTask.kling_task_id || latestTask.id,
                videoTaskStatus: 'succeed',
                lastStatusCheck: Date.now(),
                generatedVideos: [...s.generatedVideos, ...newVideos]
              }
            : s
        )
      )

      console.log(`✅ [AUTO-REFRESH] 场景 "${scene.title}" 视频状态已更新，视频数量: ${newVideos.length}个`)
    } else {
      console.warn(`⚠️ [AUTO-REFRESH] 任务已完成但未找到视频，场景: ${scene.title}`)
    }
  }

  // 批量处理processing状态的任务
  const handleBatchProcessingTasks = async (processingTasks: Array<{shotId: string, task: any, scene: Scene}>) => {
    console.log(`🔍 [AUTO-REFRESH] 批量查询 ${processingTasks.length} 个processing任务的Kling API状态`)

    // 收集所有需要查询的Kling任务ID
    const klingTaskIds = processingTasks
      .filter(item => item.task.kling_task_id)
      .map(item => item.task.kling_task_id)

    if (klingTaskIds.length === 0) {
      console.warn('⚠️ [AUTO-REFRESH] 没有有效的Kling任务ID需要查询')
      return
    }

    try {
      // 批量查询Kling API状态
      const klingResponse = await fetch('/api/check-task-status/batch', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ taskIds: klingTaskIds })
      })

      if (!klingResponse.ok) {
        throw new Error(`Kling API批量查询失败: ${klingResponse.status}`)
      }

      const klingResult = await klingResponse.json()
      if (!klingResult.success) {
        throw new Error(klingResult.error || 'Kling API批量查询失败')
      }

      console.log(`✅ [AUTO-REFRESH] Kling API批量查询成功，成功查询 ${klingResult.summary.success} 个任务`)

      // 处理每个任务的结果
      for (const item of processingTasks) {
        const { task, scene, shotId } = item
        const klingTaskId = task.kling_task_id

        if (!klingTaskId || !klingResult.data[klingTaskId]) {
          console.warn(`⚠️ [AUTO-REFRESH] 未找到Kling任务 ${klingTaskId} 的查询结果`)
          continue
        }

        const taskData = klingResult.data[klingTaskId]
        const taskStatus = taskData.task_status

        console.log(`📊 [AUTO-REFRESH] 场景 "${scene.title}" Kling任务状态: ${taskStatus}`)

        if (taskStatus === 'succeed' && taskData.task_result?.videos) {
          // 任务成功完成，更新数据库和前端，并缓存状态
          await handleSuccessfulKlingTask(scene, task, taskData)
          updateVideoStatusCache(shotId, {
            status: 'completed',
            taskId: klingTaskId,
            videos: taskData.task_result.videos
          })
        } else if (taskStatus === 'processing') {
          // 仍在处理中 - 静默记录，缓存处理中状态（较短过期时间）
          console.log(`⏳ [AUTO-REFRESH] 场景 "${scene.title}" 视频仍在生成中`)
          updateVideoStatusCache(shotId, {
            status: 'processing',
            taskId: klingTaskId
          })
        } else if (taskStatus === 'failed') {
          // 任务失败，更新数据库状态并缓存失败状态
          await handleFailedKlingTask(task, taskData)
          updateVideoStatusCache(shotId, {
            status: 'failed',
            taskId: klingTaskId,
            error: taskData.task_status_msg
          })
          console.error(`❌ [AUTO-REFRESH] 场景 "${scene.title}" 视频生成失败: ${taskData.task_status_msg || '未知错误'}`)
        }
      }

    } catch (error) {
      console.error('❌ [AUTO-REFRESH] 批量查询Kling API失败:', error)
    }
  }

  // 处理成功的Kling任务
  const handleSuccessfulKlingTask = async (scene: Scene, latestTask: any, taskData: any) => {
    console.log(`✅ [AUTO-REFRESH] 处理成功的Kling任务，场景: ${scene.title}`)

    // 先更新数据库
    try {
      const updateResponse = await fetch('/api/save-video-generation', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          taskId: latestTask.id,
          status: 'completed',
          completedAt: new Date().toISOString(),
          apiResponseData: taskData,
          videos: taskData.task_result.videos.map((video: any) => ({
            videoUrl: video.url,
            videoDurationSeconds: scene.duration,
            videoFormat: 'mp4',
            storageProvider: 'kling',
            metadata: video
          }))
        })
      })

      if (updateResponse.ok) {
        console.log(`✅ [AUTO-REFRESH] 数据库更新成功，场景: ${scene.title}`)
      } else {
        console.warn(`⚠️ [AUTO-REFRESH] 数据库更新失败，但继续更新前端，场景: ${scene.title}`)
      }
    } catch (updateError) {
      console.error(`❌ [AUTO-REFRESH] 数据库更新失败，场景: ${scene.title}`, updateError)
    }

    // 更新前端页面
    const newVideos = taskData.task_result.videos.map((video: any, index: number) => ({
      id: `${latestTask.kling_task_id}_${index}`,
      url: video.url,
      taskId: latestTask.kling_task_id,
      status: 'completed',
      createdAt: new Date().toISOString(),
      duration: scene.duration
    }))

    setScenes(prevScenes =>
      prevScenes.map(s =>
        s.id === scene.id
          ? {
              ...s,
              videoTaskId: latestTask.kling_task_id,
              videoTaskStatus: 'succeed',
              lastStatusCheck: Date.now(),
              generatedVideos: [...s.generatedVideos, ...newVideos]
            }
          : s
      )
    )

    console.log(`✅ [AUTO-REFRESH] 场景 "${scene.title}" 视频生成完成，视频数量: ${newVideos.length}个`)
  }

  // 处理失败的Kling任务
  const handleFailedKlingTask = async (latestTask: any, taskData: any) => {
    try {
      await fetch('/api/save-video-generation', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          taskId: latestTask.id,
          status: 'failed',
          completedAt: new Date().toISOString(),
          errorMessage: taskData.task_status_msg || 'Kling API返回失败状态',
          apiResponseData: taskData
        })
      })
    } catch (updateError) {
      console.error(`❌ [AUTO-REFRESH] 更新失败状态到数据库失败:`, updateError)
    }
  }



  // 刷新视频 - 查询任务状态并根据状态处理
  const handleRefreshVideo = async (sceneId: number) => {
    const scene = scenes.find(s => s.id === sceneId)
    if (!scene) {
      console.error(`❌ [REFRESH-VIDEO] 未找到场景 ID: ${sceneId}`)
      return
    }

    console.log(`🔄 [REFRESH-VIDEO] 开始刷新场景 "${scene.title}" 的视频`)
    console.log(`🔄 [REFRESH-VIDEO] 项目ID: ${projectId}`)
    console.log(`🔄 [REFRESH-VIDEO] 镜头ID: ${scene.shotData?.shotId}`)
    console.log(`🔄 [REFRESH-VIDEO] 用户ID: ${userId}`)

    // 检查必要的ID
    if (!projectId || !scene.shotData?.shotId || !userId) {
      const missingIds = []
      if (!projectId) missingIds.push('项目ID')
      if (!scene.shotData?.shotId) missingIds.push('镜头ID')
      if (!userId) missingIds.push('用户ID')

      alert(`无法刷新视频，缺少必要信息：\n${missingIds.join('、')}\n\n请确保：\n• 项目已正确加载\n• 镜头数据完整\n• 用户已登录`)
      return
    }

    // 检查缓存是否有效（对于手动刷新，使用较短的缓存时间）
    const cachedStatus = getCachedVideoStatus(scene.shotData.shotId)
    if (cachedStatus && Date.now() < cachedStatus.expiresAt) {
      const cacheAge = Math.round((Date.now() - cachedStatus.cachedAt) / 1000)
      console.log(`💾 [REFRESH-VIDEO] 使用缓存数据（${cacheAge}秒前），镜头 ${scene.shotData.shotId}`)

      if (cachedStatus.status === 'completed' && cachedStatus.videos) {
        alert(`✅ 视频状态（来自缓存）\n\n场景: ${scene.title}\n状态: 已完成\n视频数量: ${cachedStatus.videos.length}个\n\n缓存时间: ${cacheAge}秒前`)
        return
      } else if (cachedStatus.status === 'processing') {
        alert(`⏳ 视频状态（来自缓存）\n\n场景: ${scene.title}\n状态: 处理中\n\n缓存时间: ${cacheAge}秒前\n请稍后再试`)
        return
      } else if (cachedStatus.status === 'failed') {
        alert(`❌ 视频状态（来自缓存）\n\n场景: ${scene.title}\n状态: 生成失败\n错误: ${cachedStatus.error || '未知错误'}\n\n缓存时间: ${cacheAge}秒前`)
        return
      }
    }

    try {
      // 步骤1: 查询数据库中的最新任务
      console.log(`�️ [REFRESH-VIDEO] 步骤1: 查询数据库中的最新任务`)
      const queryUrl = `/api/save-video-generation?projectId=${projectId}&shotId=${scene.shotData.shotId}&userId=${userId}`
      console.log(`🔍 [REFRESH-VIDEO] 查询URL: ${queryUrl}`)

      const dbResponse = await fetch(queryUrl)
      if (!dbResponse.ok) {
        throw new Error(`数据库查询失败: ${dbResponse.status} ${dbResponse.statusText}`)
      }

      const dbResult = await dbResponse.json()
      if (!dbResult.success || !dbResult.tasks || dbResult.tasks.length === 0) {
        alert(`未找到相关的视频生成任务\n\n场景: ${scene.title}\n项目ID: ${projectId}\n镜头ID: ${scene.shotData.shotId}\n\n请先点击"生成视频"按钮创建任务`)
        return
      }

      const latestTask = dbResult.tasks[0] // 获取最新任务
      console.log(`✅ [REFRESH-VIDEO] 找到最新任务:`, latestTask.id)
      console.log(`📊 [REFRESH-VIDEO] 任务状态: ${latestTask.status}`)

      // 步骤2: 根据任务状态进行不同处理
      if (latestTask.status === 'completed') {
        // 状态为completed：查询generated_videos表获取视频地址
        console.log(`✅ [REFRESH-VIDEO] 任务已完成，获取视频地址`)

        if (latestTask.generated_videos && latestTask.generated_videos.length > 0) {
          const newVideos = latestTask.generated_videos.map((video: any, index: number) => ({
            id: `${latestTask.kling_task_id || latestTask.id}_${index}`,
            url: video.video_url,
            taskId: latestTask.kling_task_id || latestTask.id,
            status: 'completed',
            createdAt: video.created_at,
            duration: video.video_duration_seconds || scene.duration
          }))

          // 更新前端显示
          setScenes(prevScenes =>
            prevScenes.map(s =>
              s.id === sceneId
                ? {
                    ...s,
                    videoTaskId: latestTask.kling_task_id || latestTask.id,
                    videoTaskStatus: 'succeed',
                    lastStatusCheck: Date.now(),
                    statusCacheExpiry: Date.now() + CACHE_EXPIRY_TIME,
                    generatedVideos: [...s.generatedVideos, ...newVideos]
                  }
                : s
            )
          )

          // 更新缓存
          updateVideoStatusCache(scene.shotData.shotId, {
            status: 'completed',
            taskId: latestTask.kling_task_id || latestTask.id,
            videos: latestTask.generated_videos
          })

          alert(`✅ 视频刷新成功！\n\n场景: ${scene.title}\n状态: 已完成\n视频数量: ${newVideos.length}个\n\n视频已显示在页面上`)
        } else {
          alert(`⚠️ 任务已完成但未找到视频\n\n场景: ${scene.title}\n任务ID: ${latestTask.id}\n\n可能需要重新生成视频`)
        }

      } else if (latestTask.status === 'failed') {
        // 状态为failed：显示错误信息并更新缓存
        console.log(`❌ [REFRESH-VIDEO] 任务失败`)

        const errorMessage = latestTask.error_message || '未知错误'
        const errorCode = latestTask.error_code || '无错误代码'

        // 更新缓存
        updateVideoStatusCache(scene.shotData.shotId, {
          status: 'failed',
          taskId: latestTask.kling_task_id || latestTask.id,
          error: errorMessage
        })

        alert(`❌ 视频生成失败\n\n场景: ${scene.title}\n任务ID: ${latestTask.id}\n错误代码: ${errorCode}\n错误信息: ${errorMessage}\n\n建议：\n• 检查图片是否有效\n• 重新生成视频\n• 如问题持续，请联系技术支持`)

      } else if (latestTask.status === 'processing') {
        // 状态为processing：通过kling_task_id查询Kling API
        console.log(`⏳ [REFRESH-VIDEO] 任务处理中，查询Kling API状态`)

        if (!latestTask.kling_task_id) {
          alert(`⚠️ 任务处理中但缺少Kling任务ID\n\n场景: ${scene.title}\n任务ID: ${latestTask.id}\n\n任务可能刚创建，请稍后再试`)
          return
        }

        await handleProcessingTask(scene, latestTask, sceneId)

      } else {
        // 其他状态
        console.log(`📝 [REFRESH-VIDEO] 未知状态: ${latestTask.status}`)
        alert(`📝 任务状态: ${latestTask.status}\n\n场景: ${scene.title}\n任务ID: ${latestTask.id}\n\n请稍后再试或重新生成视频`)
      }

    } catch (error) {
      console.error('❌ [REFRESH-VIDEO] 刷新视频失败:', error)
      alert(`❌ 刷新视频失败\n\n错误信息: ${error instanceof Error ? error.message : '未知错误'}\n\n请检查：\n• 网络连接\n• 服务器状态\n• 然后重试`)
    }
  }



  // 处理processing状态的任务
  const handleProcessingTask = async (scene: any, latestTask: any, sceneId: number) => {
    console.log(`🔍 [REFRESH-VIDEO] 通过Kling任务ID查询API状态`)
    console.log(`🔍 [REFRESH-VIDEO] Kling任务ID: ${latestTask.kling_task_id}`)

    const response = await fetch('/api/check-task-status', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ taskId: latestTask.kling_task_id }),
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || '查询Kling任务状态失败')
    }

    const result = await response.json()
    console.log(`📊 [REFRESH-VIDEO] Kling API查询结果:`, result)

    if (result.success && result.data) {
      const taskData = result.data
      const taskStatus = taskData.task_status

      console.log(`📊 [REFRESH-VIDEO] Kling任务状态: ${taskStatus}`)

      if (taskStatus === 'succeed' && taskData.task_result?.videos) {
        // 任务成功完成，更新数据库和前端
        console.log(`✅ [REFRESH-VIDEO] 视频生成完成，更新数据库和页面`)

        // 先更新数据库
        try {
          const updateResponse = await fetch('/api/save-video-generation', {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              taskId: latestTask.id,
              status: 'completed',
              completedAt: new Date().toISOString(),
              apiResponseData: taskData,
              videos: taskData.task_result.videos.map((video: any) => ({
                videoUrl: video.url,
                videoDurationSeconds: scene.duration,
                videoFormat: 'mp4',
                storageProvider: 'kling',
                metadata: video
              }))
            })
          })

          if (updateResponse.ok) {
            console.log(`✅ [REFRESH-VIDEO] 数据库更新成功`)
          } else {
            console.warn(`⚠️ [REFRESH-VIDEO] 数据库更新失败，但继续更新前端`)
          }
        } catch (updateError) {
          console.error(`❌ [REFRESH-VIDEO] 数据库更新失败:`, updateError)
        }

        // 更新前端页面
        const newVideos = taskData.task_result.videos.map((video: any, index: number) => ({
          id: `${latestTask.kling_task_id}_${index}`,
          url: video.url,
          taskId: latestTask.kling_task_id,
          status: 'completed',
          createdAt: new Date().toISOString(),
          duration: scene.duration
        }))

        setScenes(prevScenes =>
          prevScenes.map(s =>
            s.id === sceneId
              ? {
                  ...s,
                  videoTaskId: latestTask.kling_task_id,
                  videoTaskStatus: 'succeed',
                  lastStatusCheck: Date.now(),
                  statusCacheExpiry: Date.now() + CACHE_EXPIRY_TIME,
                  generatedVideos: [...s.generatedVideos, ...newVideos]
                }
              : s
          )
        )

        // 更新缓存
        if (scene.shotData?.shotId) {
          updateVideoStatusCache(scene.shotData.shotId, {
            status: 'completed',
            taskId: latestTask.kling_task_id,
            videos: taskData.task_result.videos
          })
        }

        alert(`✅ 视频生成完成！\n\n场景: ${scene.title}\n状态: 已完成\n视频数量: ${newVideos.length}个\n\n视频已显示在页面上`)

      } else if (taskStatus === 'processing') {
        // 仍在处理中，更新缓存（较短过期时间）
        if (scene.shotData?.shotId) {
          updateVideoStatusCache(scene.shotData.shotId, {
            status: 'processing',
            taskId: latestTask.kling_task_id
          })
        }
        alert(`⏳ 视频仍在生成中\n\n场景: ${scene.title}\n状态: 处理中\n\n请稍后再次点击刷新按钮查看进度`)

      } else if (taskStatus === 'failed') {
        // 任务失败，更新数据库状态和缓存
        try {
          await fetch('/api/save-video-generation', {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              taskId: latestTask.id,
              status: 'failed',
              completedAt: new Date().toISOString(),
              errorMessage: taskData.task_status_msg || 'Kling API返回失败状态',
              apiResponseData: taskData
            })
          })
        } catch (updateError) {
          console.error(`❌ [REFRESH-VIDEO] 更新失败状态到数据库失败:`, updateError)
        }

        // 更新缓存
        if (scene.shotData?.shotId) {
          updateVideoStatusCache(scene.shotData.shotId, {
            status: 'failed',
            taskId: latestTask.kling_task_id,
            error: taskData.task_status_msg
          })
        }

        alert(`❌ 视频生成失败\n\n场景: ${scene.title}\n错误信息: ${taskData.task_status_msg || '未知错误'}\n\n建议：\n• 检查图片是否有效\n• 重新生成视频`)

      } else {
        // 其他状态
        console.log(`📝 [REFRESH-VIDEO] 其他Kling状态: ${taskStatus}`)
        alert(`📝 Kling任务状态: ${taskStatus}\n\n场景: ${scene.title}\n\n请稍后再试`)
      }
    } else {
      throw new Error(result.error || '查询Kling任务状态失败')
    }
  }







  // 处理文件选择
  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    try {
      const uploadResult = await handleImageUpload(file)
      alert(`图片已成功上传到Supabase Storage！\n\n✅ 上传完成并验证可访问\n🔗 URL: ${uploadResult.url}\n📁 文件名: ${uploadResult.originalFilename}\n\n现在可以生成视频了。`)
    } catch (error) {
      alert('图片上传失败: ' + (error instanceof Error ? error.message : '未知错误'))
    }

    // 清空文件输入
    event.target.value = ''
  }

  const handleGenerateVideo = async () => {
    setIsGenerating(true)
    setGenerationProgress(0)

    try {
      const currentSceneData = scenes[currentScene]
      if (!currentSceneData) {
        throw new Error('没有选择场景')
      }

      // 检查是否有基础图片
      if (!currentSceneData.primaryImage && !currentSceneData.baseImage) {
        throw new Error('请先上传基础图片')
      }

      const baseImageUrl = currentSceneData.primaryImage?.url || currentSceneData.baseImage
      if (baseImageUrl === "/placeholder.svg") {
        throw new Error('请先上传基础图片')
      }

      // 分析图片URL来源
      const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || ''
      const isSupabaseUrl = baseImageUrl.includes(supabaseUrl) &&
                            baseImageUrl.includes('/storage/v1/object/public/generated_images/')
      const isInternalUrl = baseImageUrl.includes('***********') ||
                           baseImageUrl.includes('localhost') ||
                           baseImageUrl.includes('127.0.0.1') ||
                           baseImageUrl.includes('192.168.') ||
                           baseImageUrl.includes('10.') ||
                           baseImageUrl.startsWith('http://172.') ||
                           baseImageUrl.startsWith('http://192.168.')

      let imageSource = 'Unknown'
      if (isSupabaseUrl) {
        imageSource = 'Supabase Storage ✅'
      } else if (isInternalUrl) {
        imageSource = 'Local (will upload to Supabase) 📤'
      } else if (baseImageUrl !== "/placeholder.svg") {
        imageSource = 'External ⚠️'
      }

      console.log('🎬 [VIDEO] 开始生成视频...')
      console.log('🎬 [VIDEO] 基础图片URL:', baseImageUrl)
      console.log('🎬 [VIDEO] 图片来源:', imageSource)
      console.log('🎬 [VIDEO] 视频提示词:', currentSceneData.videoPrompt)

      if (isInternalUrl) {
        console.log('📤 [VIDEO] 检测到本地URL，服务器将自动上传到Supabase Storage')
      }

      // 模拟进度更新
      const progressInterval = setInterval(() => {
        setGenerationProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval)
            return 90
          }
          return prev + 5
        })
      }, 1000)

      // 调用视频生成API
      const response = await fetch('/api/generate-video', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: currentSceneData.videoPrompt,
          image: baseImageUrl,
          imageReference: 'subject',
          imageFidelity: animationStrength[0] / 100,
          duration: 5, // 统一使用5秒时长
          aspectRatio: videoRatio,
          mode: mode,
          modelName: modelName,
          // 数据库相关字段
          projectId: projectId,
          shotId: currentSceneData.shotData?.shotId,
          userId: userId,
          taskName: `${currentSceneData.title} - 视频生成`,
        }),
      })

      clearInterval(progressInterval)

      if (!response.ok) {
        const errorData = await response.json()
        console.error('❌ [VIDEO] API请求失败:', errorData)

        // 显示详细错误信息
        const userMessage = errorData.userMessage || errorData.error || '视频生成失败'
        const errorDetails = errorData.details ? JSON.stringify(errorData.details, null, 2) : '无详细信息'

        console.error('❌ [VIDEO] 错误详情:', errorDetails)
        alert(`视频生成失败:\n${userMessage}\n\n详细信息:\n${errorDetails}`)

        throw new Error(userMessage)
      }

      const result = await response.json()

      if (!result.success) {
        console.error('❌ [VIDEO] 生成结果失败:', result)

        const userMessage = result.userMessage || result.error || '视频生成失败'
        const errorDetails = result.details ? JSON.stringify(result.details, null, 2) : '无详细信息'

        console.error('❌ [VIDEO] 错误详情:', errorDetails)
        alert(`视频生成失败:\n${userMessage}\n\n详细信息:\n${errorDetails}`)

        throw new Error(userMessage)
      }

      console.log('✅ [VIDEO] 视频生成成功:', result)
      setGenerationProgress(100)

      // 更新场景中的生成视频和任务信息
      if (result.videos && result.videos.length > 0) {
        const newVideos = result.videos.map((video: any, index: number) => ({
          url: video.url,
          id: `${result.taskId || Date.now()}-${index}`,
          taskId: result.taskId,
          duration: 5, // 统一使用5秒时长
        }))

        setScenes(scenes.map(scene =>
          scene.id === currentSceneData.id
            ? {
                ...scene,
                generatedVideos: [...scene.generatedVideos, ...newVideos],
                videoTaskId: result.taskId,
                videoTaskStatus: 'succeed',
                lastStatusCheck: Date.now()
              }
            : scene
        ))

        alert('视频生成成功！')
      } else if (result.taskId) {
        // 如果只有taskId但没有视频（异步生成），保存任务信息
        console.log('📝 [VIDEO] 视频生成已提交，任务ID:', result.taskId)

        setScenes(scenes.map(scene =>
          scene.id === currentSceneData.id
            ? {
                ...scene,
                videoTaskId: result.taskId,
                videoTaskStatus: result.status || 'submitted',
                lastStatusCheck: Date.now()
              }
            : scene
        ))

        alert(`视频生成已提交！\n任务ID: ${result.taskId}\n\n您可以点击场景卡片查询生成状态。`)
      }

    } catch (error) {
      console.error('❌ [VIDEO] ===== 视频生成失败详细日志 =====')
      console.error('❌ [VIDEO] 错误类型:', error instanceof Error ? error.constructor.name : typeof error)
      console.error('❌ [VIDEO] 错误信息:', error instanceof Error ? error.message : String(error))
      console.error('❌ [VIDEO] 错误堆栈:', error instanceof Error ? error.stack : '无堆栈信息')
      console.error('❌ [VIDEO] 当前场景:', scenes[currentScene]?.title)
      console.error('❌ [VIDEO] 基础图片:', scenes[currentScene]?.primaryImage?.url || scenes[currentScene]?.baseImage)
      console.error('❌ [VIDEO] ===== 视频生成失败日志结束 =====')

      // 如果错误信息已经包含了用户友好的消息，就不再重复显示alert
      const errorMessage = error instanceof Error ? error.message : '未知错误'
      if (!errorMessage.includes('视频生成失败:')) {
        alert('视频生成失败: ' + errorMessage)
      }
    } finally {
      setIsGenerating(false)
      setGenerationProgress(0)
    }
  }

  const handleGenerateAllVideos = async () => {
    if (isGenerating) return

    // 筛选出有基础图片但没有视频的场景
    const scenesWithImagesButNoVideos = scenes.filter(scene =>
      (scene.primaryImage || (scene.baseImage && scene.baseImage !== "/placeholder.svg")) &&
      scene.generatedVideos.length === 0
    )

    if (scenesWithImagesButNoVideos.length === 0) {
      alert('所有有图片的场景都已生成视频，或没有可用的基础图片')
      return
    }

    setIsGenerating(true)
    setGenerationProgress(0)

    let successCount = 0
    const totalScenes = scenesWithImagesButNoVideos.length

    try {

      for (let i = 0; i < scenesWithImagesButNoVideos.length; i++) {
        const scene = scenesWithImagesButNoVideos[i]

        try {
          console.log(`🎬 [BATCH] 正在生成第 ${i + 1}/${totalScenes} 个视频: ${scene.title}`)

          const baseImageUrl = scene.primaryImage?.url || scene.baseImage

          // 分析图片URL来源
          const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || ''
          const isSupabaseUrl = baseImageUrl.includes(supabaseUrl) &&
                                baseImageUrl.includes('/storage/v1/object/public/generated_images/')
          const isInternalUrl = baseImageUrl.includes('***********') ||
                               baseImageUrl.includes('localhost') ||
                               baseImageUrl.includes('127.0.0.1') ||
                               baseImageUrl.includes('192.168.') ||
                               baseImageUrl.includes('10.') ||
                               baseImageUrl.startsWith('http://172.') ||
                               baseImageUrl.startsWith('http://192.168.')

          let imageSource = 'Unknown'
          if (isSupabaseUrl) {
            imageSource = 'Supabase Storage ✅'
          } else if (isInternalUrl) {
            imageSource = 'Local (will upload to Supabase) 📤'
          } else {
            imageSource = 'External ⚠️'
          }

          console.log(`🎬 [BATCH] 场景 ${scene.title} 图片来源:`, imageSource)
          console.log(`🎬 [BATCH] 场景 ${scene.title} 图片URL:`, baseImageUrl)

          if (isInternalUrl) {
            console.log(`📤 [BATCH] 场景 ${scene.title} 检测到本地URL，服务器将自动上传到Supabase Storage`)
          }

          const response = await fetch('/api/generate-video', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              prompt: scene.videoPrompt,
              image: baseImageUrl,
              imageReference: 'subject',
              imageFidelity: animationStrength[0] / 100,
              duration: 5, // 统一使用5秒时长
              aspectRatio: videoRatio,
              mode: mode,
              modelName: modelName,
              // 数据库相关字段
              projectId: projectId,
              shotId: scene.shotData?.shotId,
              userId: userId,
              taskName: `${scene.title} - 批量视频生成`,
            }),
          })

          if (response.ok) {
            const result = await response.json()

            if (result.success && result.videos && result.videos.length > 0) {
              const newVideos = result.videos.map((video: any, index: number) => ({
                url: video.url,
                id: `${result.taskId || Date.now()}-${index}`,
                taskId: result.taskId,
                duration: 5, // 统一使用5秒时长
              }))

              setScenes(prevScenes => prevScenes.map(s =>
                s.id === scene.id
                  ? { ...s, generatedVideos: [...s.generatedVideos, ...newVideos] }
                  : s
              ))

              successCount++
              console.log(`✅ [BATCH] 场景 ${scene.title} 视频生成成功`)
            } else {
              console.error(`❌ [BATCH] 场景 ${scene.title} 视频生成失败:`, result)

              const userMessage = result.userMessage || result.error || '视频生成失败'
              const errorDetails = result.details ? JSON.stringify(result.details, null, 2) : '无详细信息'

              console.error(`❌ [BATCH] 场景 ${scene.title} 错误详情:`, errorDetails)

              // 对于批量生成，我们记录错误但继续处理其他场景
              console.error(`❌ [BATCH] 场景 ${scene.title} 跳过: ${userMessage}`)
            }
          } else {
            const errorData = await response.json()
            console.error(`❌ [BATCH] 场景 ${scene.title} 请求失败:`, errorData)

            const userMessage = errorData.userMessage || errorData.error || '请求失败'
            const errorDetails = errorData.details ? JSON.stringify(errorData.details, null, 2) : '无详细信息'

            console.error(`❌ [BATCH] 场景 ${scene.title} 错误详情:`, errorDetails)
            console.error(`❌ [BATCH] 场景 ${scene.title} 跳过: ${userMessage}`)
          }
        } catch (sceneError) {
          console.error(`❌ [BATCH] ===== 场景 ${scene.title} 生成出错详细日志 =====`)
          console.error(`❌ [BATCH] 错误类型:`, sceneError instanceof Error ? sceneError.constructor.name : typeof sceneError)
          console.error(`❌ [BATCH] 错误信息:`, sceneError instanceof Error ? sceneError.message : String(sceneError))
          console.error(`❌ [BATCH] 错误堆栈:`, sceneError instanceof Error ? sceneError.stack : '无堆栈信息')
          console.error(`❌ [BATCH] 场景信息:`, {
            title: scene.title,
            baseImage: scene.primaryImage?.url || scene.baseImage,
            prompt: scene.videoPrompt
          })
          console.error(`❌ [BATCH] ===== 场景错误日志结束 =====`)
        }

        // 更新进度
        setGenerationProgress(((i + 1) / totalScenes) * 100)

        // 添加延迟避免API限制
        if (i < scenesWithImagesButNoVideos.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 3000))
        }
      }

      const failedCount = totalScenes - successCount
      let message = `批量生成完成！\n成功: ${successCount}/${totalScenes} 个场景`

      if (failedCount > 0) {
        message += `\n失败: ${failedCount} 个场景`
        message += `\n请查看控制台日志了解失败详情`
      }

      alert(message)

    } catch (error) {
      console.error('❌ [BATCH] ===== 批量生成错误详细日志 =====')
      console.error('❌ [BATCH] 错误类型:', error instanceof Error ? error.constructor.name : typeof error)
      console.error('❌ [BATCH] 错误信息:', error instanceof Error ? error.message : String(error))
      console.error('❌ [BATCH] 错误堆栈:', error instanceof Error ? error.stack : '无堆栈信息')
      console.error('❌ [BATCH] 已处理场景数:', successCount)
      console.error('❌ [BATCH] ===== 批量生成错误日志结束 =====')

      alert(`批量生成失败: ${error instanceof Error ? error.message : '未知错误'}\n已成功生成 ${successCount} 个视频`)
    } finally {
      setIsGenerating(false)
      setGenerationProgress(0)
    }
  }

  const totalDuration = scenes.length * 5 // 每个场景统一5秒

  return (
    <div className="h-full flex bg-gradient-to-br from-slate-50/50 to-blue-50/30">
      {/* 左侧场景列表 */}
      <div className="w-1/3 p-8 bg-white/80 backdrop-blur-sm border-r border-slate-200/60">
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-purple-500 to-pink-600 flex items-center justify-center">
              <Video className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-slate-800">场景管理</h2>
              <p className="text-sm text-slate-600">图片转视频创作</p>
            </div>
            {shotsWithImages && shotsWithImages.length > 0 && (
              <Badge className="bg-gradient-to-r from-emerald-100 to-green-100 text-emerald-800 border-emerald-200 font-medium px-3 py-1">
                <ImageIcon className="w-3 h-3 mr-1" />
                {shotsWithImages.length} 个镜头
              </Badge>
            )}
          </div>
          <div className="flex space-x-2">
            <div className="relative">
              <input
                type="file"
                accept="image/*"
                onChange={handleFileSelect}
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                disabled={isUploading}
              />
              <Button
                variant="outline"
                size="sm"
                className="group bg-white/70 border-slate-300 hover:border-slate-400 hover:bg-white hover:shadow-md transition-all duration-300"
                disabled={isUploading}
              >
                <Upload className={`w-4 h-4 mr-2 group-hover:scale-110 transition-transform duration-300 ${isUploading ? 'animate-spin' : ''}`} />
                <span className="font-medium">
                  {isUploading ? '上传中...' : (shotsWithImages && shotsWithImages.length > 0 ? '更换图片' : '导入图片')}
                </span>
              </Button>
            </div>
          </div>
        </div>

        <div className="space-y-4 mb-8">
          {scenes.map((scene, index) => (
            <div
              key={scene.id}
              onClick={() => {
                setCurrentScene(index)
              }}
              className={`group p-5 rounded-xl border-2 cursor-pointer transition-all duration-300 hover:scale-[1.02] ${
                currentScene === index
                  ? "border-purple-400 bg-gradient-to-r from-purple-50 to-pink-50 shadow-lg shadow-purple-100/50"
                  : "border-slate-200 hover:border-purple-300 hover:bg-gradient-to-r hover:from-purple-50/50 hover:to-pink-50/50 hover:shadow-md"
              }`}
            >
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <h3 className="font-bold text-slate-800 group-hover:text-purple-700 transition-colors duration-300">{scene.title}</h3>
                  <Badge className="bg-gradient-to-r from-emerald-100 to-green-100 text-emerald-800 border-emerald-200 font-semibold px-2 py-1">
                    <Clock className="w-3 h-3 mr-1" />
                    {scene.duration}s
                  </Badge>
                  {scene.shotData && scene.shotData.hasImages && (
                    <Badge className="bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 border-blue-200 font-semibold px-2 py-1">
                      <ImageIcon className="w-3 h-3 mr-1" />
                      {scene.shotData.imageCount}张
                    </Badge>
                  )}
                  {/* 缓存状态指示器 */}
                  {scene.shotData?.shotId && (() => {
                    const cachedStatus = getCachedVideoStatus(scene.shotData.shotId)
                    if (cachedStatus && Date.now() < cachedStatus.expiresAt) {
                      const cacheAge = Math.round((Date.now() - cachedStatus.cachedAt) / 1000)
                      return (
                        <Badge className="bg-gradient-to-r from-amber-100 to-yellow-100 text-amber-800 border-amber-200 font-semibold px-2 py-1" title={`缓存状态: ${cachedStatus.status}, ${cacheAge}秒前`}>
                          💾 {cacheAge}s
                        </Badge>
                      )
                    }
                    return null
                  })()}

                </div>
                <div className="flex items-center space-x-2">
                  <Badge
                    variant={scene.generatedVideos.length > 0 ? "default" : "secondary"}
                    className={scene.generatedVideos.length > 0
                      ? "bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border-green-200"
                      : "bg-gradient-to-r from-purple-100 to-pink-100 text-purple-800 border-purple-200"
                    }
                  >
                    {scene.generatedVideos.length > 0 ? `${scene.generatedVideos.length}视频` : '未生成'}
                  </Badge>

                </div>
              </div>
              <p className="text-sm text-gray-600 line-clamp-2">{scene.description}</p>
              <div className="mt-2 flex items-center space-x-2">
                <div className="w-12 h-8 bg-gray-200 rounded border flex items-center justify-center overflow-hidden">
                  {scene.primaryImage ? (
                    <img
                      src={scene.primaryImage.url}
                      alt="基础图片"
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <ImageIcon className="w-3 h-3 text-gray-500" />
                  )}
                </div>
                <div className="text-xs text-gray-500">→</div>
                <div className={`w-12 h-8 rounded border flex items-center justify-center ${
                  scene.generatedVideos.length > 0
                    ? "bg-gradient-to-r from-green-100 to-emerald-100 border-green-200"
                    : "bg-gradient-to-r from-blue-100 to-purple-100 border-blue-200"
                }`}>
                  {scene.generatedVideos.length > 0 ? (
                    <Video className="w-3 h-3 text-green-600" />
                  ) : (
                    <Video className="w-3 h-3 text-blue-600" />
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-xl border border-blue-200 shadow-sm">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-2">
              <Clock className="w-5 h-5 text-blue-600" />
              <span className="text-sm font-bold text-blue-900">总时长</span>
            </div>
            <span className="text-2xl font-bold text-blue-700 bg-white px-3 py-1 rounded-full shadow-sm">{totalDuration}s</span>
          </div>
          <div className="flex items-center space-x-2">
            <Zap className="w-4 h-4 text-indigo-600" />
            <span className="text-sm font-medium text-indigo-700">预计生成时间: {Math.ceil(scenes.length * 2)}分钟</span>
          </div>
        </div>

        {/* 批量操作区域 */}
        <div className="mt-8 space-y-4">
          <Button
            onClick={handleGenerateAllVideos}
            disabled={isGenerating}
            className="group w-full h-14 bg-gradient-to-r from-purple-600 via-pink-600 to-red-500 hover:from-purple-700 hover:via-pink-700 hover:to-red-600 text-white font-bold text-lg shadow-xl hover:shadow-2xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed hover:scale-105 disabled:hover:scale-100"
          >
            <Zap className="w-5 h-5 mr-3 group-hover:scale-110 transition-transform duration-300" />
            <div className="flex flex-col items-start">
              <span>{isGenerating ? `批量生成中... ${Math.round(generationProgress)}%` : '批量生成所有视频'}</span>
              {!isGenerating && <span className="text-xs opacity-80">一键将所有图片转换为视频</span>}
            </div>
          </Button>


        </div>
      </div>

      {/* 右侧编辑和预览区域 */}
      <div className="flex-1 p-8 bg-gradient-to-br from-white/60 to-slate-50/80 backdrop-blur-sm">
        <div className="h-full flex flex-col">
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center">
                <Video className="w-4 h-4 text-white" />
              </div>
              <div>
                <h3 className="text-2xl font-bold text-slate-800">
                  {scenes[currentScene]?.title}
                </h3>
                <p className="text-sm text-slate-600 mt-1">{scenes[currentScene]?.description}</p>
              </div>
            </div>
            <div className="flex space-x-3">
              {/* 刷新视频按钮 */}
              <Button
                variant="outline"
                onClick={() => {
                  handleRefreshVideo(scenes[currentScene].id)
                }}
                disabled={isGenerating}
                className="group transition-all duration-300 bg-gradient-to-r from-orange-50 to-amber-50 hover:from-orange-100 hover:to-amber-100 text-orange-700 border-orange-200 hover:border-orange-300 hover:shadow-md"
                title="查询视频生成状态并刷新"
              >
                <RefreshCw className="w-4 h-4 mr-2 group-hover:scale-110 transition-transform duration-300" />
                <span className="font-medium">刷新视频</span>
              </Button>



              <Button
                variant="outline"
                onClick={() => handleGenerateVideo()}
                disabled={isGenerating}
                className="group bg-white/80 border-slate-300 hover:border-purple-400 hover:bg-purple-50 hover:text-purple-700 transition-all duration-300"
              >
                <RefreshCw className={`w-4 h-4 mr-2 group-hover:scale-110 transition-transform duration-300 ${isGenerating ? 'animate-spin' : ''}`} />
                <span className="font-medium">{isGenerating ? '生成中...' : '重新生成'}</span>
              </Button>
              <Button
                onClick={() => handleGenerateVideo()}
                disabled={isGenerating}
                className="group bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-bold shadow-xl hover:shadow-2xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed hover:scale-105 disabled:hover:scale-100 px-6"
              >
                <Video className="w-4 h-4 mr-2 group-hover:scale-110 transition-transform duration-300" />
                <span>{isGenerating ? `生成中... ${Math.round(generationProgress)}%` : '生成视频'}</span>
              </Button>
            </div>
          </div>

          {/* 上传进度条 */}
          {isUploading && (
            <div className="mb-6 p-4 bg-gradient-to-r from-emerald-50 to-green-50 border border-emerald-200 rounded-xl shadow-sm">
              <div className="flex justify-between items-center mb-3">
                <div className="flex items-center space-x-2">
                  <div className="w-5 h-5 rounded-full bg-emerald-100 flex items-center justify-center">
                    <Upload className="w-3 h-3 text-emerald-600 animate-bounce" />
                  </div>
                  <span className="text-sm font-semibold text-emerald-800">上传中...</span>
                </div>
                <span className="text-sm font-bold text-emerald-700 bg-white px-2 py-1 rounded-full">
                  {Math.round(uploadProgress)}%
                </span>
              </div>
              <div className="w-full bg-emerald-200 rounded-full h-2 overflow-hidden">
                <div
                  className="bg-gradient-to-r from-emerald-500 to-green-500 h-2 rounded-full transition-all duration-500 shadow-sm"
                  style={{ width: `${uploadProgress}%` }}
                />
              </div>
            </div>
          )}

          {/* Supabase上传进度条 */}
          {isUploadingToImgbb && (
            <div className="mb-6 p-4 bg-gradient-to-r from-orange-50 to-amber-50 border border-orange-200 rounded-xl shadow-sm">
              <div className="flex justify-between items-center mb-3">
                <div className="flex items-center space-x-2">
                  <div className="w-5 h-5 rounded-full bg-orange-100 flex items-center justify-center">
                    <RefreshCw className="w-3 h-3 text-orange-600 animate-spin" />
                  </div>
                  <span className="text-sm font-semibold text-orange-800">上传到Supabase Storage中...</span>
                </div>
                <span className="text-sm font-bold text-orange-700 bg-white px-2 py-1 rounded-full">
                  处理中
                </span>
              </div>
              <div className="w-full bg-orange-200 rounded-full h-2 overflow-hidden">
                <div className="bg-gradient-to-r from-orange-500 to-amber-500 h-2 rounded-full animate-pulse shadow-sm" />
              </div>
            </div>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 flex-1">
            {/* 视频生成设置区 */}
            <div>
              <Card className="h-full bg-white/90 backdrop-blur-sm border-slate-200/60 shadow-xl hover:shadow-2xl transition-all duration-300">
                <CardHeader className="pb-3 border-b border-slate-100">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 rounded-xl bg-gradient-to-br from-purple-500 via-pink-500 to-red-500 flex items-center justify-center shadow-lg">
                      <Video className="w-4 h-4 text-white" />
                    </div>
                    <CardTitle className="text-xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
                      视频生成设置
                    </CardTitle>
                  </div>
                </CardHeader>
                <CardContent className="p-6 space-y-4">
                  {/* 视频提示词编辑 */}
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 rounded-full bg-gradient-to-r from-purple-400 to-pink-400"></div>
                      <Label className="text-base font-semibold text-slate-700">运动描述</Label>
                    </div>
                    <Textarea
                      placeholder="描述画面中的运动、相机移动、物体动作等..."
                      value={scenes[currentScene]?.videoPrompt || ''}
                      onChange={(e) => updateScenePrompt(scenes[currentScene]?.id, e.target.value)}
                      className="h-28 resize-none bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-white border-purple-200/60 focus:border-purple-400 focus:ring-2 focus:ring-purple-100 rounded-xl transition-all duration-200"
                    />
                  </div>

                  {/* 视频设置区域 - 统一背景框 */}
                  <div className="bg-gradient-to-br from-slate-50/80 via-gray-50/60 to-white rounded-xl border border-slate-200/60 p-5 shadow-inner space-y-4">
                    {/* 基础设置 */}
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2 mb-3">
                        <div className="w-1.5 h-1.5 rounded-full bg-gradient-to-r from-blue-400 to-indigo-400"></div>
                        <span className="text-sm font-semibold text-slate-600">基础参数</span>
                      </div>

                      <div className="space-y-4">
                        {/* 第一行：视频比例和生成模式 */}
                        <div className="grid grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <Label className="text-sm font-medium text-slate-600 flex items-center space-x-1">
                              <span>📐</span>
                              <span>视频比例</span>
                            </Label>
                            <Select value={videoRatio} onValueChange={setVideoRatio}>
                              <SelectTrigger className="h-10 bg-gradient-to-r from-purple-50 to-pink-50 border-purple-200 hover:border-purple-300 focus:border-purple-400 focus:ring-2 focus:ring-purple-100 transition-all duration-300 ease-in-out shadow-sm hover:shadow-md rounded-lg">
                                <SelectValue className="text-gray-600 font-medium" />
                              </SelectTrigger>
                              <SelectContent className="bg-white/95 backdrop-blur-sm border border-purple-200 shadow-xl rounded-xl p-1 animate-in fade-in-0 zoom-in-95 duration-200 ease-out">
                                <SelectItem value="16:9" className="rounded-lg my-1 px-3 py-2.5 text-sm font-medium text-gray-700 hover:bg-gradient-to-r hover:from-purple-100 hover:to-pink-100 hover:text-purple-700 focus:bg-gradient-to-r focus:from-purple-100 focus:to-pink-100 focus:text-purple-700 transition-all duration-200 ease-in-out cursor-pointer">
                                  📺 16:9 (横屏)
                                </SelectItem>
                                <SelectItem value="9:16" className="rounded-lg my-1 px-3 py-2.5 text-sm font-medium text-gray-700 hover:bg-gradient-to-r hover:from-purple-100 hover:to-pink-100 hover:text-purple-700 focus:bg-gradient-to-r focus:from-purple-100 focus:to-pink-100 focus:text-purple-700 transition-all duration-200 ease-in-out cursor-pointer">
                                  📱 9:16 (竖屏)
                                </SelectItem>
                                <SelectItem value="1:1" className="rounded-lg my-1 px-3 py-2.5 text-sm font-medium text-gray-700 hover:bg-gradient-to-r hover:from-purple-100 hover:to-pink-100 hover:text-purple-700 focus:bg-gradient-to-r focus:from-purple-100 focus:to-pink-100 focus:text-purple-700 transition-all duration-200 ease-in-out cursor-pointer">
                                  ⬜ 1:1 (正方形)
                                </SelectItem>
                                <SelectItem value="4:3" className="rounded-lg my-1 px-3 py-2.5 text-sm font-medium text-gray-700 hover:bg-gradient-to-r hover:from-purple-100 hover:to-pink-100 hover:text-purple-700 focus:bg-gradient-to-r focus:from-purple-100 focus:to-pink-100 focus:text-purple-700 transition-all duration-200 ease-in-out cursor-pointer">
                                  🖥️ 4:3 (传统)
                                </SelectItem>
                              </SelectContent>
                            </Select>
                          </div>

                          <div className="space-y-2">
                            <Label className="text-sm font-medium text-slate-600 flex items-center space-x-1">
                              <span>⚡</span>
                              <span>生成模式</span>
                            </Label>
                            <Select value={mode} onValueChange={setMode}>
                              <SelectTrigger className="h-10 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200 hover:border-blue-300 focus:border-blue-400 focus:ring-2 focus:ring-blue-100 transition-all duration-300 ease-in-out shadow-sm hover:shadow-md rounded-lg">
                                <SelectValue className="text-gray-600 font-medium" />
                              </SelectTrigger>
                              <SelectContent className="bg-white/95 backdrop-blur-sm border border-blue-200 shadow-xl rounded-xl p-1 animate-in fade-in-0 zoom-in-95 duration-200 ease-out">
                                <SelectItem value="std" className="rounded-lg my-1 px-3 py-2.5 text-sm font-medium text-gray-700 hover:bg-gradient-to-r hover:from-blue-100 hover:to-indigo-100 hover:text-blue-700 focus:bg-gradient-to-r focus:from-blue-100 focus:to-indigo-100 focus:text-blue-700 transition-all duration-200 ease-in-out cursor-pointer">
                                  🎯 标准模式 (性价比高)
                                </SelectItem>
                                <SelectItem value="pro" className="rounded-lg my-1 px-3 py-2.5 text-sm font-medium text-gray-700 hover:bg-gradient-to-r hover:from-blue-100 hover:to-indigo-100 hover:text-blue-700 focus:bg-gradient-to-r focus:from-blue-100 focus:to-indigo-100 focus:text-blue-700 transition-all duration-200 ease-in-out cursor-pointer">
                                  💎 专家模式 (高品质)
                                </SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>

                        {/* 模型选择 */}
                        <div className="space-y-2">
                          <Label className="text-sm font-medium text-slate-600 flex items-center space-x-1">
                            <span>🤖</span>
                            <span>AI模型</span>
                          </Label>
                          <Select value={modelName} onValueChange={setModelName}>
                            <SelectTrigger className="h-10 bg-gradient-to-r from-rose-50 to-pink-50 border-rose-200 hover:border-rose-300 focus:border-rose-400 focus:ring-2 focus:ring-rose-100 transition-all duration-300 ease-in-out shadow-sm hover:shadow-md rounded-lg">
                              <SelectValue className="text-gray-600 font-medium" />
                            </SelectTrigger>
                            <SelectContent className="bg-white/95 backdrop-blur-sm border border-rose-200 shadow-xl rounded-xl p-1 animate-in fade-in-0 zoom-in-95 duration-200 ease-out">
                              <SelectItem value="kling-v1" className="rounded-lg my-1 px-3 py-2.5 text-sm font-medium text-gray-700 hover:bg-gradient-to-r hover:from-rose-100 hover:to-pink-100 hover:text-rose-700 focus:bg-gradient-to-r focus:from-rose-100 focus:to-pink-100 focus:text-rose-700 transition-all duration-200 ease-in-out cursor-pointer">
                                🚀 Kling V1 (标准)
                              </SelectItem>
                              <SelectItem value="kling-v1-5" className="rounded-lg my-1 px-3 py-2.5 text-sm font-medium text-gray-700 hover:bg-gradient-to-r hover:from-rose-100 hover:to-pink-100 hover:text-rose-700 focus:bg-gradient-to-r focus:from-rose-100 focus:to-pink-100 focus:text-rose-700 transition-all duration-200 ease-in-out cursor-pointer">
                                ⭐ Kling V1.5 (增强)
                              </SelectItem>
                              <SelectItem value="kling-v1-6" className="rounded-lg my-1 px-3 py-2.5 text-sm font-medium text-gray-700 hover:bg-gradient-to-r hover:from-rose-100 hover:to-pink-100 hover:text-rose-700 focus:bg-gradient-to-r focus:from-rose-100 focus:to-pink-100 focus:text-rose-700 transition-all duration-200 ease-in-out cursor-pointer">
                                🔥 Kling V1.6 (优化)
                              </SelectItem>
                              <SelectItem value="kling-v2-master" className="rounded-lg my-1 px-3 py-2.5 text-sm font-medium text-gray-700 hover:bg-gradient-to-r hover:from-rose-100 hover:to-pink-100 hover:text-rose-700 focus:bg-gradient-to-r focus:from-rose-100 focus:to-pink-100 focus:text-rose-700 transition-all duration-200 ease-in-out cursor-pointer">
                                💎 Kling V2 Master (专业)
                              </SelectItem>
                              <SelectItem value="kling-v2-1" className="rounded-lg my-1 px-3 py-2.5 text-sm font-medium text-gray-700 hover:bg-gradient-to-r hover:from-rose-100 hover:to-pink-100 hover:text-rose-700 focus:bg-gradient-to-r focus:from-rose-100 focus:to-pink-100 focus:text-rose-700 transition-all duration-200 ease-in-out cursor-pointer">
                                ✨ Kling V2.1 (最新)
                              </SelectItem>
                              <SelectItem value="kling-v2-1-master" className="rounded-lg my-1 px-3 py-2.5 text-sm font-medium text-gray-700 hover:bg-gradient-to-r hover:from-rose-100 hover:to-pink-100 hover:text-rose-700 focus:bg-gradient-to-r focus:from-rose-100 focus:to-pink-100 focus:text-rose-700 transition-all duration-200 ease-in-out cursor-pointer">
                                👑 Kling V2.1 Master (旗舰)
                              </SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </div>

                    {/* 动画设置 */}
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2 mb-3">
                        <div className="w-1.5 h-1.5 rounded-full bg-gradient-to-r from-green-400 to-emerald-400"></div>
                        <span className="text-sm font-semibold text-slate-600">动画控制</span>
                      </div>

                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label className="text-sm font-medium text-slate-600 flex items-center space-x-1">
                            <span>🎭</span>
                            <span>动画强度</span>
                          </Label>
                          <div className="px-4 py-3 bg-gradient-to-br from-blue-50/80 via-indigo-50/60 to-white rounded-lg border border-blue-200/60 shadow-sm">
                            <Slider
                              value={animationStrength}
                              onValueChange={setAnimationStrength}
                              max={100}
                              min={0}
                              step={10}
                              className="w-full"
                            />
                            <div className="flex justify-between items-center text-xs text-slate-600 mt-2">
                              <span className="font-medium">静态</span>
                              <span className="font-bold text-blue-700 bg-white px-3 py-1 rounded-full shadow-sm border border-blue-100">
                                {animationStrength[0]}%
                              </span>
                              <span className="font-medium">动态</span>
                            </div>
                          </div>
                        </div>

                        <div className="space-y-2">
                          <Label className="text-sm font-medium text-slate-600 flex items-center space-x-1">
                            <span>🌊</span>
                            <span>运动幅度</span>
                          </Label>
                          <div className="px-4 py-3 bg-gradient-to-br from-green-50/80 via-emerald-50/60 to-white rounded-lg border border-green-200/60 shadow-sm">
                            <Slider
                              value={motionScale}
                              onValueChange={setMotionScale}
                              max={100}
                              min={0}
                              step={10}
                              className="w-full"
                            />
                            <div className="flex justify-between items-center text-xs text-slate-600 mt-2">
                              <span className="font-medium">微动</span>
                              <span className="font-bold text-green-700 bg-white px-3 py-1 rounded-full shadow-sm border border-green-100">
                                {motionScale[0]}%
                              </span>
                              <span className="font-medium">大幅</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* 高级设置 */}
                    <div className="space-y-3">
                      <div className="flex items-center space-x-2 mb-3">
                        <div className="w-1.5 h-1.5 rounded-full bg-gradient-to-r from-amber-400 to-orange-400"></div>
                        <span className="text-sm font-semibold text-slate-600">高级选项</span>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label className="text-sm font-medium text-slate-600 flex items-center space-x-1">
                            <span>🎲</span>
                            <span>随机种子</span>
                            <span className="text-xs text-slate-400">(可选)</span>
                          </Label>
                          <Input
                            placeholder="留空随机生成"
                            value={seed}
                            onChange={(e) => setSeed(e.target.value)}
                            className="h-10 bg-white/80 border-slate-200 hover:border-purple-300 focus:border-purple-400 focus:ring-2 focus:ring-purple-100 rounded-lg transition-all duration-200"
                          />
                        </div>

                        <div className="space-y-2">
                          <Label className="text-sm font-medium text-slate-600 flex items-center space-x-1">
                            <span>⏱️</span>
                            <span>视频时长</span>
                            <span className="text-xs bg-green-100 text-green-700 px-2 py-0.5 rounded-full">统一5秒</span>
                          </Label>
                          <div className="relative">
                            <Input
                              type="number"
                              value={5}
                              readOnly
                              className="h-10 bg-gradient-to-br from-green-50/80 via-emerald-50/60 to-white border-green-200/60 text-slate-600 cursor-not-allowed rounded-lg"
                              title="时长已统一设置为5秒"
                            />
                            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                              <span className="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full font-medium shadow-sm">
                                5秒
                              </span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* 视频预览区 */}
            <div>
              <Card className="h-full bg-white/80 backdrop-blur-sm border-slate-200/60 shadow-xl">
                <CardHeader className="pb-4">
                  <div className="flex items-center space-x-2">
                    <div className="w-6 h-6 rounded-lg bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center">
                      <Play className="w-3 h-3 text-white" />
                    </div>
                    <CardTitle className="text-lg font-bold text-slate-800">视频预览</CardTitle>
                  </div>
                </CardHeader>
                <CardContent className="h-full flex flex-col">
                  <div className="flex-1">
                    <div className="aspect-video bg-gradient-to-br from-slate-100 to-gray-200 rounded-xl flex items-center justify-center relative overflow-hidden border-2 border-dashed border-slate-300 shadow-inner">
                      {scenes[currentScene]?.generatedVideos?.length > 0 ? (
                        <video
                          src={scenes[currentScene].generatedVideos[0].url}
                          className="w-full h-full object-cover rounded-xl shadow-lg"
                          controls
                          poster={scenes[currentScene]?.primaryImage?.url || scenes[currentScene]?.baseImage}
                        />
                      ) : (
                        <div className="text-center">
                          <div className="w-20 h-20 rounded-full bg-gradient-to-br from-slate-200 to-gray-300 flex items-center justify-center mx-auto mb-4">
                            <Video className="w-10 h-10 text-slate-400" />
                          </div>
                          <h4 className="text-lg font-bold text-slate-700 mb-2">视频预览区域</h4>
                          <p className="text-sm text-slate-500 max-w-sm mx-auto leading-relaxed">
                            {scenes[currentScene]?.primaryImage || (scenes[currentScene]?.baseImage && scenes[currentScene]?.baseImage !== "/placeholder.svg")
                              ? '点击生成视频按钮，AI将为您创建精彩的动态视频'
                              : '请先上传基础图片，然后生成视频'
                            }
                          </p>
                        </div>
                      )}

                      {/* 播放控制 - 只在没有生成视频时显示 */}
                      {(!scenes[currentScene]?.generatedVideos || scenes[currentScene]?.generatedVideos.length === 0) && (
                        <div className="absolute bottom-4 left-4 right-4">
                          <div className="flex items-center space-x-4 bg-black/80 backdrop-blur-sm rounded-xl p-4 shadow-xl">
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => setIsPlaying(!isPlaying)}
                              className="text-white hover:bg-white/20 rounded-lg"
                              disabled
                            >
                              {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                            </Button>
                            <div className="flex-1 bg-white/20 rounded-full h-2">
                              <div className="bg-gradient-to-r from-purple-400 to-pink-400 rounded-full h-2 w-0 transition-all duration-200"></div>
                            </div>
                            <span className="text-white text-sm font-medium">0:00 / 0:05</span>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="mt-6 space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <ImageIcon className="w-4 h-4 text-slate-600" />
                        <span className="text-sm font-bold text-slate-700">基础图片</span>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        className="group bg-white/70 border-slate-300 hover:border-purple-400 hover:bg-purple-50 hover:text-purple-700 transition-all duration-300"
                      >
                        <Settings className="w-3 h-3 mr-1 group-hover:rotate-90 transition-transform duration-300" />
                        <span className="font-medium">调整</span>
                      </Button>
                    </div>
                    <div className="aspect-video bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl border-2 border-dashed border-blue-300 flex items-center justify-center overflow-hidden shadow-inner">
                      {scenes[currentScene]?.primaryImage ? (
                        <img
                          src={scenes[currentScene].primaryImage.url}
                          alt="基础图片预览"
                          className="w-full h-full object-cover rounded-lg shadow-sm"
                        />
                      ) : (
                        <div className="text-center">
                          <div className="w-12 h-12 rounded-full bg-blue-100 flex items-center justify-center mx-auto mb-2">
                            <ImageIcon className="w-6 h-6 text-blue-600" />
                          </div>
                          <p className="text-xs font-medium text-blue-700">源图片预览</p>
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="mt-6 flex space-x-3">
                    <Button
                      variant="outline"
                      size="sm"
                      className="group flex-1 bg-white/70 border-slate-300 hover:border-emerald-400 hover:bg-emerald-50 hover:text-emerald-700 transition-all duration-300"
                    >
                      <Download className="w-3 h-3 mr-2 group-hover:scale-110 transition-transform duration-300" />
                      <span className="font-medium">下载</span>
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="group flex-1 bg-white/70 border-slate-300 hover:border-blue-400 hover:bg-blue-50 hover:text-blue-700 transition-all duration-300"
                    >
                      <Save className="w-3 h-3 mr-2 group-hover:scale-110 transition-transform duration-300" />
                      <span className="font-medium">保存</span>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
          
          {/* 生成进度条 */}
          {isGenerating && (
            <Card className="mt-6 border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50">
              <CardContent className="p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm font-medium text-blue-900">生成进度</span>
                  <span className="text-sm font-bold text-blue-700">{generationProgress}%</span>
                </div>
                <div className="w-full bg-blue-200 rounded-full h-2">
                  <div 
                    className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${generationProgress}%` }}
                  />
                </div>
                <p className="text-xs text-blue-700 mt-2">正在生成视频，请稍候...</p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
} 