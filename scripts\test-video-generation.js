#!/usr/bin/env node

/**
 * 图生视频功能完整测试脚本
 * 测试数据库连接、API端点、Kling AI连接等
 */

// 加载环境变量
require('dotenv').config({ path: '.env.local' });
require('dotenv').config();

// 使用内置的fetch (Node.js 18+)
const fetch = globalThis.fetch;

console.log('🎬 图生视频功能完整测试');
console.log('='.repeat(60));

// 测试配置
const API_BASE_URL = 'http://localhost:3000';
const TEST_IMAGE_URL = 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=512&h=512&fit=crop';

async function runAllTests() {
  console.log('\n🔍 开始运行所有测试...\n');

  // 1. 环境变量检查
  await testEnvironmentVariables();
  
  // 2. 数据库连接测试
  await testDatabaseConnection();
  
  // 3. API健康检查
  await testAPIHealth();
  
  // 4. Supabase连接测试
  await testSupabaseConnection();
  
  // 5. Kling AI连接测试
  await testKlingAIConnection();
  
  console.log('\n' + '='.repeat(60));
  console.log('✅ 所有测试完成！');
}

// 1. 环境变量检查
async function testEnvironmentVariables() {
  console.log('📋 1. 环境变量检查');
  console.log('-'.repeat(40));
  
  const requiredVars = [
    'KLING_ACCESS_KEY',
    'KLING_SECRET_KEY',
    'DATABASE_URL',
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY'
  ];
  
  let allSet = true;
  
  requiredVars.forEach(varName => {
    const value = process.env[varName];
    if (value) {
      const maskedValue = value.length > 20 ? 
        `${value.substring(0, 15)}...${value.substring(value.length - 5)}` : 
        value.substring(0, 15) + '...';
      console.log(`✅ ${varName}: ${maskedValue}`);
    } else {
      console.log(`❌ ${varName}: 未设置`);
      allSet = false;
    }
  });
  
  if (allSet) {
    console.log('✅ 所有环境变量都已正确设置');
  } else {
    console.log('❌ 部分环境变量缺失');
  }
  
  console.log('');
}

// 2. 数据库连接测试
async function testDatabaseConnection() {
  console.log('🗄️  2. 数据库连接测试');
  console.log('-'.repeat(40));
  
  try {
    const { PrismaClient } = require('../lib/generated/prisma');
    const prisma = new PrismaClient();
    
    await prisma.$connect();
    console.log('✅ 数据库连接成功');
    
    // 检查相关表
    const taskCount = await prisma.image_generation_tasks.count();
    const imageCount = await prisma.generated_images.count();
    
    console.log(`📊 图像生成任务: ${taskCount} 条记录`);
    console.log(`📊 生成图像: ${imageCount} 条记录`);
    
    await prisma.$disconnect();
    
  } catch (error) {
    console.log(`❌ 数据库连接失败: ${error.message}`);
  }
  
  console.log('');
}

// 3. API健康检查
async function testAPIHealth() {
  console.log('🌐 3. API健康检查');
  console.log('-'.repeat(40));
  
  try {
    const response = await fetch(`${API_BASE_URL}/api/generate-video`);
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ API端点正常工作');
      console.log(`📋 访问密钥: ${data.hasAccessKey ? '已配置' : '未配置'}`);
      console.log(`📋 秘密密钥: ${data.hasSecretKey ? '已配置' : '未配置'}`);
      console.log(`⏰ 响应时间: ${data.timestamp}`);
    } else {
      console.log(`❌ API端点异常: ${response.status}`);
    }
    
  } catch (error) {
    console.log(`❌ API连接失败: ${error.message}`);
    console.log('💡 请确保开发服务器正在运行 (npm run dev)');
  }
  
  console.log('');
}

// 4. Supabase连接测试
async function testSupabaseConnection() {
  console.log('☁️  4. Supabase连接测试');
  console.log('-'.repeat(40));
  
  if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
    console.log('⚠️  Supabase环境变量未配置，跳过测试');
    console.log('');
    return;
  }
  
  try {
    const { createClient } = require('@supabase/supabase-js');
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
    );
    
    const { data: buckets, error } = await supabase.storage.listBuckets();
    
    if (error) {
      console.log(`❌ Supabase连接失败: ${error.message}`);
    } else {
      console.log(`✅ Supabase连接成功`);
      console.log(`📦 存储桶数量: ${buckets.length}`);
      
      if (buckets.length > 0) {
        buckets.forEach(bucket => {
          console.log(`   - ${bucket.name} (${bucket.public ? '公开' : '私有'})`);
        });
      }
    }
    
  } catch (error) {
    console.log(`❌ Supabase测试失败: ${error.message}`);
  }
  
  console.log('');
}

// 5. Kling AI连接测试
async function testKlingAIConnection() {
  console.log('🤖 5. Kling AI连接测试');
  console.log('-'.repeat(40));
  
  if (!process.env.KLING_ACCESS_KEY || !process.env.KLING_SECRET_KEY) {
    console.log('⚠️  Kling AI环境变量未配置，跳过测试');
    console.log('');
    return;
  }
  
  try {
    const jwt = require('jsonwebtoken');
    
    // 生成JWT Token
    const token = jwt.sign(
      {
        iss: process.env.KLING_ACCESS_KEY,
        exp: Math.floor(Date.now() / 1000) + 1800,
        nbf: Math.floor(Date.now() / 1000) - 5
      },
      process.env.KLING_SECRET_KEY,
      { algorithm: 'HS256' }
    );
    
    // 测试API连接（获取账户信息或其他轻量级操作）
    const testUrl = 'https://api-beijing.klingai.com/v1/videos/image2video';
    
    console.log('🔗 测试Kling AI API连接...');
    
    // 这里我们只测试连接，不实际创建视频任务
    const response = await fetch(testUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model_name: 'kling-v1',
        image: TEST_IMAGE_URL,
        prompt: 'Test connection',
        duration: 5,
        aspect_ratio: '16:9'
      })
    });
    
    console.log(`📡 HTTP状态: ${response.status}`);
    
    if (response.status === 200 || response.status === 201) {
      console.log('✅ Kling AI API连接成功');
      const data = await response.json();
      if (data.data && data.data.task_id) {
        console.log(`📋 测试任务ID: ${data.data.task_id}`);
        console.log('💡 注意: 这是一个测试任务，可能会消耗配额');
      }
    } else if (response.status === 401) {
      console.log('❌ Kling AI认证失败，请检查访问密钥和秘密密钥');
    } else if (response.status === 429) {
      console.log('⚠️  Kling AI请求频率限制，但连接正常');
    } else {
      const errorText = await response.text();
      console.log(`❌ Kling AI API错误: ${errorText}`);
    }
    
  } catch (error) {
    console.log(`❌ Kling AI连接测试失败: ${error.message}`);
  }
  
  console.log('');
}

// 运行所有测试
runAllTests().catch(console.error);
