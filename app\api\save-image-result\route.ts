import { NextRequest, NextResponse } from 'next/server'
import { createImageGenerationTask, createGeneratedImage, updateImageGenerationTask } from '@/lib/prisma-image-operations'
import { z } from 'zod'

const saveImageResultSchema = z.object({
  projectId: z.string().uuid(),
  shotId: z.string().uuid(),
  userId: z.string().uuid(),
  prompt: z.string().min(1),
  negativePrompt: z.string().optional(),
  images: z.array(z.object({
    url: z.string().url(),
    width: z.number(),
    height: z.number(),
    filename: z.string().optional(),
  })),
  taskName: z.string().optional(),
  modelName: z.string().optional(),
  aspectRatio: z.string().optional(),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const validatedData = saveImageResultSchema.parse(body)

    console.log('💾 [SAVE-IMAGE] Saving image generation results...')
    console.log('💾 [SAVE-IMAGE] Data:', JSON.stringify(validatedData, null, 2))

    // 1. Create image generation task record
    const task = await createImageGenerationTask({
      projectId: validatedData.projectId,
      shotId: validatedData.shotId,
      userId: validatedData.userId,
      taskName: validatedData.taskName || `Image generation for shot ${validatedData.shotId}`,
      prompt: validatedData.prompt,
      negativePrompt: validatedData.negativePrompt,
      modelName: validatedData.modelName || 'kling-v1-5',
      aspectRatio: validatedData.aspectRatio || '16:9',
      status: 'completed',
    })

    console.log('💾 [SAVE-IMAGE] Created task:', task.id)

    // 2. Save each generated image to database
    const savedImages = []
    for (let i = 0; i < validatedData.images.length; i++) {
      const image = validatedData.images[i]
      const isPrimary = i === 0 // First image is primary

      const savedImage = await createGeneratedImage({
        taskId: task.id,
        imageUrl: image.url,
        imageFilename: image.filename || `generated-${Date.now()}-${i + 1}.jpg`,
        imageWidth: image.width,
        imageHeight: image.height,
        imageFormat: 'jpg',
        isPrimary,
        cdnUrl: image.url, // Use the URL as CDN URL for now
        storagePath: image.url,
      })

      savedImages.push(savedImage)
      console.log(`💾 [SAVE-IMAGE] Saved image ${i + 1}:`, savedImage.id)
    }

    // 3. Update task as completed
    await updateImageGenerationTask(task.id, {
      status: 'completed',
      completedAt: new Date(),
    })

    console.log('💾 [SAVE-IMAGE] Successfully saved all images')

    return NextResponse.json({
      success: true,
      taskId: task.id,
      images: savedImages,
    })

  } catch (error) {
    console.error('💥 [SAVE-IMAGE] Error saving image results:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: 'Invalid request data',
        details: error.errors,
      }, { status: 400 })
    }

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Failed to save image results',
    }, { status: 500 })
  }
}