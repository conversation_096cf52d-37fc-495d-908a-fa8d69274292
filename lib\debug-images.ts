import { prisma } from './prisma'

// Debug script to check image data
export async function debugImageData() {
  try {
    console.log('🔍 [DEBUG] Checking database for images...')
    
    // 1. Check if there are any image generation tasks
    const tasks = await prisma.image_generation_tasks.findMany({
      include: {
        generated_images: true,
        script_shots: {
          include: {
            script_projects: true
          }
        }
      },
      take: 10
    })
    
    console.log('🔍 [DEBUG] Found', tasks.length, 'image generation tasks')
    
    if (tasks.length === 0) {
      console.log('🔍 [DEBUG] No image generation tasks found')
      console.log('🔍 [DEBUG] This confirms the issue: images are generated but never saved to database')
      return
    }
    
    // 2. Check generated images
    const images = await prisma.generated_images.findMany({
      take: 10,
      include: {
        image_generation_tasks: {
          include: {
            script_shots: true
          }
        }
      }
    })
    
    console.log('🔍 [DEBUG] Found', images.length, 'generated images')
    
    tasks.forEach((task, index) => {
      console.log(`🔍 [DEBUG] Task ${index + 1}:`, {
        id: task.id,
        status: task.status,
        prompt: task.prompt.substring(0, 50) + '...',
        imagesCount: task.generated_images.length,
        shot: task.shot_id ? `Shot ${task.shot_id}` : 'No shot assigned',
        project: task.script_shots?.script_projects?.title || 'No project'
      })
    })
    
    images.forEach((image, index) => {
      console.log(`🔍 [DEBUG] Image ${index + 1}:`, {
        id: image.id,
        url: image.image_url,
        cdn_url: image.cdn_url,
        width: image.image_width,
        height: image.image_height,
        task_id: image.task_id
      })
    })
    
  } catch (error) {
    console.error('❌ [DEBUG] Error checking image data:', error)
  }
}

// Run the debug
if (require.main === module) {
  debugImageData().then(() => process.exit())
}