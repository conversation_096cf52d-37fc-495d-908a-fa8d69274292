#!/usr/bin/env node

// 加载环境变量
require('dotenv').config({ path: '.env.local' });
require('dotenv').config(); // 备用加载 .env 文件

const { getTaskList, getTaskDetail, displayTaskInfo } = require('./test-kling-tasks.js');

// 命令行参数解析
function parseArgs() {
  const args = process.argv.slice(2);
  const options = {
    command: 'list',
    status: null,
    page: 1,
    size: 10,
    taskId: null,
    interactive: false
  };

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    switch (arg) {
      case '--help':
      case '-h':
        showHelp();
        process.exit(0);
        break;
      case '--list':
      case '-l':
        options.command = 'list';
        break;
      case '--detail':
      case '-d':
        options.command = 'detail';
        if (args[i + 1] && !args[i + 1].startsWith('-')) {
          options.taskId = args[i + 1];
          i++;
        }
        break;
      case '--status':
      case '-s':
        if (args[i + 1] && !args[i + 1].startsWith('-')) {
          options.status = args[i + 1];
          i++;
        }
        break;
      case '--page':
      case '-p':
        if (args[i + 1] && !args[i + 1].startsWith('-')) {
          options.page = parseInt(args[i + 1]);
          i++;
        }
        break;
      case '--size':
        if (args[i + 1] && !args[i + 1].startsWith('-')) {
          options.size = parseInt(args[i + 1]);
          i++;
        }
        break;
      case '--interactive':
      case '-i':
        options.interactive = true;
        break;
    }
  }

  return options;
}

// 显示帮助信息
function showHelp() {
  console.log(`
🎬 Kling AI 任务管理器

用法:
  node kling-task-manager.js [选项]

选项:
  -h, --help              显示帮助信息
  -l, --list              查询任务列表 (默认)
  -d, --detail <task_id>  查询指定任务详情
  -s, --status <status>   按状态筛选 (submitted|processing|succeed|failed)
  -p, --page <number>     指定页码 (默认: 1)
  --size <number>         每页数量 (默认: 10, 最大: 100)
  -i, --interactive       交互式模式

示例:
  node kling-task-manager.js --list --status succeed --size 5
  node kling-task-manager.js --detail task_12345
  node kling-task-manager.js --interactive

环境变量:
  KLING_ACCESS_KEY        Kling AI Access Key
  KLING_SECRET_KEY        Kling AI Secret Key
`);
}

// 交互式菜单
async function interactiveMode() {
  const readline = require('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  function question(prompt) {
    return new Promise((resolve) => {
      rl.question(prompt, resolve);
    });
  }

  console.log('\n🎬 欢迎使用 Kling AI 任务管理器 (交互式模式)');
  console.log('=====================================');

  while (true) {
    console.log('\n请选择操作:');
    console.log('1. 查询任务列表');
    console.log('2. 查询任务详情');
    console.log('3. 按状态筛选任务');
    console.log('4. 查询最新任务');
    console.log('0. 退出');

    const choice = await question('\n请输入选项 (0-4): ');

    switch (choice) {
      case '1':
        const page = await question('页码 (默认 1): ') || '1';
        const size = await question('每页数量 (默认 10): ') || '10';
        await listTasks({ page: parseInt(page), size: parseInt(size) });
        break;

      case '2':
        const taskId = await question('请输入任务ID: ');
        if (taskId) {
          await getTaskDetailInteractive(taskId);
        } else {
          console.log('❌ 任务ID不能为空');
        }
        break;

      case '3':
        console.log('\n可选状态: submitted, processing, succeed, failed');
        const status = await question('请输入状态: ');
        if (status) {
          await listTasks({ status, size: 10 });
        } else {
          console.log('❌ 状态不能为空');
        }
        break;

      case '4':
        await listTasks({ size: 5 });
        break;

      case '0':
        console.log('\n👋 再见!');
        rl.close();
        return;

      default:
        console.log('❌ 无效选项，请重新选择');
    }
  }
}

// 查询任务列表
async function listTasks(options = {}) {
  try {
    console.log('\n🔍 查询任务列表...');
    const result = await getTaskList(options);

    if (result.code === 0 && result.data && result.data.tasks) {
      const tasks = result.data.tasks;
      console.log(`\n📊 找到 ${tasks.length} 个任务:`);
      console.log('='.repeat(80));

      tasks.forEach((task, index) => {
        const status = getStatusEmoji(task.task_status);
        const createTime = new Date(task.created_at * 1000).toLocaleString();
        console.log(`${index + 1}. ${status} ${task.task_id} | ${createTime}`);
      });

      if (result.data.total) {
        console.log(`\n📈 总计: ${result.data.total} 个任务`);
      }
    } else {
      console.log('❌ 没有找到任务或查询失败');
    }
  } catch (error) {
    console.error('❌ 查询失败:', error.message);
  }
}

// 查询任务详情 (交互式)
async function getTaskDetailInteractive(taskId) {
  try {
    console.log(`\n🔍 查询任务详情: ${taskId}`);
    const result = await getTaskDetail(taskId);

    if (result.code === 0 && result.data) {
      displayTaskInfo(result.data);
    } else {
      console.log('❌ 任务不存在或查询失败');
    }
  } catch (error) {
    console.error('❌ 查询失败:', error.message);
  }
}

// 获取状态表情符号
function getStatusEmoji(status) {
  const statusMap = {
    'submitted': '📝',
    'processing': '⏳',
    'succeed': '✅',
    'failed': '❌'
  };
  return statusMap[status] || '❓';
}

// 统计任务状态
async function getTaskStats() {
  try {
    console.log('\n📊 获取任务统计信息...');
    
    const statuses = ['submitted', 'processing', 'succeed', 'failed'];
    const stats = {};

    for (const status of statuses) {
      const result = await getTaskList({ status, size: 1 });
      if (result.code === 0 && result.data) {
        stats[status] = result.data.total || 0;
      } else {
        stats[status] = 0;
      }
    }

    console.log('\n📈 任务状态统计:');
    console.log('='.repeat(30));
    Object.entries(stats).forEach(([status, count]) => {
      const emoji = getStatusEmoji(status);
      console.log(`${emoji} ${status.padEnd(12)}: ${count}`);
    });

    const total = Object.values(stats).reduce((sum, count) => sum + count, 0);
    console.log('-'.repeat(30));
    console.log(`📊 总计: ${total}`);

  } catch (error) {
    console.error('❌ 获取统计信息失败:', error.message);
  }
}

// 主函数
async function main() {
  const options = parseArgs();

  try {
    if (options.interactive) {
      await interactiveMode();
    } else if (options.command === 'list') {
      await listTasks({
        status: options.status,
        page: options.page,
        size: options.size
      });
    } else if (options.command === 'detail') {
      if (!options.taskId) {
        console.error('❌ 请提供任务ID');
        console.log('用法: node kling-task-manager.js --detail <task_id>');
        process.exit(1);
      }
      await getTaskDetailInteractive(options.taskId);
    }
  } catch (error) {
    console.error('❌ 执行失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  listTasks,
  getTaskDetailInteractive,
  getTaskStats
};
