"use client"

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { RefreshCw, Video, Play } from 'lucide-react'

interface MockScene {
  id: number
  title: string
  description: string
  videoTaskId?: string
  videoTaskStatus?: 'submitted' | 'processing' | 'succeed' | 'failed'
  generatedVideos: Array<{
    id: string
    url: string
    taskId: string
  }>
  lastStatusCheck?: number
}

export default function TestVideoRefreshPage() {
  const [scenes, setScenes] = useState<MockScene[]>([
    {
      id: 1,
      title: "测试场景 1",
      description: "这是一个测试场景，用于演示视频刷新功能",
      videoTaskId: "task_example_123",
      videoTaskStatus: "processing",
      generatedVideos: [],
      lastStatusCheck: Date.now() - 60000 // 1分钟前
    },
    {
      id: 2,
      title: "测试场景 2", 
      description: "这个场景已经有生成的视频",
      videoTaskId: "task_example_456",
      videoTaskStatus: "succeed",
      generatedVideos: [
        {
          id: "video_1",
          url: "https://example.com/video1.mp4",
          taskId: "task_example_456"
        }
      ],
      lastStatusCheck: Date.now() - 30000 // 30秒前
    },
    {
      id: 3,
      title: "测试场景 3",
      description: "这个场景生成失败了",
      videoTaskId: "task_example_789",
      videoTaskStatus: "failed",
      generatedVideos: [],
      lastStatusCheck: Date.now() - 120000 // 2分钟前
    }
  ])

  const [customTaskId, setCustomTaskId] = useState('')
  const [refreshing, setRefreshing] = useState<number | null>(null)

  const mockRefreshVideoStatus = async (sceneId: number, taskId?: string) => {
    setRefreshing(sceneId)
    
    try {
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // 模拟不同的响应结果
      const responses = [
        { status: 'succeed', hasVideo: true },
        { status: 'processing', hasVideo: false },
        { status: 'failed', hasVideo: false }
      ]
      
      const randomResponse = responses[Math.floor(Math.random() * responses.length)]
      
      setScenes(prevScenes => 
        prevScenes.map(scene => 
          scene.id === sceneId 
            ? {
                ...scene,
                videoTaskStatus: randomResponse.status as any,
                lastStatusCheck: Date.now(),
                generatedVideos: randomResponse.hasVideo ? [
                  {
                    id: `video_${Date.now()}`,
                    url: `https://example.com/video_${Date.now()}.mp4`,
                    taskId: scene.videoTaskId || 'unknown'
                  }
                ] : scene.generatedVideos
              }
            : scene
        )
      )
      
      alert(`刷新完成！\n状态: ${randomResponse.status}\n${randomResponse.hasVideo ? '视频已生成' : '暂无视频'}`)
      
    } catch (error) {
      alert(`刷新失败: ${error}`)
    } finally {
      setRefreshing(null)
    }
  }

  const addCustomScene = () => {
    if (!customTaskId.trim()) {
      alert('请输入任务ID')
      return
    }
    
    const newScene: MockScene = {
      id: Date.now(),
      title: `自定义场景 ${customTaskId}`,
      description: `使用任务ID: ${customTaskId}`,
      videoTaskId: customTaskId.trim(),
      videoTaskStatus: 'submitted',
      generatedVideos: [],
      lastStatusCheck: undefined
    }
    
    setScenes(prev => [...prev, newScene])
    setCustomTaskId('')
  }

  const getStatusBadge = (status?: string) => {
    switch (status) {
      case 'succeed':
        return <Badge className="bg-green-100 text-green-800">✅ 成功</Badge>
      case 'processing':
        return <Badge className="bg-yellow-100 text-yellow-800">⏳ 处理中</Badge>
      case 'failed':
        return <Badge className="bg-red-100 text-red-800">❌ 失败</Badge>
      case 'submitted':
        return <Badge className="bg-blue-100 text-blue-800">📝 已提交</Badge>
      default:
        return <Badge className="bg-gray-100 text-gray-800">未知</Badge>
    }
  }

  return (
    <div className="container mx-auto p-6 max-w-6xl">
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>视频刷新功能测试</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex space-x-2 mb-4">
            <Input
              placeholder="输入任务ID添加测试场景"
              value={customTaskId}
              onChange={(e) => setCustomTaskId(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && addCustomScene()}
            />
            <Button onClick={addCustomScene}>添加场景</Button>
          </div>
          
          <div className="text-sm text-gray-600">
            <p>• 点击"刷新状态"按钮模拟查询Kling任务状态</p>
            <p>• 系统会随机返回不同的状态结果</p>
            <p>• 绿色表示成功，黄色表示处理中，红色表示失败</p>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {scenes.map((scene) => (
          <Card key={scene.id} className="border-2 hover:border-blue-300 transition-colors">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">{scene.title}</CardTitle>
                {getStatusBadge(scene.videoTaskStatus)}
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm text-gray-600">{scene.description}</p>
              
              {scene.videoTaskId && (
                <div className="text-xs bg-gray-100 p-2 rounded">
                  <strong>任务ID:</strong> {scene.videoTaskId}
                </div>
              )}
              
              <div className="flex items-center space-x-2">
                <div className="w-12 h-8 bg-gray-200 rounded border flex items-center justify-center">
                  <Video className="w-3 h-3 text-gray-500" />
                </div>
                <div className="text-xs text-gray-500">→</div>
                <div className={`w-12 h-8 rounded border flex items-center justify-center ${
                  scene.generatedVideos.length > 0 
                    ? "bg-gradient-to-r from-green-100 to-emerald-100 border-green-200"
                    : scene.videoTaskStatus === 'processing'
                    ? "bg-gradient-to-r from-yellow-100 to-orange-100 border-orange-200"
                    : scene.videoTaskStatus === 'failed'
                    ? "bg-gradient-to-r from-red-100 to-pink-100 border-red-200"
                    : "bg-gradient-to-r from-blue-100 to-purple-100 border-blue-200"
                }`}>
                  {scene.generatedVideos.length > 0 ? (
                    <Video className="w-3 h-3 text-green-600" />
                  ) : scene.videoTaskStatus === 'processing' ? (
                    <RefreshCw className="w-3 h-3 text-orange-600 animate-spin" />
                  ) : scene.videoTaskStatus === 'failed' ? (
                    <Video className="w-3 h-3 text-red-600" />
                  ) : (
                    <Video className="w-3 h-3 text-blue-600" />
                  )}
                </div>
              </div>
              
              {scene.generatedVideos.length > 0 && (
                <div className="space-y-2">
                  <p className="text-sm font-medium text-green-600">
                    ✅ 已生成 {scene.generatedVideos.length} 个视频
                  </p>
                  {scene.generatedVideos.map((video, index) => (
                    <div key={video.id} className="text-xs bg-green-50 p-2 rounded border border-green-200">
                      <div className="flex items-center space-x-2">
                        <Play className="w-3 h-3 text-green-600" />
                        <span>视频 {index + 1}</span>
                      </div>
                      <div className="text-gray-600 mt-1 break-all">{video.url}</div>
                    </div>
                  ))}
                </div>
              )}
              
              {scene.lastStatusCheck && (
                <div className="text-xs text-gray-500">
                  上次检查: {new Date(scene.lastStatusCheck).toLocaleTimeString()}
                </div>
              )}
              
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => mockRefreshVideoStatus(scene.id, scene.videoTaskId)}
                  disabled={refreshing === scene.id}
                  className="flex-1"
                >
                  <RefreshCw className={`w-3 h-3 mr-1 ${refreshing === scene.id ? 'animate-spin' : ''}`} />
                  {refreshing === scene.id ? '刷新中...' : '刷新状态'}
                </Button>
                
                {scene.generatedVideos.length > 0 && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => alert('这是模拟的视频播放功能')}
                    className="px-3"
                  >
                    <Play className="w-3 h-3" />
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
      
      <Card className="mt-6">
        <CardContent className="pt-6">
          <div className="text-center space-y-2">
            <h3 className="font-medium text-gray-900">功能说明</h3>
            <div className="text-sm text-gray-600 space-y-1">
              <p>🔄 <strong>自动刷新</strong>: 点击场景卡片时自动检查任务状态（30秒防重复）</p>
              <p>🔍 <strong>手动刷新</strong>: 点击"刷新状态"按钮手动查询</p>
              <p>📊 <strong>状态显示</strong>: 实时显示任务状态和生成结果</p>
              <p>⚡ <strong>批量操作</strong>: 支持批量刷新所有待处理视频</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
