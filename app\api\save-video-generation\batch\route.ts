import { NextRequest, NextResponse } from 'next/server'
import { getVideoGenerationTasksByProjectUserAndShots } from '@/lib/prisma-video-operations'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { projectId, userId, shotIds } = body

    console.log('🔍 [BATCH-VIDEO-QUERY] 批量查询参数:', { projectId, userId, shotIds })

    if (!projectId || !userId || !shotIds) {
      return NextResponse.json({
        success: false,
        error: '缺少必要参数: projectId, userId, shotIds'
      }, { status: 400 })
    }

    if (!Array.isArray(shotIds) || shotIds.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'shotIds必须是非空数组'
      }, { status: 400 })
    }

    console.log(`🔍 [BATCH-VIDEO-QUERY] 查询 ${shotIds.length} 个镜头的任务:`, shotIds)

    // 批量查询所有镜头的最新任务
    const tasks = await getVideoGenerationTasksByProjectUserAndShots(projectId, userId, shotIds)

    console.log(`✅ [BATCH-VIDEO-QUERY] 查询成功，找到 ${tasks?.length || 0} 个任务`)

    // 按镜头ID分组，每个镜头只保留最新的任务
    const tasksByShot: Record<string, any[]> = {}
    
    if (tasks) {
      for (const task of tasks) {
        const shotId = task.shot_id
        if (!tasksByShot[shotId]) {
          tasksByShot[shotId] = []
        }
        tasksByShot[shotId].push(task)
      }

      // 每个镜头只保留最新的任务（已经按created_at降序排列）
      for (const shotId in tasksByShot) {
        tasksByShot[shotId] = [tasksByShot[shotId][0]]
      }
    }

    console.log(`📊 [BATCH-VIDEO-QUERY] 分组结果:`, Object.keys(tasksByShot).map(shotId => ({
      shotId,
      taskCount: tasksByShot[shotId].length,
      latestStatus: tasksByShot[shotId][0]?.status
    })))

    return NextResponse.json({
      success: true,
      tasks: tasksByShot,
      totalShots: Object.keys(tasksByShot).length,
      queriedShots: shotIds.length
    })

  } catch (error) {
    console.error('❌ [BATCH-VIDEO-QUERY] 批量查询失败:', error)
    return NextResponse.json({
      success: false,
      error: '服务器内部错误',
      details: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 })
  }
}
