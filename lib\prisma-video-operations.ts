import { PrismaClient } from './generated/prisma'

const prisma = new PrismaClient()

// 视频生成任务相关操作

export interface CreateVideoTaskData {
  projectId?: string
  shotId?: string
  userId?: string
  taskName?: string
  prompt: string
  negativePrompt?: string
  imageUrl: string  // 必需字段
  modelName?: string
  aspectRatio?: string
  duration?: number
  cfgScale?: number
  mode?: string
  seed?: bigint
  klingTaskId?: string
  apiRequestPayload?: any
  apiResponseData?: any
}

export interface UpdateVideoTaskData {
  status?: string
  klingTaskId?: string
  apiResponseData?: any
  errorMessage?: string
  errorCode?: string
  startedAt?: Date
  completedAt?: Date
}

export interface CreateVideoData {
  taskId: string
  videoUrl: string
  videoFilename?: string
  videoSizeBytes?: number
  videoWidth?: number
  videoHeight?: number
  videoFormat?: string
  videoDurationSeconds?: number
  qualityScore?: number
  isPrimary?: boolean
  storageProvider?: string
  storagePath?: string
  cdnUrl?: string
  minioObjectName?: string
  minioBucketName?: string
  cdnBaseUrl?: string
  generationTimeSeconds?: number
  actualPrompt?: string
  metadata?: any
}

// 创建视频生成任务
export async function createVideoGenerationTask(data: CreateVideoTaskData) {
  console.log('💾 [DB] Creating video generation task with data:', JSON.stringify(data, null, 2))

  const taskData: any = {
    prompt: data.prompt,
    negative_prompt: data.negativePrompt || null,
    image_url: data.imageUrl,  // 必需字段
    model_name: data.modelName || 'kling-v1-6',
    aspect_ratio: data.aspectRatio || '16:9',
    duration: data.duration || 5,
    cfg_scale: data.cfgScale !== undefined ? data.cfgScale : 0.5,
    mode: data.mode || 'pro',
    seed: data.seed || null,
    status: 'pending',
    kling_task_id: data.klingTaskId || null,
    api_request_payload: data.apiRequestPayload || null,
    api_response_data: data.apiResponseData || null,
  }

  if (data.projectId) {
    taskData.project_id = data.projectId
  }

  if (data.shotId) {
    taskData.shot_id = data.shotId
  }

  if (data.userId) {
    taskData.user_id = data.userId
  }

  if (data.taskName) {
    taskData.task_name = data.taskName
  }

  console.log('💾 [DB] Final task data to be saved:', JSON.stringify(taskData, null, 2))

  try {
    const result = await prisma.video_generation_tasks.create({
      data: taskData
    })
    console.log('✅ [DB] Video generation task created successfully:', result.id)
    return result
  } catch (error) {
    console.error('❌ [DB] Failed to create video generation task:', error)
    throw error
  }
}

// 更新视频生成任务
export async function updateVideoGenerationTask(taskId: string, data: UpdateVideoTaskData) {
  const updateData: any = {}

  if (data.status !== undefined) {
    updateData.status = data.status
  }

  if (data.klingTaskId !== undefined) {
    updateData.kling_task_id = data.klingTaskId
  }

  if (data.apiResponseData !== undefined) {
    updateData.api_response_data = data.apiResponseData
  }

  if (data.errorMessage !== undefined) {
    updateData.error_message = data.errorMessage
  }

  if (data.errorCode !== undefined) {
    updateData.error_code = data.errorCode
  }

  if (data.startedAt !== undefined) {
    updateData.started_at = data.startedAt
  }

  if (data.completedAt !== undefined) {
    updateData.completed_at = data.completedAt
  }

  updateData.updated_at = new Date()

  return await prisma.video_generation_tasks.update({
    where: { id: taskId },
    data: updateData
  })
}

// 根据 Kling Task ID 查找视频生成任务
export async function getVideoGenerationTaskByKlingId(klingTaskId: string) {
  return await prisma.video_generation_tasks.findUnique({
    where: { kling_task_id: klingTaskId },
    include: {
      generated_videos: true
    }
  })
}

// 根据项目ID获取视频生成任务
export async function getVideoGenerationTasksByProject(projectId: string) {
  return await prisma.video_generation_tasks.findMany({
    where: { project_id: projectId },
    include: {
      generated_videos: true
    },
    orderBy: {
      created_at: 'desc'
    }
  })
}

// 根据镜头ID获取视频生成任务
export async function getVideoGenerationTasksByShot(shotId: string) {
  return await prisma.video_generation_tasks.findMany({
    where: { shot_id: shotId },
    include: {
      generated_videos: true
    },
    orderBy: {
      created_at: 'desc'
    }
  })
}

// 根据项目ID、镜头ID、用户ID组合查询视频生成任务
export async function getVideoGenerationTasksByProjectShotUser(projectId: string, shotId: string, userId: string) {
  return await prisma.video_generation_tasks.findMany({
    where: {
      project_id: projectId,
      shot_id: shotId,
      user_id: userId
    },
    include: {
      generated_videos: true
    },
    orderBy: {
      created_at: 'desc'
    }
  })
}

// 批量查询多个镜头的视频生成任务
export async function getVideoGenerationTasksByProjectUserAndShots(
  projectId: string,
  userId: string,
  shotIds: string[]
) {
  return await prisma.video_generation_tasks.findMany({
    where: {
      project_id: projectId,
      user_id: userId,
      shot_id: {
        in: shotIds
      }
    },
    include: {
      generated_videos: true
    },
    orderBy: {
      created_at: 'desc'
    }
  })
}

// 创建生成的视频记录
export async function createGeneratedVideo(data: CreateVideoData) {
  const videoData: any = {
    task_id: data.taskId,
    video_url: data.videoUrl,
    video_filename: data.videoFilename,
    video_format: data.videoFormat || 'mp4',
    video_duration_seconds: data.videoDurationSeconds,
    quality_score: data.qualityScore,
    is_primary: data.isPrimary || false,
    storage_provider: data.storageProvider || 'minio',
    storage_path: data.storagePath,
    cdn_url: data.cdnUrl,
    minio_object_name: data.minioObjectName,
    minio_bucket_name: data.minioBucketName || 'videos',
    cdn_base_url: data.cdnBaseUrl,
    generation_time_seconds: data.generationTimeSeconds,
    actual_prompt: data.actualPrompt,
    metadata: data.metadata,
  }

  // Handle bigint conversion for videoSizeBytes
  if (data.videoSizeBytes) {
    videoData.video_size_bytes = BigInt(data.videoSizeBytes)
  }

  if (data.videoWidth) {
    videoData.video_width = data.videoWidth
  }

  if (data.videoHeight) {
    videoData.video_height = data.videoHeight
  }

  return await prisma.generated_videos.create({
    data: videoData
  })
}

// 根据任务ID获取生成的视频
export async function getGeneratedVideosByTask(taskId: string) {
  return await prisma.generated_videos.findMany({
    where: { task_id: taskId },
    orderBy: {
      created_at: 'asc'
    }
  })
}

// 获取所有待处理的视频任务（用于批量刷新）
export async function getPendingVideoTasks() {
  return await prisma.video_generation_tasks.findMany({
    where: {
      status: {
        in: ['pending', 'processing']
      },
      kling_task_id: {
        not: null
      }
    },
    include: {
      generated_videos: true
    },
    orderBy: {
      created_at: 'desc'
    }
  })
}

// 根据用户ID获取视频生成任务
export async function getVideoGenerationTasksByUser(userId: string) {
  return await prisma.video_generation_tasks.findMany({
    where: { user_id: userId },
    include: {
      generated_videos: true,
      script_projects: true,
      script_shots: true
    },
    orderBy: {
      created_at: 'desc'
    }
  })
}
