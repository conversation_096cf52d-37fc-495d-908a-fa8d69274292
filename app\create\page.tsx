"use client"

import { useState, useEffect } from "react"
import { useAuth } from "@/contexts/AuthContext"
import { useSearchParams } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { <PERSON>lider } from "@/components/ui/slider"
import { Checkbox } from "@/components/ui/checkbox"
import { Separator } from "@/components/ui/separator"
import {
  FileText,
  ImageIcon,
  Video,
  Music,
  Scissors,
  Sparkles,
  Type,
  Check,
  ChevronLeft,
  ChevronRight,
  Play,
  Pause,
  Save,
  Eye,
  Download,
  Volume2,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Lock,
  LucideIcon,
  Loader2,
  <PERSON>,
} from "lucide-react"
import Link from "next/link"
import { ScriptStep } from "./components/ScriptStep"
import { ImageGenerationStep } from "./components/ImageGenerationStep"
import { VideoGenerationStep } from "./components/VideoGenerationStep"
import { VideoEditingStep } from "./components/VideoEditingStep"

interface Step {
  id: number
  title: string
  icon: LucideIcon
  description: string
  isOptional?: boolean
}

interface ScriptData {
  title: string
  style: string
  totalDuration: number
  shotCount: number
  shots: Array<{
    shotNumber: number
    duration: number
    shotType: string
    location: string
    characters: string[]
    action: string
    dialogue?: string
    cameraMovement: string
    lighting: string
    props?: string[]
    mood: string
    soundEffect: string
    transition: string
  }>
}

const steps: Step[] = [
  { id: 1, title: "故事脚本", icon: FileText, description: "创建剧本内容" },
  { id: 2, title: "文生图", icon: ImageIcon, description: "生成场景图片"},
  { id: 3, title: "图生视频", icon: Video, description: "转换为视频（需完成所有镜头图片生成）" },
  { id: 4, title: "剪辑", icon: Scissors, description: "视频剪辑" },
]

export default function CreatePage() {
  const searchParams = useSearchParams()
  const { user } = useAuth()

  const [currentStep, setCurrentStep] = useState(1)
  const [completedSteps, setCompletedSteps] = useState<number[]>([])
  const [scriptContent, setScriptContent] = useState("")
  const [selectedStyle, setSelectedStyle] = useState("realistic")
  const [isPlaying, setIsPlaying] = useState(false)
  const [scriptData, setScriptData] = useState<ScriptData | undefined>(undefined)
  const [projectId, setProjectId] = useState<string | undefined>(undefined)
  const [loading, setLoading] = useState(false)
  const [editMode, setEditMode] = useState(false)
  const [shotsImageStatus, setShotsImageStatus] = useState<{[key: number]: boolean}>({})
  const [shotsWithImages, setShotsWithImages] = useState<any[]>([])

  const progress = (currentStep / steps.length) * 100

  // 从URL参数加载项目数据
  useEffect(() => {
    const projectParam = searchParams.get('project')
    if (projectParam && user?.id) {
      loadProject(projectParam)
    }
  }, [searchParams, user?.id])

  // 加载项目数据
  const loadProject = async (projectId: string) => {
    setLoading(true)
    try {
      const response = await fetch(`/api/projects/${projectId}`)
      const data = await response.json()

      if (data.success) {
        const project = data.project

        // 设置项目基本信息
        setProjectId(project.id)
        setEditMode(true)
        setScriptContent(project.scriptContent || "")
        setSelectedStyle(project.style || "realistic")

        // 如果有脚本数据，设置脚本数据
        if (project.scriptData) {
          setScriptData(project.scriptData)
        }

        // 根据项目进度设置当前步骤和已完成步骤
        const completed = []
        let currentStepToSet = 1

        if (project.scriptContent) {
          completed.push(1)
          currentStepToSet = 2
        }

        if (project.imageCount > 0) {
          completed.push(2)
          currentStepToSet = 3
        }

        setCompletedSteps(completed)
        setCurrentStep(currentStepToSet)

        // 加载镜头图片状态
        if (project.id) {
          loadShotsImageStatus(project.id)
        }

      } else {
        console.error('Failed to load project:', data.error)
        alert('加载项目失败: ' + data.error)
      }
    } catch (error) {
      console.error('Error loading project:', error)
      alert('加载项目时发生错误')
    } finally {
      setLoading(false)
    }
  }

  // 加载镜头图片状态和数据
  const loadShotsImageStatus = async (projectId: string) => {
    try {
      const response = await fetch(`/api/projects/${projectId}/shots-images`)
      const data = await response.json()

      if (data.success && data.data.shots) {
        const imageStatus: {[key: number]: boolean} = {}
        data.data.shots.forEach((shot: any) => {
          imageStatus[shot.shotNumber] = shot.hasImages && shot.imageCount > 0
        })
        setShotsImageStatus(imageStatus)
        setShotsWithImages(data.data.shots)
      }
    } catch (error) {
      console.error('Error loading shots image status:', error)
    }
  }

  // 检查所有镜头是否都已生成图片
  const checkAllShotsHaveImages = () => {
    if (!scriptData || !scriptData.shots || scriptData.shots.length === 0) {
      return false
    }

    // 检查每个镜头是否都有图片
    return scriptData.shots.every(shot =>
      shotsImageStatus[shot.shotNumber] === true
    )
  }

  const handleStepClick = (stepId: number) => {
    console.log('handleStepClick called with stepId:', stepId)
    console.log('completedSteps:', completedSteps)
    console.log('currentStep:', currentStep)

    // 允许点击第一步、剪辑步骤(步骤4)或者前一步已完成的步骤
    if (stepId === 1 || stepId === 4 || completedSteps.includes(stepId - 1)) {
      console.log('Setting currentStep to:', stepId)
      setCurrentStep(stepId)
      // 滚动到页面顶部
      window.scrollTo({ top: 0, behavior: 'smooth' })
    } else {
      console.log('Step click blocked for stepId:', stepId)
    }
  }

  // 检查当前步骤是否可以完成
  const canCompleteCurrentStep = () => {
    switch (currentStep) {
      case 1: // 脚本步骤
        return scriptContent.trim().length > 0 || (scriptData && scriptData.shots && scriptData.shots.length > 0)
      case 2: // 文生图步骤
        // 检查是否所有镜头都已生成图片
        return checkAllShotsHaveImages()
      case 3: // 图生视频步骤
        // 只有在所有镜头都有图片的情况下才能进行图生视频
        return checkAllShotsHaveImages()
      case 4: // 配音配乐步骤
        return true // 暂时允许跳过
      case 5: // 剪辑步骤
        return true // 暂时允许跳过
      case 6: // 视频增强步骤
        return true // 暂时允许跳过
      case 7: // 字幕步骤
        return true // 最后一步
      default:
        return false
    }
  }

  const handleNextStep = () => {
    if (currentStep < steps.length && canCompleteCurrentStep()) {
      setCompletedSteps([...completedSteps, currentStep])
      setCurrentStep(currentStep + 1)
      // 滚动到页面顶部
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }
  }

  const handlePrevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
      // 滚动到页面顶部
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }
  }

  // 处理从剧本跳转到文生图
  const handleGenerateImages = (scriptData: ScriptData, newProjectId?: string) => {
    setScriptData(scriptData)
    if (newProjectId) {
      setProjectId(newProjectId)
      // 加载镜头图片状态
      loadShotsImageStatus(newProjectId)
    }
    setCurrentStep(2) // 跳转到文生图步骤
    // 滚动到页面顶部
    window.scrollTo({ top: 0, behavior: 'smooth' })
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return <ScriptStep
          scriptContent={scriptContent}
          setScriptContent={setScriptContent}
          onGenerateImages={handleGenerateImages}
          projectId={projectId}
          editMode={editMode}
          scriptData={scriptData}
          selectedStyle={selectedStyle}
        />
      case 2:
        return <ImageGenerationStep
          selectedStyle={selectedStyle}
          setSelectedStyle={setSelectedStyle}
          scriptData={scriptData}
          projectId={projectId}
          userId={user?.id}
          editMode={editMode}
          onImageGenerated={() => {
            // 当图片生成完成时，重新加载镜头图片状态
            if (projectId) {
              loadShotsImageStatus(projectId)
            }
          }}
        />
      case 3:
        return <VideoGenerationStep
          isPlaying={isPlaying}
          setIsPlaying={setIsPlaying}
          scriptData={scriptData}
          projectId={projectId}
          userId={user?.id}
          shotsWithImages={shotsWithImages}
        />
      case 4:
        return <VideoEditingStep
          projectId={projectId}
          userId={user?.id}
          scriptData={scriptData}
          shotsWithImages={shotsWithImages}
        />
      default:
        return null
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/50">
      {/* 顶部进度条 + 导航 - 现代化设计 */}
      <header className="bg-white/80 backdrop-blur-xl border-b border-slate-200/60 sticky top-0 z-50 shadow-sm">
        <div className="container mx-auto px-6 py-5">
          <div className="flex items-center justify-between mb-5">
            <div className="flex items-center space-x-6">
              <Link href="/" className="group flex items-center text-slate-600 hover:text-slate-900 transition-all duration-300 hover:scale-105">
                <ArrowLeft className="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform duration-300" />
                <span className="font-medium">返回首页</span>
              </Link>
              <div className="w-px h-6 bg-slate-300"></div>
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                    <Sparkles className="w-4 h-4 text-white" />
                  </div>
                  <h1 className="text-2xl font-bold bg-gradient-to-r from-slate-800 via-blue-700 to-purple-700 bg-clip-text text-transparent">
                    AI短剧创作工坊
                  </h1>
                </div>
                {editMode && (
                  <Badge className="bg-gradient-to-r from-amber-100 to-orange-100 text-amber-800 border-amber-200 font-medium px-3 py-1">
                    <Edit className="w-3 h-3 mr-1" />
                    编辑模式
                  </Badge>
                )}
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Badge variant="outline" className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200 text-blue-700 font-semibold px-4 py-2">
                第 {currentStep} 步 / 共 {steps.length} 步
              </Badge>
              {loading && (
                <Badge className="bg-blue-100 text-blue-700 border-blue-200 animate-pulse">
                  <Loader2 className="w-3 h-3 mr-1 animate-spin" />
                  处理中
                </Badge>
              )}
            </div>
          </div>
          <div className="space-y-3">
            <div className="flex justify-between items-center text-sm">
              <span className="font-semibold text-slate-700">创作进度</span>
              <div className="flex items-center space-x-2">
                <span className="text-slate-600">已完成</span>
                <span className="font-bold text-blue-600 bg-blue-50 px-2 py-1 rounded-full text-xs">
                  {Math.round(progress)}%
                </span>
              </div>
            </div>
            <div className="relative h-2 bg-slate-200 rounded-full overflow-hidden">
              <div
                className="absolute inset-y-0 left-0 bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500 rounded-full transition-all duration-700 ease-out shadow-sm"
                style={{ width: `${progress}%` }}
              />
              <div
                className="absolute inset-y-0 left-0 bg-gradient-to-r from-blue-400/50 to-purple-400/50 rounded-full animate-pulse"
                style={{ width: `${progress}%` }}
              />
            </div>
          </div>
        </div>
      </header>

      <div className="flex h-[calc(100vh-140px)]">
        {/* 侧边栏 - 步骤导航 */}
        <aside className="w-80 bg-white/70 backdrop-blur-sm border-r border-slate-200/60 overflow-y-auto">
          <div className="p-6">
            <div className="flex items-center space-x-2 mb-8">
              <div className="w-6 h-6 rounded-md bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                <FileText className="w-3 h-3 text-white" />
              </div>
              <h2 className="text-lg font-bold text-slate-800">创作流程</h2>
            </div>
            <nav className="space-y-3">
              {steps.map((step) => {
                const Icon = step.icon
                const isActive = currentStep === step.id
                const isCompleted = completedSteps.includes(step.id)
                // 允许点击第一步、剪辑步骤(步骤4)或者前一步已完成的步骤
                const isClickable = step.id === 1 || step.id === 4 || completedSteps.includes(step.id - 1)

                // 调试信息
                if (step.id === 4) {
                  console.log(`Step 4 (剪辑) - isClickable: ${isClickable}, isActive: ${isActive}, isCompleted: ${isCompleted}`)
                }

                return (
                  <button
                    key={step.id}
                    onClick={() => handleStepClick(step.id)}
                    disabled={!isClickable}
                    className={`group w-full flex items-center p-4 rounded-xl text-left transition-all duration-300 hover:scale-[1.02] ${
                      isActive
                        ? "bg-gradient-to-r from-blue-50 to-indigo-50 border-2 border-blue-200 text-blue-800 shadow-lg shadow-blue-100/50"
                        : isCompleted
                          ? "bg-gradient-to-r from-emerald-50 to-green-50 border-2 border-emerald-200 text-emerald-800 hover:shadow-lg hover:shadow-emerald-100/50"
                          : isClickable
                            ? "hover:bg-gradient-to-r hover:from-slate-50 hover:to-gray-50 border-2 border-transparent text-slate-700 hover:border-slate-200 hover:shadow-md"
                            : "text-slate-400 cursor-not-allowed border-2 border-transparent bg-slate-50/50"
                    }`}
                    title={!isClickable && step.id > 1 ? `请先完成步骤${step.id - 1}` : ""}
                  >
                    <div
                      className={`w-12 h-12 rounded-xl flex items-center justify-center mr-4 transition-all duration-300 ${
                        isActive
                          ? "bg-gradient-to-br from-blue-500 to-indigo-600 shadow-lg shadow-blue-200/50"
                          : isCompleted
                            ? "bg-gradient-to-br from-emerald-500 to-green-600 shadow-lg shadow-emerald-200/50"
                            : isClickable
                              ? "bg-gradient-to-br from-slate-100 to-gray-100 group-hover:from-slate-200 group-hover:to-gray-200"
                              : "bg-slate-200"
                      }`}
                    >
                      {isCompleted ? (
                        <Check className="w-6 h-6 text-white" />
                      ) : !isClickable ? (
                        <Lock className="w-5 h-5 text-slate-400" />
                      ) : (
                        <Icon
                          className={`w-5 h-5 transition-colors duration-300 ${
                            isActive ? "text-white" : isClickable ? "text-slate-600 group-hover:text-slate-700" : "text-slate-400"
                          }`}
                        />
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-semibold truncate">{step.title}</span>
                        {step.isOptional && (
                          <Badge className="text-xs bg-amber-100 text-amber-700 border-amber-200 px-2 py-0.5">
                            可选
                          </Badge>
                        )}
                        {!isClickable && step.id > 1 && (
                          <Badge variant="outline" className="text-xs bg-slate-100 text-slate-500 border-slate-300 px-2 py-0.5">
                            锁定
                          </Badge>
                        )}
                      </div>
                      <div className="text-sm opacity-80 leading-relaxed">
                        {step.description}
                        {!isClickable && step.id > 1 && (
                          <div className="text-xs text-slate-400 mt-1 font-medium">
                            需要完成步骤 {step.id - 1}
                          </div>
                        )}
                        {/* 显示文生图步骤的进度 */}
                        {step.id === 2 && scriptData && scriptData.shots && (
                          <div className="text-xs text-blue-600 mt-2 font-medium bg-blue-50 px-2 py-1 rounded-md">
                            {Object.values(shotsImageStatus).filter(Boolean).length} / {scriptData.shots.length} 镜头已生成
                          </div>
                        )}
                        {/* 显示图生视频步骤的前置条件 */}
                        {step.id === 3 && !checkAllShotsHaveImages() && scriptData && scriptData.shots && (
                          <div className="text-xs text-amber-600 mt-2 font-medium bg-amber-50 px-2 py-1 rounded-md">
                            需要完成所有镜头的图片生成
                          </div>
                        )}
                      </div>
                    </div>
                  </button>
                )
              })}
            </nav>
          </div>
        </aside>

        {/* 主工作区域 */}
        <main className="flex-1 overflow-y-auto">{renderStepContent()}</main>
      </div>

      {/* 底部操作栏 */}
      <footer className="bg-white/80 backdrop-blur-xl border-t border-slate-200/60 px-6 py-5 sticky bottom-0 z-50 shadow-lg">
        <div className="flex items-center justify-between">
          <Button
            variant="outline"
            onClick={handlePrevStep}
            disabled={currentStep === 1}
            className="group flex items-center px-6 py-3 bg-white/70 border-slate-300 hover:border-slate-400 hover:bg-white hover:shadow-md transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <ChevronLeft className="w-4 h-4 mr-2 group-hover:-translate-x-1 transition-transform duration-300" />
            <span className="font-medium">上一步</span>
          </Button>

          <div className="flex items-center space-x-3">
            <Button
              variant="outline"
              className="group flex items-center px-5 py-3 bg-white/70 border-slate-300 hover:border-emerald-400 hover:bg-emerald-50 hover:text-emerald-700 hover:shadow-md transition-all duration-300"
            >
              <Save className="w-4 h-4 mr-2 group-hover:scale-110 transition-transform duration-300" />
              <span className="font-medium">保存草稿</span>
            </Button>
            <Button
              variant="outline"
              className="group flex items-center px-5 py-3 bg-white/70 border-slate-300 hover:border-purple-400 hover:bg-purple-50 hover:text-purple-700 hover:shadow-md transition-all duration-300"
            >
              <Eye className="w-4 h-4 mr-2 group-hover:scale-110 transition-transform duration-300" />
              <span className="font-medium">预览</span>
            </Button>
          </div>

          <Button
            onClick={handleNextStep}
            disabled={currentStep === steps.length || !canCompleteCurrentStep()}
            className="group flex items-center px-8 py-3 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold shadow-lg hover:shadow-xl disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:shadow-lg transition-all duration-300 hover:scale-105"
            title={
              !canCompleteCurrentStep()
                ? currentStep === 2
                  ? "请为所有镜头生成图片后再继续"
                  : currentStep === 3
                    ? "请确保所有镜头都有图片后再继续"
                    : "请完成当前步骤后再继续"
                : ""
            }
          >
            <span className="font-bold">
              {currentStep === steps.length ? "完成创作" : "下一步"}
            </span>
            {currentStep < steps.length && (
              <ChevronRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-300" />
            )}
            {currentStep === steps.length && (
              <Sparkles className="w-4 h-4 ml-2 group-hover:rotate-12 transition-transform duration-300" />
            )}
          </Button>
        </div>
      </footer>
    </div>
  )
}







