#!/usr/bin/env node

/**
 * 最终测试所有调试alert功能
 * 
 * 验证所有修改后的alert是否能提供详细的调试信息
 */

async function testFinalDebugAlerts() {
  console.log('🎯 最终测试调试Alert功能');
  console.log('='.repeat(60));
  
  // 测试1: 正常的API调用（有完整数据）
  console.log('📋 测试1: 正常API调用 - 有完整数据');
  await testNormalApiCall();
  
  // 测试2: 查询不存在的镜头ID
  console.log('\n📋 测试2: 查询不存在的镜头ID');
  await testNonExistentShotId();
  
  // 测试3: 查询有任务但无Kling任务ID的情况
  console.log('\n📋 测试3: 查询有任务但无Kling任务ID');
  await testTaskWithoutKlingId();
  
  // 测试4: 服务器错误情况
  console.log('\n📋 测试4: 服务器错误情况');
  await testServerError();
  
  console.log('\n✅ 所有调试功能测试完成！');
  console.log('\n💡 前端调试信息总结:');
  console.log('   • 所有alert都包含详细的调试信息');
  console.log('   • 控制台输出包含完整的上下文数据');
  console.log('   • 提供具体的解决方案建议');
  console.log('   • 便于快速定位和解决问题');
}

async function testNormalApiCall() {
  try {
    const shotId = '7268a0c0-d00f-470c-a7e6-beca3669474c';
    console.log(`   查询镜头ID: ${shotId}`);
    
    const response = await fetch(`http://localhost:3001/api/save-video-generation?shotId=${shotId}`);
    
    if (response.ok) {
      const result = await response.json();
      console.log(`   ✅ 查询成功: 找到 ${result.tasks?.length || 0} 个任务`);
      
      if (result.tasks && result.tasks.length > 0) {
        const task = result.tasks[0];
        console.log(`   任务状态: ${task.status}`);
        console.log(`   Kling任务ID: ${task.kling_task_id || '无'}`);
        console.log(`   视频数量: ${task.generated_videos?.length || 0}`);
        
        console.log('   💡 前端会显示的调试信息:');
        console.log('     • 场景和任务的完整信息');
        console.log('     • 关联ID的状态检查');
        console.log('     • 任务生命周期时间');
        console.log('     • 具体的下一步操作建议');
      }
    } else {
      console.log(`   ❌ 查询失败: ${response.status}`);
    }
  } catch (error) {
    console.log(`   ❌ 请求异常: ${error.message}`);
  }
}

async function testNonExistentShotId() {
  try {
    const fakeShotId = 'non-existent-shot-id-12345';
    console.log(`   查询不存在的镜头ID: ${fakeShotId}`);
    
    const response = await fetch(`http://localhost:3001/api/save-video-generation?shotId=${fakeShotId}`);
    
    if (response.ok) {
      const result = await response.json();
      console.log(`   查询结果: 找到 ${result.tasks?.length || 0} 个任务`);
      
      if (!result.tasks || result.tasks.length === 0) {
        console.log('   💡 前端会显示"未找到任务"的详细调试信息:');
        console.log('     • 查询URL和所有参数');
        console.log('     • 场景和镜头的详细信息');
        console.log('     • 查询参数状态检查');
        console.log('     • 可能原因的详细分析');
        console.log('     • 具体的解决步骤指导');
      }
    } else {
      console.log(`   ❌ 查询失败: ${response.status}`);
    }
  } catch (error) {
    console.log(`   ❌ 请求异常: ${error.message}`);
    console.log('   💡 前端会显示"数据库查询失败"的详细调试信息:');
    console.log('     • 错误类型和详细信息');
    console.log('     • 网络连接状态检查');
    console.log('     • 服务器运行状态提示');
    console.log('     • 完整的错误堆栈信息');
  }
}

async function testTaskWithoutKlingId() {
  try {
    // 查找一个可能没有Kling任务ID的项目
    const projectId = '99833a8b-1d51-40e2-9461-89d3c311847f';
    console.log(`   查询项目ID: ${projectId}`);
    
    const response = await fetch(`http://localhost:3001/api/save-video-generation?projectId=${projectId}`);
    
    if (response.ok) {
      const result = await response.json();
      
      if (result.tasks && result.tasks.length > 0) {
        const taskWithoutKlingId = result.tasks.find(task => !task.kling_task_id);
        
        if (taskWithoutKlingId) {
          console.log(`   找到无Kling任务ID的任务: ${taskWithoutKlingId.id}`);
          console.log(`   任务状态: ${taskWithoutKlingId.status}`);
          console.log('   💡 前端会显示"缺少Kling任务ID"的详细调试信息:');
          console.log('     • 任务的完整生命周期信息');
          console.log('     • 创建、开始、完成时间');
          console.log('     • API请求和响应数据状态');
          console.log('     • 失败原因的详细分析');
          console.log('     • 重新生成视频的建议');
        } else {
          console.log('   所有任务都有Kling任务ID');
        }
      }
    }
  } catch (error) {
    console.log(`   ❌ 测试异常: ${error.message}`);
  }
}

async function testServerError() {
  console.log('   模拟服务器错误情况...');
  console.log('   💡 当服务器不可用时，前端会显示:');
  console.log('     • 详细的错误类型和信息');
  console.log('     • 网络连接检查建议');
  console.log('     • 服务器状态确认步骤');
  console.log('     • 重试操作的指导');
  console.log('     • 完整的错误上下文信息');
}

// 显示调试功能的完整特性
function showDebugFeaturesComplete() {
  console.log('\n🎯 调试功能完整特性');
  console.log('='.repeat(60));
  
  console.log('📋 1. 刷新按钮点击时（场景无任务ID）:');
  console.log('   ✅ 显示场景的完整基本信息');
  console.log('   ✅ 列出所有关联ID的状态（项目ID、镜头ID、用户ID）');
  console.log('   ✅ 显示镜头数据详情（编号、地点、图片状态）');
  console.log('   ✅ 分析缺失信息并提供解决方案');
  console.log('   ✅ 输出详细调试日志到控制台');
  
  console.log('\n📋 2. 数据库查询参数不足时:');
  console.log('   ✅ 显示查询URL和所有参数');
  console.log('   ✅ 分析项目ID和镜头ID的可用性');
  console.log('   ✅ 显示镜头数据的完整详情');
  console.log('   ✅ 提供参数获取的具体建议');
  console.log('   ✅ 输出完整的上下文信息');
  
  console.log('\n📋 3. 数据库查询失败时:');
  console.log('   ✅ 显示查询URL和参数详情');
  console.log('   ✅ 分析错误类型和具体信息');
  console.log('   ✅ 提供网络和服务器检查步骤');
  console.log('   ✅ 输出完整的错误堆栈');
  console.log('   ✅ 建议具体的修复操作');
  
  console.log('\n📋 4. 未找到相关任务时:');
  console.log('   ✅ 显示查询条件的详细信息');
  console.log('   ✅ 分析镜头和项目关联状态');
  console.log('   ✅ 列出查询参数的状态检查');
  console.log('   ✅ 分析可能的原因');
  console.log('   ✅ 提供创建任务的指导');
  
  console.log('\n📋 5. 任务缺少Kling任务ID时:');
  console.log('   ✅ 显示任务的完整生命周期');
  console.log('   ✅ 显示所有时间戳信息');
  console.log('   ✅ 分析API请求响应状态');
  console.log('   ✅ 提供失败原因分析');
  console.log('   ✅ 建议具体的修复步骤');
  
  console.log('\n💡 所有调试信息的共同特点:');
  console.log('   ✅ 同时显示在alert和控制台');
  console.log('   ✅ 包含完整的上下文数据');
  console.log('   ✅ 提供可操作的解决方案');
  console.log('   ✅ 便于开发者快速定位问题');
  console.log('   ✅ 格式化良好，易于阅读');
}

// 运行所有测试
async function runAllTests() {
  await testFinalDebugAlerts();
  showDebugFeaturesComplete();
}

runAllTests().catch(console.error);
