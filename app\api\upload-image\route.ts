import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const file = formData.get('image') as File

    if (!file) {
      return NextResponse.json({
        success: false,
        error: '没有上传文件'
      }, { status: 400 })
    }

    // 检查文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json({
        success: false,
        error: '不支持的文件类型，请上传 JPG、PNG 或 WebP 格式的图片'
      }, { status: 400 })
    }

    // 检查文件大小 (最大 10MB)
    const maxSize = 10 * 1024 * 1024 // 10MB
    if (file.size > maxSize) {
      return NextResponse.json({
        success: false,
        error: '文件大小不能超过 10MB'
      }, { status: 400 })
    }

    // 检查Supabase配置
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

    if (!supabaseUrl || !supabaseAnonKey) {
      return NextResponse.json({
        success: false,
        error: 'Supabase 配置未完成'
      }, { status: 500 })
    }

    // 初始化Supabase客户端
    const supabase = createClient(supabaseUrl, supabaseAnonKey)

    // 获取文件数据
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    console.log('🖼️ [UPLOAD] 开始上传图片到 Supabase Storage...')
    console.log('🖼️ [UPLOAD] 文件名:', file.name)
    console.log('🖼️ [UPLOAD] 文件大小:', file.size, 'bytes')
    console.log('🖼️ [UPLOAD] 文件类型:', file.type)

    // 生成唯一文件名
    const fileExtension = file.name.split('.').pop() || 'jpg'
    const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExtension}`
    const filePath = `uploads/${fileName}`

    console.log('🖼️ [UPLOAD] 生成的文件路径:', filePath)

    // 上传到 Supabase Storage
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('generated_images')
      .upload(filePath, buffer, {
        contentType: file.type,
        cacheControl: '3600',
        upsert: false
      })

    if (uploadError) {
      console.error('❌ [UPLOAD] Supabase Storage 上传失败:', uploadError)
      return NextResponse.json({
        success: false,
        error: 'Supabase Storage 上传失败: ' + uploadError.message
      }, { status: 500 })
    }

    console.log('✅ [UPLOAD] Supabase Storage 上传成功')
    console.log('🖼️ [UPLOAD] 上传数据:', uploadData)

    // 获取公共URL
    const { data: urlData } = supabase.storage
      .from('generated_images')
      .getPublicUrl(filePath)

    const publicUrl = urlData.publicUrl
    console.log('🖼️ [UPLOAD] 公共URL:', publicUrl)

    // 验证上传的图片URL是否可访问
    console.log('🔍 [UPLOAD] 验证上传的图片URL是否可访问...')
    try {
      const verifyResponse = await fetch(publicUrl, { method: 'HEAD' })
      if (!verifyResponse.ok) {
        console.error('❌ [UPLOAD] 图片URL验证失败:', verifyResponse.status, verifyResponse.statusText)
        return NextResponse.json({
          success: false,
          error: '图片上传成功但URL验证失败，请重试'
        }, { status: 500 })
      }
      console.log('✅ [UPLOAD] 图片URL验证成功，可正常访问')
    } catch (verifyError) {
      console.error('❌ [UPLOAD] 图片URL验证出错:', verifyError)
      return NextResponse.json({
        success: false,
        error: '图片上传成功但URL验证失败，请重试'
      }, { status: 500 })
    }

    // 返回上传结果
    return NextResponse.json({
      success: true,
      message: '图片已成功上传到Supabase Storage并验证可访问',
      data: {
        id: uploadData.id || fileName,
        url: publicUrl,
        displayUrl: publicUrl,
        deleteUrl: null, // Supabase doesn't provide delete URLs like ImgBB
        filename: fileName,
        size: file.size,
        width: null, // We don't extract image dimensions here
        height: null,
        type: file.type,
        uploadTime: Date.now(),
        originalFilename: file.name,
        verified: true,
        path: filePath,
        bucket: 'generated_images'
      }
    })

  } catch (error) {
    console.error('❌ [UPLOAD] 上传过程中发生错误:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '上传失败'
    }, { status: 500 })
  }
}
