"use client"

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

export default function TestTaskStatusPage() {
  const [taskId, setTaskId] = useState('')
  const [result, setResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const checkTaskStatus = async () => {
    if (!taskId.trim()) {
      setError('请输入任务ID')
      return
    }

    setLoading(true)
    setError('')
    setResult(null)

    try {
      console.log('🔍 [TEST] 开始查询任务状态:', taskId)
      
      const response = await fetch('/api/check-task-status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ taskId: taskId.trim() }),
      })

      const data = await response.json()
      console.log('📊 [TEST] 查询结果:', data)

      if (response.ok && data.success) {
        setResult(data)
      } else {
        setError(data.error || '查询失败')
      }
    } catch (err) {
      console.error('❌ [TEST] 查询失败:', err)
      setError(err instanceof Error ? err.message : '查询失败')
    } finally {
      setLoading(false)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'succeed':
        return <Badge className="bg-green-100 text-green-800">✅ 成功</Badge>
      case 'processing':
        return <Badge className="bg-yellow-100 text-yellow-800">⏳ 处理中</Badge>
      case 'failed':
        return <Badge className="bg-red-100 text-red-800">❌ 失败</Badge>
      case 'submitted':
        return <Badge className="bg-blue-100 text-blue-800">📝 已提交</Badge>
      default:
        return <Badge className="bg-gray-100 text-gray-800">{status}</Badge>
    }
  }

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <Card>
        <CardHeader>
          <CardTitle>Kling AI 任务状态查询测试</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex space-x-2">
            <Input
              placeholder="输入任务ID (例如: task_12345)"
              value={taskId}
              onChange={(e) => setTaskId(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && checkTaskStatus()}
            />
            <Button 
              onClick={checkTaskStatus} 
              disabled={loading}
            >
              {loading ? '查询中...' : '查询状态'}
            </Button>
          </div>

          {error && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-800">❌ {error}</p>
            </div>
          )}

          {result && (
            <div className="space-y-4">
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <p className="text-green-800">✅ 查询成功</p>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">任务信息</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-600">任务ID</label>
                      <p className="text-sm font-mono bg-gray-100 p-2 rounded">
                        {result.data.task_id}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">状态</label>
                      <div className="mt-1">
                        {getStatusBadge(result.data.task_status)}
                      </div>
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-gray-600">状态消息</label>
                    <p className="text-sm bg-gray-100 p-2 rounded">
                      {result.data.task_status_msg || '无'}
                    </p>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium text-gray-600">创建时间</label>
                      <p className="text-sm bg-gray-100 p-2 rounded">
                        {new Date(result.data.created_at * 1000).toLocaleString()}
                      </p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">更新时间</label>
                      <p className="text-sm bg-gray-100 p-2 rounded">
                        {new Date(result.data.updated_at * 1000).toLocaleString()}
                      </p>
                    </div>
                  </div>

                  {result.data.task_result && (
                    <div>
                      <label className="text-sm font-medium text-gray-600">生成结果</label>
                      {result.data.task_result.videos && result.data.task_result.videos.length > 0 ? (
                        <div className="space-y-2 mt-2">
                          <p className="text-sm text-green-600">
                            ✅ 生成了 {result.data.task_result.videos.length} 个视频
                          </p>
                          {result.data.task_result.videos.map((video: any, index: number) => (
                            <div key={index} className="bg-gray-100 p-3 rounded">
                              <p className="text-sm font-medium">视频 {index + 1}</p>
                              <a 
                                href={video.url} 
                                target="_blank" 
                                rel="noopener noreferrer"
                                className="text-blue-600 hover:text-blue-800 text-sm break-all"
                              >
                                {video.url}
                              </a>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p className="text-sm text-gray-500 bg-gray-100 p-2 rounded mt-2">
                          暂无生成结果
                        </p>
                      )}
                    </div>
                  )}

                  <div>
                    <label className="text-sm font-medium text-gray-600">完整响应数据</label>
                    <pre className="text-xs bg-gray-100 p-3 rounded mt-2 overflow-auto max-h-64">
                      {JSON.stringify(result.data, null, 2)}
                    </pre>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 className="font-medium text-blue-900 mb-2">使用说明</h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• 输入Kling AI的任务ID（通常以task_开头）</li>
              <li>• 点击"查询状态"按钮或按回车键</li>
              <li>• 查看任务的当前状态和生成结果</li>
              <li>• 如果任务已完成，会显示生成的视频链接</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
