import { Client } from 'minio'

const minioClient = new Client({
  endPoint: process.env.MINIO_ENDPOINT || '172.16.30.2',
  port: parseInt(process.env.MINIO_PORT || '9400'),
  useSSL: false,
  accessKey: process.env.MINIO_ACCESS_KEY || 'minioadmin',
  secretKey: process.env.MINIO_SECRET_KEY || 'minioadmin',
})

const BUCKET_NAME = process.env.MINIO_BUCKET_NAME || 'images'

export async function ensureBucketExists() {
  try {
    const exists = await minioClient.bucketExists(BUCKET_NAME)
    if (!exists) {
      await minioClient.makeBucket(BUCKET_NAME)
      console.log(`Bucket '${BUCKET_NAME}' created successfully`)
    }
  } catch (error) {
    console.error('Error checking/creating bucket:', error)
    throw error
  }
}

export async function uploadImageToMinIO(
  imageBuffer: Buffer,
  fileName: string,
  contentType: string = 'image/jpeg'
): Promise<string> {
  try {
    await ensureBucketExists()
    
    const objectName = `generated-images/${Date.now()}-${fileName}`
    
    await minioClient.putObject(
      BUCKET_NAME,
      objectName,
      imageBuffer,
      imageBuffer.length,
      {
        'Content-Type': contentType,
        'Cache-Control': 'public, max-age=31536000',
      }
    )
    
    // Return the public URL
    return `http://${process.env.MINIO_ENDPOINT || '172.16.30.2'}:${process.env.MINIO_PORT || '9400'}/${BUCKET_NAME}/${objectName}`
  } catch (error) {
    console.error('Error uploading to MinIO:', error)
    throw error
  }
}

export async function deleteImageFromMinIO(imageUrl: string) {
  try {
    const url = new URL(imageUrl)
    const objectName = url.pathname.replace(`/${BUCKET_NAME}/`, '')
    
    await minioClient.removeObject(BUCKET_NAME, objectName)
  } catch (error) {
    console.error('Error deleting from MinIO:', error)
    throw error
  }
}

export { minioClient, BUCKET_NAME }