#!/usr/bin/env node

/**
 * Kling AI 响应调试脚本
 * 专门用于查看完整的API响应体，便于调试
 */

// 加载环境变量
require('dotenv').config({ path: '.env.local' });
require('dotenv').config();

const jwt = require('jsonwebtoken');

// 生成JWT Token
function generateJWTToken() {
  const accessKey = process.env.KLING_ACCESS_KEY;
  const secretKey = process.env.KLING_SECRET_KEY;

  if (!accessKey || !secretKey) {
    throw new Error('请设置 KLING_ACCESS_KEY 和 KLING_SECRET_KEY 环境变量');
  }

  const token = jwt.sign(
    {
      iss: accessKey,
      exp: Math.floor(Date.now() / 1000) + 1800, // 30分钟有效期
      nbf: Math.floor(Date.now() / 1000) - 5     // 5秒前开始生效
    },
    secretKey,
    { algorithm: 'HS256' }
  );

  return token;
}

// 调试任务列表查询
async function debugTaskList() {
  console.log('🔍 [DEBUG] Kling AI 任务列表查询调试');
  console.log('='.repeat(80));

  try {
    const token = generateJWTToken();
    console.log('✅ [DEBUG] JWT Token 生成成功');
    console.log('🔑 [DEBUG] Token (前20字符):', token.substring(0, 20) + '...');

    // 构建请求参数
    const params = new URLSearchParams({
      pageNum: '1',
      pageSize: '5'
    });

    const url = `https://api-beijing.klingai.com/v1/videos/image2video?${params.toString()}`;
    
    console.log('\n📡 [DEBUG] 请求信息:');
    console.log('🔗 [DEBUG] URL:', url);
    console.log('📋 [DEBUG] 参数:', Object.fromEntries(params));
    console.log('🔑 [DEBUG] Authorization: Bearer', token.substring(0, 20) + '...');

    console.log('\n⏳ [DEBUG] 发送请求...');
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    console.log('\n📡 [DEBUG] 响应信息:');
    console.log('📊 [DEBUG] HTTP状态:', response.status, response.statusText);
    console.log('📋 [DEBUG] 响应头:');
    
    // 打印响应头
    for (const [key, value] of response.headers.entries()) {
      console.log(`   ${key}: ${value}`);
    }

    if (!response.ok) {
      console.log('\n❌ [DEBUG] 请求失败，获取错误响应体...');
      const errorText = await response.text();
      console.log('📄 [DEBUG] 错误响应体:');
      console.log(errorText);
      
      // 尝试解析为JSON
      try {
        const errorJson = JSON.parse(errorText);
        console.log('📄 [DEBUG] 错误响应体 (格式化JSON):');
        console.log(JSON.stringify(errorJson, null, 2));
      } catch (parseError) {
        console.log('📄 [DEBUG] 错误响应体不是有效的JSON格式');
      }
      
      return;
    }

    console.log('\n✅ [DEBUG] 请求成功，获取响应体...');
    const data = await response.json();

    console.log('\n📄 [DEBUG] 完整响应体:');
    console.log('='.repeat(80));
    console.log(JSON.stringify(data, null, 2));
    console.log('='.repeat(80));

    // 解析响应数据
    console.log('\n📊 [DEBUG] 响应数据解析:');
    console.log('📊 [DEBUG] 返回码:', data.code);
    console.log('📊 [DEBUG] 消息:', data.message || '无');
    console.log('📊 [DEBUG] 是否成功:', data.code === 0 ? '是' : '否');

    if (data.data) {
      console.log('📊 [DEBUG] 数据字段存在:', '是');
      
      if (data.data.tasks) {
        console.log('📊 [DEBUG] 任务列表存在:', '是');
        console.log('📊 [DEBUG] 任务数量:', data.data.tasks.length);
        console.log('📊 [DEBUG] 总数:', data.data.total || '未提供');
        
        // 显示前3个任务的基本信息
        const tasksToShow = data.data.tasks.slice(0, 3);
        console.log('\n📋 [DEBUG] 任务列表预览:');
        tasksToShow.forEach((task, index) => {
          console.log(`📋 [TASK-${index + 1}] ID: ${task.task_id}`);
          console.log(`📋 [TASK-${index + 1}] 状态: ${task.task_status}`);
          console.log(`📋 [TASK-${index + 1}] 创建时间: ${new Date(task.created_at * 1000).toLocaleString()}`);
          if (index < tasksToShow.length - 1) console.log('');
        });
      } else {
        console.log('📊 [DEBUG] 任务列表存在:', '否');
        console.log('📊 [DEBUG] data字段内容:', Object.keys(data.data));
      }
    } else {
      console.log('📊 [DEBUG] 数据字段存在:', '否');
      console.log('📊 [DEBUG] 响应字段:', Object.keys(data));
    }

    console.log('\n✅ [DEBUG] 任务列表调试完成');

  } catch (error) {
    console.error('\n❌ [DEBUG] 调试过程中发生错误:', error.message);
    console.error('❌ [DEBUG] 错误详情:', error);
  }
}

// 调试单任务查询
async function debugSingleTask(taskId) {
  console.log(`\n🔍 [DEBUG] 单任务查询调试 (ID: ${taskId})`);
  console.log('='.repeat(80));

  try {
    const token = generateJWTToken();
    const url = `https://api-beijing.klingai.com/v1/videos/image2video/${taskId}`;
    
    console.log('📡 [DEBUG] 请求信息:');
    console.log('🔗 [DEBUG] URL:', url);
    console.log('🔑 [DEBUG] Authorization: Bearer', token.substring(0, 20) + '...');

    console.log('\n⏳ [DEBUG] 发送请求...');
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    console.log('\n📡 [DEBUG] 响应信息:');
    console.log('📊 [DEBUG] HTTP状态:', response.status, response.statusText);

    if (!response.ok) {
      const errorText = await response.text();
      console.log('📄 [DEBUG] 错误响应体:');
      console.log(errorText);
      return;
    }

    const data = await response.json();

    console.log('\n📄 [DEBUG] 完整响应体:');
    console.log('='.repeat(80));
    console.log(JSON.stringify(data, null, 2));
    console.log('='.repeat(80));

    console.log('\n✅ [DEBUG] 单任务查询调试完成');

  } catch (error) {
    console.error('\n❌ [DEBUG] 调试过程中发生错误:', error.message);
  }
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    // 默认调试任务列表
    await debugTaskList();
  } else if (args[0] === 'list') {
    // 调试任务列表
    await debugTaskList();
  } else if (args[0] === 'task' && args[1]) {
    // 调试单个任务
    await debugSingleTask(args[1]);
  } else {
    console.log(`
🔍 Kling AI 响应调试工具

用法:
  node scripts/debug-kling-response.js           # 调试任务列表
  node scripts/debug-kling-response.js list      # 调试任务列表
  node scripts/debug-kling-response.js task <id> # 调试单个任务

示例:
  node scripts/debug-kling-response.js
  node scripts/debug-kling-response.js task task_12345
`);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  debugTaskList,
  debugSingleTask,
  generateJWTToken
};
