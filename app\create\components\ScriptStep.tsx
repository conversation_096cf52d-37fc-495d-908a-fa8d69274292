"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Slider } from "@/components/ui/slider"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Separator } from "@/components/ui/separator"
// Removed direct Prisma import - now using API routes
import { useAuth } from "@/contexts/AuthContext"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog"
import {
  <PERSON><PERSON><PERSON>,
  RefreshCw,
  Download,
  Clock,
  MapPin,
  Users,
  Camera,
  Lightbulb,
  Volume2,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Check,
  ImageIcon,
  Edit,
  FileText,
} from "lucide-react"

interface ScriptStepProps {
  scriptContent: string
  setScriptContent: (content: string) => void
  onGenerateImages?: (scriptData: ScriptData, projectId?: string) => void
  projectId?: string
  editMode?: boolean
  scriptData?: ScriptData
  selectedStyle?: string
}

interface Shot {
  shotNumber: number
  duration: number
  shotType: string
  location: string
  characters: string[]
  action: string
  dialogue?: string
  cameraMovement: string
  lighting: string
  props?: string[]
  mood: string
  soundEffect: string
  transition: string
}

interface ScriptData {
  title: string
  totalDuration: number
  style: string
  shotCount: number
  shots: Shot[]
}

export function ScriptStep({
  scriptContent,
  setScriptContent,
  onGenerateImages,
  projectId,
  editMode = false,
  scriptData,
  selectedStyle
}: ScriptStepProps) {
  const [creationMode, setCreationMode] = useState("keywords")
  const [theme, setTheme] = useState("")
  const [style, setStyle] = useState(selectedStyle || "")
  const [duration, setDuration] = useState([90]) // 90秒 = 1.5分钟
  const [isGenerating, setIsGenerating] = useState(false)
  const [showLoginDialog, setShowLoginDialog] = useState(false)
  const { user } = useAuth()
  
  // 关键词生成模式状态
  const [selectedTags, setSelectedTags] = useState<string[]>([])
  const [customKeywords, setCustomKeywords] = useState("")
  const [selectedMood, setSelectedMood] = useState("")
  
  // 模板选择模式状态
  const [selectedTemplate, setSelectedTemplate] = useState("")
  const [characters, setCharacters] = useState<Array<{id: string, name: string, role: string, background: string}>>([
    { id: '1', name: '', role: '主角', background: '' }
  ])
  const [backgroundSetting, setBackgroundSetting] = useState("")
  
  // 自由创作模式状态
  const [character1, setCharacter1] = useState("")
  const [character2, setCharacter2] = useState("")
  const [supportingCharacter, setSupportingCharacter] = useState("")
  const [requiredScenes, setRequiredScenes] = useState("")
  const [avoidElements, setAvoidElements] = useState("")

  // 在编辑模式下初始化表单数据
  useEffect(() => {
    if (editMode && scriptData) {
      setStyle(scriptData.style || selectedStyle || "")
      setDuration([scriptData.totalDuration || 90])
      setTheme(scriptData.title || "")
    }
  }, [editMode, scriptData, selectedStyle])

  // 在编辑模式下加载项目配置
  useEffect(() => {
    if (editMode && projectId) {
      loadProjectConfig(projectId)
    }
  }, [editMode, projectId])

  // 加载项目配置
  const loadProjectConfig = async (projectId: string) => {
    try {
      const response = await fetch(`/api/projects/${projectId}/config`)
      const data = await response.json()

      if (data.success) {
        const config = data.config

        // 恢复配置数据
        if (config.theme) setTheme(config.theme)
        if (config.selectedTags) setSelectedTags(config.selectedTags)
        if (config.customKeywords) setCustomKeywords(config.customKeywords)
        if (config.selectedMood) setSelectedMood(config.selectedMood)
        if (config.selectedTemplate) setSelectedTemplate(config.selectedTemplate)
        if (config.backgroundSetting) setBackgroundSetting(config.backgroundSetting)
        if (config.character1) setCharacter1(config.character1)
        if (config.character2) setCharacter2(config.character2)
        if (config.supportingCharacter) setSupportingCharacter(config.supportingCharacter)
        if (config.requiredScenes) setRequiredScenes(config.requiredScenes)
        if (config.avoidElements) setAvoidElements(config.avoidElements)

        // 根据配置确定创作模式
        if (config.selectedTemplate) {
          setCreationMode("template")
        } else if (config.selectedTags && config.selectedTags.length > 0) {
          setCreationMode("keywords")
        } else if (config.character1 || config.character2) {
          setCreationMode("free")
        }
      }
    } catch (error) {
      console.error('Error loading project config:', error)
    }
  }

  // 检测是否为JSON格式的分镜头
  const isJsonScript = (content: string) => {
    try {
      const parsed = JSON.parse(content)
      return parsed && typeof parsed === 'object' && parsed.shots && Array.isArray(parsed.shots)
    } catch {
      return false
    }
  }

  // 分镜头展示组件
  const ShotsDisplay = ({ content }: { content: string }) => {
    try {
      const script: ScriptData = JSON.parse(content)
      
      return (
        <div className="space-y-6 relative">
          {/* 剧本头部信息 */}
          <div className="bg-gradient-to-r from-blue-50 to-purple-50 p-6 rounded-xl border border-blue-200">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-2xl font-bold text-gray-900">{script.title}</h3>
              <Badge className="bg-blue-100 text-blue-800 border-blue-300">
                {script.style}
              </Badge>
            </div>
            <div className="grid grid-cols-3 gap-4 text-center">
              <div className="bg-white/70 p-3 rounded-lg">
                <div className="flex items-center justify-center mb-1">
                  <Clock className="w-4 h-4 text-blue-600 mr-1" />
                  <span className="text-sm text-gray-600">总时长</span>
                </div>
                <div className="text-lg font-semibold text-gray-900">
                  {script.totalDuration >= 60 ? `${(script.totalDuration / 60).toFixed(1)}分钟` : `${script.totalDuration}秒`}
                </div>
              </div>
              <div className="bg-white/70 p-3 rounded-lg">
                <div className="flex items-center justify-center mb-1">
                  <Hash className="w-4 h-4 text-purple-600 mr-1" />
                  <span className="text-sm text-gray-600">镜头数</span>
                </div>
                <div className="text-lg font-semibold text-gray-900">{script.shotCount}</div>
              </div>
              <div className="bg-white/70 p-3 rounded-lg">
                <div className="flex items-center justify-center mb-1">
                  <Zap className="w-4 h-4 text-green-600 mr-1" />
                  <span className="text-sm text-gray-600">平均时长</span>
                </div>
                <div className="text-lg font-semibold text-gray-900">
                  {Math.round(script.totalDuration / script.shotCount)}秒
                </div>
              </div>
            </div>
          </div>

          {/* 分镜头列表 */}
          <div className="space-y-4">
            {script.shots.map((shot: Shot, index: number) => (
              <Card key={index} className="hover:shadow-lg transition-shadow duration-200">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center space-x-2">
                      <Badge variant="outline" className="bg-gray-100 text-gray-700 border-gray-300">
                        镜头 {shot.shotNumber}
                      </Badge>
                      <Badge variant="outline" className="bg-blue-100 text-blue-700 border-blue-300">
                        {shot.shotType}
                      </Badge>
                      <Badge variant="outline" className="bg-green-100 text-green-700 border-green-300">
                        {shot.duration}s
                      </Badge>
                    </div>
                    <div className="text-sm text-gray-500">
                      {shot.transition}
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-6">
                    {/* 左侧：基本信息 */}
                    <div className="space-y-4">
                      <div>
                        <h4 className="font-semibold text-gray-900 mb-2">场景动作</h4>
                        <p className="text-gray-700 bg-gray-50 p-3 rounded-lg">
                          {shot.action}
                        </p>
                      </div>
                      
                      {shot.dialogue && (
                        <div>
                          <h4 className="font-semibold text-gray-900 mb-2">对话</h4>
                          <p className="text-gray-700 bg-yellow-50 p-3 rounded-lg border-l-4 border-yellow-400">
                            {shot.dialogue}
                          </p>
                        </div>
                      )}

                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <div className="flex items-center mb-1">
                            <MapPin className="w-4 h-4 text-red-500 mr-1" />
                            <span className="text-sm font-medium text-gray-700">场景</span>
                          </div>
                          <p className="text-gray-600">{shot.location}</p>
                        </div>
                        <div>
                          <div className="flex items-center mb-1">
                            <Users className="w-4 h-4 text-blue-500 mr-1" />
                            <span className="text-sm font-medium text-gray-700">角色</span>
                          </div>
                          <p className="text-gray-600">{shot.characters.join(", ")}</p>
                        </div>
                      </div>
                    </div>

                    {/* 右侧：技术信息 */}
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <div className="flex items-center mb-1">
                            <Camera className="w-4 h-4 text-green-500 mr-1" />
                            <span className="text-sm font-medium text-gray-700">镜头运动</span>
                          </div>
                          <p className="text-gray-600">{shot.cameraMovement}</p>
                        </div>
                        <div>
                          <div className="flex items-center mb-1">
                            <Lightbulb className="w-4 h-4 text-yellow-500 mr-1" />
                            <span className="text-sm font-medium text-gray-700">灯光</span>
                          </div>
                          <p className="text-gray-600">{shot.lighting}</p>
                        </div>
                      </div>

                      <div>
                        <div className="flex items-center mb-1">
                          <Volume2 className="w-4 h-4 text-purple-500 mr-1" />
                          <span className="text-sm font-medium text-gray-700">音效</span>
                        </div>
                        <p className="text-gray-600">{shot.soundEffect}</p>
                      </div>

                      <div>
                        <div className="flex items-center mb-1">
                          <Sparkles className="w-4 h-4 text-pink-500 mr-1" />
                          <span className="text-sm font-medium text-gray-700">情绪</span>
                        </div>
                        <Badge className="bg-pink-100 text-pink-700 border-pink-300">
                          {shot.mood}
                        </Badge>
                      </div>

                      {shot.props && shot.props.length > 0 && (
                        <div>
                          <div className="flex items-center mb-1">
                            <Hash className="w-4 h-4 text-gray-500 mr-1" />
                            <span className="text-sm font-medium text-gray-700">道具</span>
                          </div>
                          <div className="flex flex-wrap gap-1">
                            {shot.props.map((prop: string, propIndex: number) => (
                              <Badge key={propIndex} variant="outline" className="text-xs">
                                {prop}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
          
          {/* 一键文生图按钮 */}
          <div className="sticky bottom-6 right-6 flex justify-end">
            <Button
              onClick={() => onGenerateImages && onGenerateImages(script)}
              className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-200 px-6 py-3 rounded-full"
              size="lg"
            >
              <ImageIcon className="w-5 h-5 mr-2" />
              一键文生图
            </Button>
          </div>
        </div>
      )
    } catch (error) {
      console.error("解析分镜头数据失败:", error)
      return <div className="text-red-500">解析分镜头数据失败</div>
    }
  }

  // 生成脚本函数
  const handleGenerateScript = async () => {
    if (isGenerating) return
    
    // 检查用户是否登录
    if (!user) {
      setShowLoginDialog(true)
      return
    }
    
    // 验证必填字段
    if (creationMode === 'free' && !theme.trim()) {
      alert('请填写故事大纲')
      return
    }
    
    if (creationMode === 'keywords' && !theme.trim()) {
      alert('请填写核心主题')
      return
    }

    if (creationMode === 'template' && !selectedTemplate) {
      alert('请选择一个剧本模板')
      return
    }

    if (!style) {
      alert('请选择故事风格')
      return
    }

    setIsGenerating(true)
    
    // 调试信息：获取用户信息
    console.log('当前用户信息:', {
      userId: user?.id,
      email: user?.email,
      creationMode,
      theme,
      style,
      duration: duration[0]
    })
    
    try {
      const response = await fetch('/api/generate-script', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          creationMode,
          theme,
          style,
          duration: duration[0],
          character1,
          character2,
          supportingCharacter,
          requiredScenes,
          avoidElements,
          // 关键词生成模式的参数
          selectedTags,
          customKeywords,
          selectedMood,
          // 模板选择模式的参数
          selectedTemplate,
          characters,
          backgroundSetting
        }),
      })

      const data = await response.json()
      console.log(data)
      if (!response.ok) {
        throw new Error(data.error || '生成失败')
      }

      setScriptContent(data.script)

      // 保存到数据库
      try {
        const scriptData = JSON.parse(data.script)
        
        // 获取当前用户ID
        const userId = user?.id
        if (!userId) {
          console.warn('用户未登录，无法保存到数据库')
          return
        }

        const saveResult = await fetch('/api/scripts/save', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            userId,
            title: scriptData.title || '未命名剧本',
            description: `通过${creationMode === 'keywords' ? '关键词' : creationMode === 'template' ? '模板' : '自由创作'}生成的${style}风格剧本`,
            creationMode: creationMode as 'keywords' | 'template' | 'free',
            style,
            duration: duration[0],
            scriptContent: data.script,
            scriptData,
            generationConfig: {
              theme,
              selectedTags,
              customKeywords,
              selectedMood,
              selectedTemplate,
              characters: characters.map(c => ({
                name: c.name,
                role: c.role,
                background: c.background
              })),
              backgroundSetting,
              character1,
              character2,
              supportingCharacter,
              requiredScenes,
              avoidElements
            }
          })
        })

        const saveData = await saveResult.json()
        if (saveData.success) {
          console.log('脚本已保存到数据库，项目ID:', saveData.projectId)
          // 传递 projectId 到下一步
          onGenerateImages && onGenerateImages(scriptData, saveData.projectId)
          return
        } else {
          console.error('保存到数据库失败:', saveData.error)
        }
      } catch (saveError) {
        console.error('保存到数据库时出错:', saveError)
        // 不阻止用户看到生成的脚本，即使保存失败
      }

    } catch (error) {
      console.error('生成脚本失败:', error)
      alert('生成脚本失败，请重试')
    } finally {
      setIsGenerating(false)
    }
  }

  // 获取当前用户ID的辅助函数 - 现在直接从AuthContext获取
  const getCurrentUserId = async () => {
    return user?.id || null
  }

  return (
    <div className="h-full flex bg-gradient-to-br from-slate-50/50 to-blue-50/30">
      {/* 登录提示对话框 */}
      <Dialog open={showLoginDialog} onOpenChange={setShowLoginDialog}>
        <DialogContent className="sm:max-w-md bg-white/95 backdrop-blur-sm border border-slate-200">
          <DialogHeader>
            <DialogTitle className="text-slate-800 font-bold">请先登录</DialogTitle>
            <DialogDescription className="text-slate-600">
              需要登录后才能生成剧本。登录后可以保存和管理您的创作内容。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter className="sm:justify-start space-x-2">
            <Button
              onClick={() => window.location.href = '/auth/login'}
              className="bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
            >
              立即登录
            </Button>
            <Button
              variant="outline"
              onClick={() => window.location.href = '/auth/register'}
              className="border-slate-300 hover:border-slate-400 hover:bg-slate-50"
            >
              注册账号
            </Button>
            <Button
              variant="ghost"
              onClick={() => setShowLoginDialog(false)}
              className="text-slate-600 hover:text-slate-800 hover:bg-slate-100"
            >
              稍后再说
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      {/* 左侧输入区 (40%) */}
      <div className="w-2/5 p-8 bg-white/80 backdrop-blur-sm border-r border-slate-200/60">
        <div className="flex items-center justify-between mb-8">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
              <Sparkles className="w-5 h-5 text-white" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-slate-800">
                {editMode ? '编辑故事脚本' : '创建故事脚本'}
              </h2>
              <p className="text-sm text-slate-600 mt-1">
                {editMode ? '修改和完善您的创意' : '开始您的创意之旅'}
              </p>
            </div>
          </div>
          {editMode && (
            <Badge className="bg-gradient-to-r from-amber-100 to-orange-100 text-amber-800 border-amber-200 font-medium px-3 py-1.5">
              <Edit className="w-3 h-3 mr-1" />
              编辑模式
            </Badge>
          )}
        </div>

        {/* 创作方式选择 */}
        <div className="mb-8">
          <div className="flex items-center space-x-2 mb-6">
            <div className="w-6 h-6 rounded-lg bg-gradient-to-br from-purple-500 to-pink-600 flex items-center justify-center">
              <Sparkles className="w-3 h-3 text-white" />
            </div>
            <Label className="text-lg font-bold text-slate-800">选择创作方式</Label>
          </div>
          <RadioGroup value={creationMode} onValueChange={setCreationMode} className="space-y-4">
            <div
              className="group flex items-start space-x-4 p-5 rounded-xl border-2 border-slate-200 hover:border-orange-300 hover:bg-gradient-to-r hover:from-orange-50 hover:to-amber-50 transition-all duration-300 cursor-pointer hover:shadow-lg hover:scale-[1.02]"
              onClick={() => setCreationMode("keywords")}
            >
              <RadioGroupItem value="keywords" id="keywords" className="mt-1.5 border-2 border-orange-400 text-orange-600" />
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-2">
                  <span className="text-2xl">🏷️</span>
                  <Label htmlFor="keywords" className="font-bold text-slate-800 cursor-pointer group-hover:text-orange-700 transition-colors duration-300">
                    关键词生成
                  </Label>
                  <Badge className="bg-orange-100 text-orange-700 border-orange-200 text-xs px-2 py-0.5">
                    智能推荐
                  </Badge>
                </div>
                <p className="text-sm text-slate-600 leading-relaxed group-hover:text-orange-600 transition-colors duration-300">
                  通过选择关键词标签和情感基调，AI 自动组合生成创意剧本，适合快速创作
                </p>
              </div>
            </div>
            <div
              className="group flex items-start space-x-4 p-5 rounded-xl border-2 border-slate-200 hover:border-indigo-300 hover:bg-gradient-to-r hover:from-indigo-50 hover:to-blue-50 transition-all duration-300 cursor-pointer hover:shadow-lg hover:scale-[1.02]"
              onClick={() => setCreationMode("template")}
            >
              <RadioGroupItem value="template" id="template" className="mt-1.5 border-2 border-indigo-400 text-indigo-600" />
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-2">
                  <span className="text-2xl">📝</span>
                  <Label htmlFor="template" className="font-bold text-slate-800 cursor-pointer group-hover:text-indigo-700 transition-colors duration-300">
                    模板选择
                  </Label>
                  <Badge className="bg-indigo-100 text-indigo-700 border-indigo-200 text-xs px-2 py-0.5">
                    经典框架
                  </Badge>
                </div>
                <p className="text-sm text-slate-600 leading-relaxed group-hover:text-indigo-600 transition-colors duration-300">
                  从经典剧本模板中选择，快速生成结构完整的故事框架，适合新手创作者
                </p>
              </div>
            </div>
            <div
              className="group flex items-start space-x-4 p-5 rounded-xl border-2 border-slate-200 hover:border-teal-300 hover:bg-gradient-to-r hover:from-teal-50 hover:to-cyan-50 transition-all duration-300 cursor-pointer hover:shadow-lg hover:scale-[1.02]"
              onClick={() => setCreationMode("free")}
            >
              <RadioGroupItem value="free" id="free" className="mt-1.5 border-2 border-teal-400 text-teal-600" />
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-2">
                  <span className="text-2xl">✍️</span>
                  <Label htmlFor="free" className="font-bold text-slate-800 cursor-pointer group-hover:text-teal-700 transition-colors duration-300">
                    自由创作
                  </Label>
                  <Badge className="bg-teal-100 text-teal-700 border-teal-200 text-xs px-2 py-0.5">
                    完全自定义
                  </Badge>
                </div>
                <p className="text-sm text-slate-600 leading-relaxed group-hover:text-teal-600 transition-colors duration-300">
                  完全自定义故事大纲、角色设定，打造独一无二的原创剧本，适合有经验的创作者
                </p>
              </div>
            </div>
          </RadioGroup>
        </div>

        {/* 动态输入表单 */}
        <div className="space-y-6">
          {/* 关键词生成模式 */}
          {creationMode === "keywords" && (
            <div className="space-y-8">
              <div className="bg-gradient-to-r from-orange-50 to-amber-50 p-6 rounded-xl border border-orange-200">
                <Label htmlFor="theme" className="text-lg font-bold text-slate-800 mb-3 block flex items-center">
                  <span className="text-xl mr-2">💡</span>
                  核心主题 <span className="text-red-500 ml-1">*</span>
                </Label>
                <Input
                  id="theme"
                  placeholder="如：霸道总裁爱上灰姑娘、时间旅行者的爱情、校园青春恋曲..."
                  value={theme}
                  onChange={(e) => setTheme(e.target.value)}
                  className="h-12 bg-white/80 border-orange-300 focus:border-orange-500 focus:ring-2 focus:ring-orange-200 text-base font-medium placeholder:text-orange-400/70 transition-all duration-300"
                />
                <p className="text-xs text-orange-600 mt-2 font-medium">
                  ✨ 描述您想要创作的故事核心概念，越具体越好
                </p>
              </div>

              <div className="bg-gradient-to-r from-orange-50 to-amber-50 p-6 rounded-xl border border-orange-200">
                <Label className="text-lg font-bold text-slate-800 mb-4 block flex items-center">
                  <span className="text-xl mr-2">🏷️</span>
                  关键词标签
                </Label>
                <div className="grid grid-cols-2 gap-3 mb-4">
                  {[
                    "校园青春", "职场励志", "古装宫廷", "现代都市", "科幻未来", "武侠江湖",
                    "家庭伦理", "医疗救援", "律政精英", "创业奋斗", "明星娱乐", "军旅生活"
                  ].map((tag, index) => (
                    <button
                      key={index}
                      onClick={() => {
                        setSelectedTags(prev =>
                          prev.includes(tag)
                            ? prev.filter(t => t !== tag)
                            : [...prev, tag]
                        )
                      }}
                      className={`group flex items-center justify-center px-4 py-3 text-sm rounded-xl border-2 font-semibold transition-all duration-300 hover:shadow-lg hover:scale-105 ${
                        selectedTags.includes(tag)
                          ? "border-orange-500 bg-gradient-to-r from-orange-500 to-amber-500 text-white shadow-lg shadow-orange-200/50"
                          : "border-orange-200 bg-white/80 text-slate-700 hover:border-orange-400 hover:bg-gradient-to-r hover:from-orange-50 hover:to-amber-50 hover:text-orange-700"
                      }`}
                    >
                      {selectedTags.includes(tag) && (
                        <Check className="w-4 h-4 mr-2 animate-in zoom-in-50 duration-200" />
                      )}
                      {tag}
                    </button>
                  ))}
                </div>
                <Input
                  placeholder="输入自定义关键词，用逗号分隔..."
                  value={customKeywords}
                  onChange={(e) => setCustomKeywords(e.target.value)}
                  className="bg-white/80 border-orange-300 focus:border-orange-500 focus:ring-2 focus:ring-orange-200 placeholder:text-orange-400/70"
                />
              </div>

              <div className="bg-gradient-to-r from-orange-50 to-amber-50 p-6 rounded-xl border border-orange-200">
                <Label className="text-lg font-bold text-slate-800 mb-4 block flex items-center">
                  <span className="text-xl mr-2">🎭</span>
                  情感基调
                </Label>
                <div className="grid grid-cols-2 gap-4">
                  {[
                    { value: "sweet", label: "甜宠", emoji: "💕", desc: "温馨浪漫" },
                    { value: "heart", label: "虐心", emoji: "💔", desc: "情感波折" },
                    { value: "funny", label: "搞笑", emoji: "😄", desc: "轻松幽默" },
                    { value: "warm", label: "治愈", emoji: "🌸", desc: "温暖感人" }
                  ].map((mood) => (
                    <button
                      key={mood.value}
                      onClick={() => setSelectedMood(mood.value === selectedMood ? "" : mood.value)}
                      className={`group flex flex-col items-center justify-center p-4 rounded-xl border-2 font-semibold transition-all duration-300 hover:shadow-lg hover:scale-105 ${
                        selectedMood === mood.value
                          ? "border-orange-500 bg-gradient-to-br from-orange-500 to-amber-500 text-white shadow-lg shadow-orange-200/50"
                          : "border-orange-200 bg-white/80 text-slate-700 hover:border-orange-400 hover:bg-gradient-to-br hover:from-orange-50 hover:to-amber-50 hover:text-orange-700"
                      }`}
                    >
                      <span className="text-2xl mb-2">{mood.emoji}</span>
                      <span className="text-base font-bold">{mood.label}</span>
                      <span className="text-xs opacity-80 mt-1">{mood.desc}</span>
                      {selectedMood === mood.value && (
                        <Check className="w-4 h-4 mt-2 animate-in zoom-in-50 duration-200" />
                      )}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* 模板选择模式 */}
          {creationMode === "template" && (
            <>
              <div>
                <Label className="text-base font-medium mb-3 block">选择剧本模板</Label>
                <div className="grid grid-cols-1 gap-4">
                  {[
                    {
                      title: "霸道总裁模板",
                      desc: "经典都市言情，霸道总裁与平凡女主的爱情故事",
                      tags: ["都市", "言情", "甜宠"],
                      color: "blue"
                    },
                    {
                      title: "校园初恋模板", 
                      desc: "青春校园背景，初恋的美好与青涩",
                      tags: ["校园", "青春", "初恋"],
                      color: "green"
                    },
                    {
                      title: "穿越重生模板",
                      desc: "现代人穿越到古代或重生回到过去",
                      tags: ["穿越", "古装", "逆袭"],
                      color: "purple"
                    },
                    {
                      title: "婚恋综艺模板",
                      desc: "真人秀综艺形式，多人恋爱关系",
                      tags: ["综艺", "多线", "现实"],
                      color: "pink"
                    }
                  ].map((template, index) => (
                    <div
                      key={index}
                      onClick={() => setSelectedTemplate(template.title === selectedTemplate ? "" : template.title)}
                      className={`p-4 rounded-xl border-2 cursor-pointer transition-all duration-200 hover:scale-[1.02] hover:shadow-lg ${
                        selectedTemplate === template.title
                          ? `bg-gradient-to-r from-${template.color}-200 to-${template.color}-300 border-${template.color}-400 shadow-xl ring-2 ring-${template.color}-200`
                          : `bg-gradient-to-r from-${template.color}-50 to-${template.color}-100 border-${template.color}-200 hover:border-${template.color}-300`
                      }`}
                    >
                      <div className="flex justify-between items-start mb-2">
                        <h4 className={`font-semibold text-${template.color}-800`}>{template.title}</h4>
                        <div className="flex space-x-1">
                          {template.tags.map((tag, tagIndex) => (
                            <span
                              key={tagIndex}
                              className={`px-2 py-1 text-xs rounded bg-${template.color}-200 text-${template.color}-700`}
                            >
                              {tag}
                            </span>
                          ))}
                        </div>
                      </div>
                      <p className={`text-sm text-${template.color}-700`}>{template.desc}</p>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <Label className="text-base font-medium mb-2 block">个性化设置</Label>
                
                {/* 角色设置部分 */}
                <div className="space-y-3 mb-4">
                  <div className="flex items-center justify-between">
                    <Label className="text-sm text-gray-600">角色设定</Label>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const newId = Date.now().toString()
                        setCharacters([...characters, { id: newId, name: '', role: '配角', background: '' }])
                      }}
                      className="bg-transparent border-indigo-200 text-indigo-600 hover:bg-indigo-50"
                    >
                      <Hash className="w-3 h-3 mr-1" />
                      新增人物
                    </Button>
                  </div>
                  
                  {characters.map((character, index) => (
                    <div key={character.id} className="space-y-3 p-4 border border-gray-200 rounded-lg bg-gradient-to-br from-indigo-50/30 to-purple-50/30">
                      <div className="flex gap-2 items-center">
                        <Input 
                          placeholder="角色姓名" 
                          value={character.name}
                          onChange={(e) => {
                            const newCharacters = [...characters]
                            newCharacters[index].name = e.target.value
                            setCharacters(newCharacters)
                          }}
                          className="flex-1 bg-gradient-to-r from-indigo-50 to-purple-50 border-indigo-200" 
                        />
                        <Select 
                          value={character.role} 
                          onValueChange={(value) => {
                            const newCharacters = [...characters]
                            newCharacters[index].role = value
                            setCharacters(newCharacters)
                          }}
                        >
                          <SelectTrigger className="w-24 h-10 bg-gradient-to-r from-indigo-50 to-purple-50 border-indigo-200 hover:border-indigo-300 focus:border-indigo-400 focus:ring-2 focus:ring-indigo-100 transition-all duration-300 ease-in-out shadow-sm hover:shadow-md">
                            <SelectValue className="text-gray-600 font-medium" />
                          </SelectTrigger>
                          <SelectContent className="bg-white/95 backdrop-blur-sm border border-indigo-200 shadow-xl rounded-xl p-1 animate-in fade-in-0 zoom-in-95 duration-200 ease-out">
                            <SelectItem 
                              value="主角" 
                              className="rounded-lg my-1 px-3 py-2.5 text-sm font-medium text-gray-700 hover:bg-gradient-to-r hover:from-indigo-100 hover:to-purple-100 hover:text-indigo-700 focus:bg-gradient-to-r focus:from-indigo-100 focus:to-purple-100 focus:text-indigo-700 transition-all duration-200 ease-in-out cursor-pointer"
                            >
                              主角
                            </SelectItem>
                            <SelectItem 
                              value="配角" 
                              className="rounded-lg my-1 px-3 py-2.5 text-sm font-medium text-gray-700 hover:bg-gradient-to-r hover:from-indigo-100 hover:to-purple-100 hover:text-indigo-700 focus:bg-gradient-to-r focus:from-indigo-100 focus:to-purple-100 focus:text-indigo-700 transition-all duration-200 ease-in-out cursor-pointer"
                            >
                              配角
                            </SelectItem>
                            <SelectItem 
                              value="反派" 
                              className="rounded-lg my-1 px-3 py-2.5 text-sm font-medium text-gray-700 hover:bg-gradient-to-r hover:from-indigo-100 hover:to-purple-100 hover:text-indigo-700 focus:bg-gradient-to-r focus:from-indigo-100 focus:to-purple-100 focus:text-indigo-700 transition-all duration-200 ease-in-out cursor-pointer"
                            >
                              反派
                            </SelectItem>
                            <SelectItem 
                              value="路人" 
                              className="rounded-lg my-1 px-3 py-2.5 text-sm font-medium text-gray-700 hover:bg-gradient-to-r hover:from-indigo-100 hover:to-purple-100 hover:text-indigo-700 focus:bg-gradient-to-r focus:from-indigo-100 focus:to-purple-100 focus:text-indigo-700 transition-all duration-200 ease-in-out cursor-pointer"
                            >
                              路人
                            </SelectItem>
                          </SelectContent>
                        </Select>
                        {characters.length > 1 && (
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              setCharacters(characters.filter(c => c.id !== character.id))
                            }}
                            className="bg-transparent border-red-200 text-red-600 hover:bg-red-50"
                          >
                            ×
                          </Button>
                        )}
                      </div>
                      <div>
                        <Label className="text-xs text-gray-500 mb-1 block">人物背景</Label>
                        <Textarea 
                          placeholder="描述角色的身份、性格、经历等背景信息..."
                          value={character.background}
                          onChange={(e) => {
                            const newCharacters = [...characters]
                            newCharacters[index].background = e.target.value
                            setCharacters(newCharacters)
                          }}
                          className="min-h-[60px] bg-gradient-to-r from-indigo-50 to-purple-50 border-indigo-200 text-sm resize-none"
                          rows={2}
                        />
                      </div>
                    </div>
                  ))}
                </div>
                
                {/* 分隔线 */}
                <Separator className="my-6" />
                
                <div>
                  <Label className="text-sm text-gray-600">背景设定</Label>
                  <Input 
                    placeholder="如：上海金融中心" 
                    value={backgroundSetting}
                    onChange={(e) => setBackgroundSetting(e.target.value)}
                    className="mt-1 bg-gradient-to-r from-indigo-50 to-purple-50 border-indigo-200" 
                  />
                </div>
              </div>
            </>
          )}

          {/* 自由创作模式 */}
          {creationMode === "free" && (
            <>
              <div>
                <Label htmlFor="story-outline" className="text-base font-medium mb-2 block">
                  故事大纲 <span className="text-red-500">*</span>
                </Label>
                <Textarea
                  id="story-outline"
                  placeholder="请详细描述你的故事情节，包括主要角色、冲突、发展脉络等...&#10;&#10;例如：&#10;第一幕：介绍女主角小雨，平凡的咖啡店员工&#10;第二幕：偶遇霸道总裁男主角，产生误会&#10;第三幕：经历种种波折，最终走到一起"
                  value={theme}
                  onChange={(e) => setTheme(e.target.value)}
                  className="mt-2 min-h-[120px] resize-none bg-gradient-to-r from-teal-50 to-cyan-50 border-teal-200 focus:border-teal-400"
                />
              </div>

              <div>
                <Label className="text-base font-medium mb-2 block">角色设定</Label>
                <div className="space-y-3">
                  <Input 
                    placeholder="主角1：性格、职业、背景..." 
                    value={character1}
                    onChange={(e) => setCharacter1(e.target.value)}
                    className="bg-gradient-to-r from-teal-50 to-cyan-50 border-teal-200"
                  />
                  <Input 
                    placeholder="主角2：性格、职业、背景..." 
                    value={character2}
                    onChange={(e) => setCharacter2(e.target.value)}
                    className="bg-gradient-to-r from-teal-50 to-cyan-50 border-teal-200"
                  />
                  <Input 
                    placeholder="配角或反派（可选）..." 
                    value={supportingCharacter}
                    onChange={(e) => setSupportingCharacter(e.target.value)}
                    className="bg-gradient-to-r from-teal-50 to-cyan-50 border-teal-200"
                  />
                </div>
              </div>

              <div>
                <Label className="text-base font-medium mb-2 block">特殊要求</Label>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm text-gray-600 mb-1 block">必须包含的场景</Label>
                    <Input 
                      placeholder="如：告白场景、分手场景" 
                      value={requiredScenes}
                      onChange={(e) => setRequiredScenes(e.target.value)}
                      className="bg-gradient-to-r from-teal-50 to-cyan-50 border-teal-200" 
                    />
                  </div>
                  <div>
                    <Label className="text-sm text-gray-600 mb-1 block">避免的元素</Label>
                    <Input 
                      placeholder="如：第三者插足、悲剧结局" 
                      value={avoidElements}
                      onChange={(e) => setAvoidElements(e.target.value)}
                      className="bg-gradient-to-r from-teal-50 to-cyan-50 border-teal-200" 
                    />
                  </div>
                </div>
              </div>
            </>
          )}

          {/* 通用设置 */}
          <div>
            <Label htmlFor="style" className="text-base font-medium mb-2 block">
              风格
            </Label>
            <Select value={style} onValueChange={setStyle}>
              <SelectTrigger className="mt-2 h-12 bg-gradient-to-r from-purple-50 to-pink-50 border-purple-200 hover:border-purple-300 focus:border-purple-400 focus:ring-2 focus:ring-purple-100 transition-all duration-300 ease-in-out shadow-sm hover:shadow-md">
                <SelectValue placeholder="选择故事风格" className="text-gray-600 font-medium" />
              </SelectTrigger>
              <SelectContent className="bg-white/95 backdrop-blur-sm border border-purple-200 shadow-xl rounded-xl p-1 animate-in fade-in-0 zoom-in-95 duration-200 ease-out">
                <SelectItem 
                  value="romantic" 
                  className="rounded-lg my-1 px-3 py-2.5 text-sm font-medium text-gray-700 hover:bg-gradient-to-r hover:from-pink-100 hover:to-purple-100 hover:text-purple-700 focus:bg-gradient-to-r focus:from-pink-100 focus:to-purple-100 focus:text-purple-700 transition-all duration-200 ease-in-out cursor-pointer"
                >
                  💕 浪漫
                </SelectItem>
                <SelectItem 
                  value="comedy" 
                  className="rounded-lg my-1 px-3 py-2.5 text-sm font-medium text-gray-700 hover:bg-gradient-to-r hover:from-yellow-100 hover:to-orange-100 hover:text-orange-700 focus:bg-gradient-to-r focus:from-yellow-100 focus:to-orange-100 focus:text-orange-700 transition-all duration-200 ease-in-out cursor-pointer"
                >
                  😄 喜剧
                </SelectItem>
                <SelectItem 
                  value="thriller" 
                  className="rounded-lg my-1 px-3 py-2.5 text-sm font-medium text-gray-700 hover:bg-gradient-to-r hover:from-gray-100 hover:to-slate-100 hover:text-slate-700 focus:bg-gradient-to-r focus:from-gray-100 focus:to-slate-100 focus:text-slate-700 transition-all duration-200 ease-in-out cursor-pointer"
                >
                  🕵️ 悬疑
                </SelectItem>
                <SelectItem 
                  value="action" 
                  className="rounded-lg my-1 px-3 py-2.5 text-sm font-medium text-gray-700 hover:bg-gradient-to-r hover:from-red-100 hover:to-orange-100 hover:text-red-700 focus:bg-gradient-to-r focus:from-red-100 focus:to-orange-100 focus:text-red-700 transition-all duration-200 ease-in-out cursor-pointer"
                >
                  💥 动作
                </SelectItem>
                <SelectItem 
                  value="drama" 
                  className="rounded-lg my-1 px-3 py-2.5 text-sm font-medium text-gray-700 hover:bg-gradient-to-r hover:from-blue-100 hover:to-indigo-100 hover:text-blue-700 focus:bg-gradient-to-r focus:from-blue-100 focus:to-indigo-100 focus:text-blue-700 transition-all duration-200 ease-in-out cursor-pointer"
                >
                  🎭 剧情
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label className="text-base font-medium mb-3 block">时长</Label>
            <div className="mt-2 px-4 py-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
              <Slider value={duration} onValueChange={setDuration} max={120} min={30} step={15} className="w-full [&_.slider-track]:bg-gradient-to-r [&_.slider-track]:from-blue-200 [&_.slider-track]:to-indigo-200 [&_.slider-range]:bg-gradient-to-r [&_.slider-range]:from-blue-500 [&_.slider-range]:to-indigo-500 [&_.slider-thumb]:bg-white [&_.slider-thumb]:border-2 [&_.slider-thumb]:border-blue-500 [&_.slider-thumb]:shadow-lg" />
              <div className="flex justify-between text-sm text-gray-600 mt-3">
                <span className="font-medium">30秒</span>
                <span className="font-bold text-blue-700 bg-white px-3 py-1 rounded-full shadow-sm border border-blue-200">
                  {duration[0] >= 60 ? `${(duration[0] / 60).toFixed(1)}分钟` : `${duration[0]}秒`}
                </span>
                <span className="font-medium">2分钟</span>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 rounded-xl border border-blue-200">
            <Button
              className="w-full h-14 bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 hover:from-blue-700 hover:via-indigo-700 hover:to-purple-700 text-white font-bold text-lg shadow-xl hover:shadow-2xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed hover:scale-105 disabled:hover:scale-100"
              onClick={handleGenerateScript}
              disabled={isGenerating}
            >
              {isGenerating ? (
                <div className="flex items-center">
                  <RefreshCw className="w-5 h-5 mr-3 animate-spin" />
                  <div className="flex flex-col items-start">
                    <span>正在生成中...</span>
                    <span className="text-xs opacity-80">AI正在为您创作精彩剧本</span>
                  </div>
                </div>
              ) : (
                <div className="flex items-center">
                  <Sparkles className="w-5 h-5 mr-3" />
                  <div className="flex flex-col items-start">
                    <span>
                      {creationMode === "keywords" ? "基于关键词生成剧本" :
                       creationMode === "template" ? "基于模板生成剧本" :
                       "生成自由创作剧本"}
                    </span>
                    <span className="text-xs opacity-80">点击开始AI创作之旅</span>
                  </div>
                </div>
              )}
            </Button>
          </div>
        </div>
      </div>

      {/* 右侧预览区 (60%) */}
      <div className="w-3/5 p-8 bg-gradient-to-br from-white/60 to-slate-50/80 backdrop-blur-sm">
        <div className="h-full flex flex-col">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center">
                <FileText className="w-4 h-4 text-white" />
              </div>
              <div>
                <h3 className="text-xl font-bold text-slate-800">剧本预览</h3>
                <p className="text-sm text-slate-600">AI生成的创意内容</p>
              </div>
            </div>
            <div className="flex space-x-3">
              <Button
                variant="outline"
                size="sm"
                onClick={handleGenerateScript}
                disabled={isGenerating}
                className="group bg-white/80 border-slate-300 hover:border-indigo-400 hover:bg-indigo-50 hover:text-indigo-700 transition-all duration-300"
              >
                <RefreshCw className={`w-4 h-4 mr-2 group-hover:scale-110 transition-transform duration-300 ${isGenerating ? 'animate-spin' : ''}`} />
                {isGenerating ? '生成中...' : '重新生成'}
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="group bg-white/80 border-slate-300 hover:border-emerald-400 hover:bg-emerald-50 hover:text-emerald-700 transition-all duration-300"
              >
                <Download className="w-4 h-4 mr-2 group-hover:scale-110 transition-transform duration-300" />
                导出
              </Button>
            </div>
          </div>

          <Card className="flex-1 bg-white/80 backdrop-blur-sm border-slate-200/60 shadow-xl">
            <CardContent className="p-0 h-full relative overflow-hidden">
              {scriptContent && isJsonScript(scriptContent) ? (
                <div className="h-full overflow-y-auto p-6">
                  <ShotsDisplay content={scriptContent} />
                </div>
              ) : (
                <div className="h-full relative">
                  {!scriptContent ? (
                    <div className="h-full flex items-center justify-center">
                      <div className="text-center">
                        <div className="w-24 h-24 rounded-full bg-gradient-to-br from-slate-100 to-gray-200 flex items-center justify-center mx-auto mb-4">
                          <Sparkles className="w-12 h-12 text-slate-400" />
                        </div>
                        <h4 className="text-lg font-semibold text-slate-700 mb-2">等待创作中...</h4>
                        <p className="text-slate-500 text-sm max-w-sm">
                          选择创作方式并填写相关信息，AI将为您生成精彩的剧本内容
                        </p>
                      </div>
                    </div>
                  ) : (
                    <>
                      <Textarea
                        placeholder="生成的剧本内容将在这里显示..."
                        value={scriptContent}
                        onChange={(e) => setScriptContent(e.target.value)}
                        className="h-full resize-none border-0 focus-visible:ring-0 text-base leading-relaxed p-6 bg-transparent"
                      />
                      {/* 当剧本内容存在但不是JSON格式时，也显示按钮 */}
                      {scriptContent && scriptContent.trim() && (
                        <div className="absolute bottom-6 right-6">
                          <Button
                            onClick={() => {
                              // 如果不是JSON格式，创建一个简单的脚本数据结构
                              const simpleScript = {
                                title: "AI生成剧本",
                                style: style || "drama",
                                totalDuration: duration[0],
                                shotCount: 1,
                                shots: [{
                                  shotNumber: 1,
                                  duration: duration[0],
                                  shotType: "全景",
                                  location: "场景设定",
                                  characters: ["主角"],
                                  action: scriptContent.substring(0, 200) + "...",
                                  dialogue: "",
                                  cameraMovement: "固定",
                                  lighting: "自然光",
                                  props: [],
                                  mood: "中性",
                                  soundEffect: "环境音",
                                  transition: "切入"
                                }]
                              };
                              onGenerateImages && onGenerateImages(simpleScript);
                            }}
                            className="group bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-bold shadow-xl hover:shadow-2xl transition-all duration-300 px-8 py-4 rounded-full hover:scale-105"
                          >
                            <ImageIcon className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform duration-300" />
                            一键文生图
                          </Button>
                        </div>
                      )}
                    </>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
} 