import { NextRequest, NextResponse } from 'next/server'
import { getVideoGenerationTaskByKlingId, getVideoGenerationTasksByProject, getVideoGenerationTasksByShot } from '@/lib/prisma-video-operations'

// GET /api/video-tasks/[id] - 根据任务ID查询视频生成状态
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const taskId = params.id
    
    if (!taskId) {
      return NextResponse.json({
        success: false,
        error: '任务ID是必需的'
      }, { status: 400 })
    }

    console.log('🔍 [VIDEO-TASK] 查询视频任务状态:', taskId)

    // 首先尝试作为 Kling Task ID 查询
    let task = await getVideoGenerationTaskByKlingId(taskId)
    
    if (!task) {
      // 如果没找到，尝试作为数据库任务ID查询
      const { prisma } = await import('@/lib/prisma')
      task = await prisma.video_generation_tasks.findUnique({
        where: { id: taskId },
        include: {
          generated_videos: {
            orderBy: {
              created_at: 'desc'
            }
          }
        }
      })
    }

    if (!task) {
      return NextResponse.json({
        success: false,
        error: '未找到指定的视频任务'
      }, { status: 404 })
    }

    console.log('✅ [VIDEO-TASK] 找到视频任务:', {
      id: task.id,
      status: task.status,
      klingTaskId: task.kling_task_id,
      videoCount: task.generated_videos?.length || 0
    })

    return NextResponse.json({
      success: true,
      task: {
        id: task.id,
        projectId: task.project_id,
        shotId: task.shot_id,
        userId: task.user_id,
        taskName: task.task_name,
        prompt: task.prompt,
        negativePrompt: task.negative_prompt,
        imageUrl: task.image_url,
        modelName: task.model_name,
        aspectRatio: task.aspect_ratio,
        duration: task.duration,
        cfgScale: task.cfg_scale,
        mode: task.mode,
        seed: task.seed,
        status: task.status,
        klingTaskId: task.kling_task_id,
        errorMessage: task.error_message,
        errorCode: task.error_code,
        startedAt: task.started_at,
        completedAt: task.completed_at,
        createdAt: task.created_at,
        updatedAt: task.updated_at,
        apiRequestPayload: task.api_request_payload,
        apiResponseData: task.api_response_data,
        generatedVideos: task.generated_videos?.map((video: any) => ({
          id: video.id,
          taskId: video.task_id,
          videoUrl: video.video_url,
          videoDurationSeconds: video.video_duration_seconds,
          videoFormat: video.video_format,
          videoSizeBytes: video.video_size_bytes?.toString(),
          videoWidth: video.video_width,
          videoHeight: video.video_height,
          storageProvider: video.storage_provider,
          cdnUrl: video.cdn_url,
          generationTimeSeconds: video.generation_time_seconds,
          metadata: video.metadata,
          createdAt: video.created_at,
          updatedAt: video.updated_at
        })) || []
      }
    })

  } catch (error) {
    console.error('❌ [VIDEO-TASK] 查询视频任务失败:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '查询视频任务失败'
    }, { status: 500 })
  }
}
