import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Play, Menu, FileText, Video, Music, ArrowRight } from "lucide-react"
import Link from "next/link"
import { UserMenu } from "@/components/auth/UserMenu"

export default function HomePage() {
  return (
    <div className="min-h-screen bg-white">
      {/* 顶部导航栏 */}
      <header className="fixed top-0 left-0 right-0 z-50 bg-white/95 backdrop-blur-sm border-b border-gray-200 shadow-sm">
        <div className="container mx-auto px-4 h-16 flex items-center justify-between">
          {/* Logo区域 */}
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg flex items-center justify-center">
              <Video className="w-5 h-5 text-white" />
            </div>
            <span className="text-xl font-bold text-gray-900">AI短剧生成器</span>
          </div>

          {/* 导航菜单 */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link href="/" className="text-gray-900 font-medium hover:text-purple-600 transition-colors">
              首页
            </Link>
            <Link href="/create" className="text-gray-600 hover:text-purple-600 transition-colors">
              创作
            </Link>
            <Link href="/gallery" className="text-gray-600 hover:text-purple-600 transition-colors">
              作品库
            </Link>
            <Link href="/help" className="text-gray-600 hover:text-purple-600 transition-colors">
              帮助
            </Link>
          </nav>

          {/* 用户区域 */}
          <div className="flex items-center space-x-3">
            <UserMenu />
            <Button variant="ghost" size="sm" className="md:hidden">
              <Menu className="w-5 h-5" />
            </Button>
          </div>
        </div>
      </header>

      {/* Hero区域 */}
      <section className="relative pt-16 h-[80vh] min-h-[700px] bg-gradient-to-br from-slate-50 via-purple-50 via-blue-50 to-indigo-100 flex items-center justify-center overflow-hidden">
        {/* 背景装饰 */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-96 h-96 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full mix-blend-multiply filter blur-2xl opacity-60 animate-pulse"></div>
          <div className="absolute -bottom-40 -left-40 w-96 h-96 bg-gradient-to-r from-blue-400 to-cyan-400 rounded-full mix-blend-multiply filter blur-2xl opacity-60 animate-pulse"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-gradient-to-r from-indigo-400 to-purple-400 rounded-full mix-blend-multiply filter blur-3xl opacity-40 animate-pulse"></div>
        </div>

        {/* 网格背景 */}
        <div className="absolute inset-0 opacity-50">
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent"></div>
        </div>

        <div className="container mx-auto px-4 text-center relative z-10">
          <div className="mb-8">
            <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl mb-6 shadow-2xl">
              <Video className="w-10 h-10 text-white" />
            </div>
          </div>

          <h1 className="text-5xl md:text-7xl font-black text-gray-900 mb-6 leading-tight tracking-tight">
            <span className="block">AI短剧生成器</span>
            <span className="block bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-600 bg-clip-text text-transparent">
              让创意瞬间成片
            </span>
          </h1>

          <p className="text-xl md:text-2xl text-gray-600 mb-10 max-w-4xl mx-auto leading-relaxed font-medium">
            只需输入一个想法，AI就能为你生成完整的短剧作品
            <br className="hidden md:block" />
            <span className="text-purple-600 font-semibold">从剧本创作到视频制作，一站式智能解决方案</span>
          </p>

          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-12">
            <Link href="/create">
              <Button
                size="lg"
                className="bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-600 hover:from-purple-700 hover:via-blue-700 hover:to-indigo-700 text-white text-lg px-10 py-6 rounded-full shadow-2xl hover:shadow-3xl transition-all duration-500 transform hover:scale-105 border-0 font-semibold"
              >
                立即开始创作
                <ArrowRight className="ml-3 w-5 h-5" />
              </Button>
            </Link>
            <Button
              variant="outline"
              size="lg"
              className="text-lg px-10 py-6 rounded-full border-2 border-purple-300 hover:border-purple-400 hover:bg-purple-50 transition-all duration-300 bg-white/80 backdrop-blur-sm shadow-lg hover:shadow-xl font-semibold text-purple-700 hover:text-purple-800"
            >
              <Play className="mr-3 w-5 h-5" />
              观看演示视频
            </Button>
          </div>

          {/* 特色标签 */}
          <div className="flex flex-wrap justify-center gap-4 text-sm">
            <Badge className="bg-white/80 backdrop-blur-sm text-purple-700 border-purple-200 px-4 py-2 rounded-full font-medium">
              ✨ 智能剧本生成
            </Badge>
            <Badge className="bg-white/80 backdrop-blur-sm text-blue-700 border-blue-200 px-4 py-2 rounded-full font-medium">
              🎬 自动视频制作
            </Badge>
            <Badge className="bg-white/80 backdrop-blur-sm text-indigo-700 border-indigo-200 px-4 py-2 rounded-full font-medium">
              🎵 智能配音配乐
            </Badge>
          </div>
        </div>
      </section>

      {/* 功能介绍区域 */}
      <section className="py-24 bg-white relative overflow-hidden">
        {/* 背景装饰 */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-1/4 w-72 h-72 bg-purple-100 rounded-full mix-blend-multiply filter blur-xl opacity-70"></div>
          <div className="absolute bottom-0 right-1/4 w-72 h-72 bg-blue-100 rounded-full mix-blend-multiply filter blur-xl opacity-70"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-20">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl mb-6">
              <Video className="w-8 h-8 text-white" />
            </div>
            <h2 className="text-4xl md:text-5xl font-black text-gray-900 mb-6 tracking-tight">
              强大功能，<span className="bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">简单操作</span>
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
              三步完成短剧制作，让AI成为你的创作伙伴，释放无限创意潜能
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-10">
            {/* 卡片1: 智能脚本生成 */}
            <Card className="group hover:shadow-2xl transition-all duration-500 border-0 shadow-lg hover:-translate-y-4 bg-gradient-to-br from-white to-green-50/50 relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-br from-green-400/5 to-green-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <CardContent className="p-10 text-center relative z-10">
                <div className="w-20 h-20 bg-gradient-to-r from-green-400 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-8 group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 shadow-lg">
                  <FileText className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6 group-hover:text-green-600 transition-colors duration-300">AI智能写剧本</h3>
                <p className="text-gray-600 leading-relaxed text-lg">
                  只需输入创意想法，AI自动生成完整剧本，包含精彩对话、详细场景描述和引人入胜的情节发展
                </p>
                <div className="mt-6 flex justify-center">
                  <Badge className="bg-green-100 text-green-700 border-green-200 px-4 py-2 rounded-full font-medium">
                    ⚡ 秒级生成
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* 卡片2: 一键生成视频 */}
            <Card className="group hover:shadow-2xl transition-all duration-500 border-0 shadow-lg hover:-translate-y-4 bg-gradient-to-br from-white to-blue-50/50 relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-400/5 to-blue-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <CardContent className="p-10 text-center relative z-10">
                <div className="w-20 h-20 bg-gradient-to-r from-blue-400 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-8 group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 shadow-lg">
                  <Video className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6 group-hover:text-blue-600 transition-colors duration-300">AI自动成片</h3>
                <p className="text-gray-600 leading-relaxed text-lg">
                  从静态图片到动态视频，全程AI自动化处理，智能剪辑、场景转换，打造流畅观影体验
                </p>
                <div className="mt-6 flex justify-center">
                  <Badge className="bg-blue-100 text-blue-700 border-blue-200 px-4 py-2 rounded-full font-medium">
                    🎬 专业制作
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* 卡片3: 专业后期制作 */}
            <Card className="group hover:shadow-2xl transition-all duration-500 border-0 shadow-lg hover:-translate-y-4 bg-gradient-to-br from-white to-purple-50/50 relative overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-br from-purple-400/5 to-purple-600/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <CardContent className="p-10 text-center relative z-10">
                <div className="w-20 h-20 bg-gradient-to-r from-purple-400 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-8 group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 shadow-lg">
                  <Music className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-6 group-hover:text-purple-600 transition-colors duration-300">AI配音配乐</h3>
                <p className="text-gray-600 leading-relaxed text-lg">
                  智能语音合成，情感丰富的配音效果，背景音乐自动匹配剧情氛围，打造专业级视听盛宴
                </p>
                <div className="mt-6 flex justify-center">
                  <Badge className="bg-purple-100 text-purple-700 border-purple-200 px-4 py-2 rounded-full font-medium">
                    🎵 智能匹配
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 流程指示器 */}
          <div className="mt-20 text-center">
            <div className="flex justify-center items-center space-x-8 max-w-2xl mx-auto">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center text-white font-bold text-sm">1</div>
                <span className="ml-3 text-gray-700 font-medium">输入创意</span>
              </div>
              <ArrowRight className="w-6 h-6 text-gray-400" />
              <div className="flex items-center">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white font-bold text-sm">2</div>
                <span className="ml-3 text-gray-700 font-medium">AI制作</span>
              </div>
              <ArrowRight className="w-6 h-6 text-gray-400" />
              <div className="flex items-center">
                <div className="w-8 h-8 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-sm">3</div>
                <span className="ml-3 text-gray-700 font-medium">完成作品</span>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 底部信息 */}
      <footer className="bg-gradient-to-br from-gray-900 via-slate-900 to-gray-900 text-white py-16 relative overflow-hidden">
        {/* 背景装饰 */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-0 w-96 h-96 bg-purple-600/10 rounded-full mix-blend-multiply filter blur-3xl"></div>
          <div className="absolute bottom-0 right-0 w-96 h-96 bg-blue-600/10 rounded-full mix-blend-multiply filter blur-3xl"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="grid md:grid-cols-4 gap-12">
            <div className="md:col-span-2">
              <div className="flex items-center space-x-4 mb-6">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-600 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg">
                  <Video className="w-6 h-6 text-white" />
                </div>
                <span className="text-2xl font-bold bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">AI短剧生成器</span>
              </div>
              <p className="text-gray-300 leading-relaxed text-lg mb-6 max-w-md">
                让AI成为你的创作伙伴，轻松制作专业短剧作品。从创意到成片，一站式智能解决方案。
              </p>
              <div className="flex space-x-4">
                <Button
                  variant="outline"
                  size="sm"
                  className="border-gray-600 text-gray-300 hover:bg-white/10 hover:border-gray-500 bg-transparent"
                >
                  开始免费试用
                </Button>
              </div>
            </div>

            <div>
              <h4 className="font-bold mb-6 text-lg text-white">产品功能</h4>
              <ul className="space-y-3 text-gray-300">
                <li>
                  <Link href="/features" className="hover:text-purple-400 transition-colors duration-300 flex items-center group">
                    <span className="w-1.5 h-1.5 bg-purple-500 rounded-full mr-3 group-hover:bg-purple-400 transition-colors"></span>
                    功能介绍
                  </Link>
                </li>
                <li>
                  <Link href="/pricing" className="hover:text-purple-400 transition-colors duration-300 flex items-center group">
                    <span className="w-1.5 h-1.5 bg-purple-500 rounded-full mr-3 group-hover:bg-purple-400 transition-colors"></span>
                    价格方案
                  </Link>
                </li>
                <li>
                  <Link href="/templates" className="hover:text-purple-400 transition-colors duration-300 flex items-center group">
                    <span className="w-1.5 h-1.5 bg-purple-500 rounded-full mr-3 group-hover:bg-purple-400 transition-colors"></span>
                    模板库
                  </Link>
                </li>
                <li>
                  <Link href="/gallery" className="hover:text-purple-400 transition-colors duration-300 flex items-center group">
                    <span className="w-1.5 h-1.5 bg-purple-500 rounded-full mr-3 group-hover:bg-purple-400 transition-colors"></span>
                    作品展示
                  </Link>
                </li>
              </ul>
            </div>

            <div>
              <h4 className="font-bold mb-6 text-lg text-white">帮助支持</h4>
              <ul className="space-y-3 text-gray-300">
                <li>
                  <Link href="/help" className="hover:text-blue-400 transition-colors duration-300 flex items-center group">
                    <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-3 group-hover:bg-blue-400 transition-colors"></span>
                    帮助中心
                  </Link>
                </li>
                <li>
                  <Link href="/tutorial" className="hover:text-blue-400 transition-colors duration-300 flex items-center group">
                    <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-3 group-hover:bg-blue-400 transition-colors"></span>
                    使用教程
                  </Link>
                </li>
                <li>
                  <Link href="/contact" className="hover:text-blue-400 transition-colors duration-300 flex items-center group">
                    <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-3 group-hover:bg-blue-400 transition-colors"></span>
                    联系我们
                  </Link>
                </li>
                <li>
                  <Link href="/about" className="hover:text-blue-400 transition-colors duration-300 flex items-center group">
                    <span className="w-1.5 h-1.5 bg-blue-500 rounded-full mr-3 group-hover:bg-blue-400 transition-colors"></span>
                    关于我们
                  </Link>
                </li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800/50 mt-12 pt-8">
            <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
              <div className="text-gray-400 text-sm">
                <p>&copy; 2024 AI短剧生成器. 保留所有权利.</p>
              </div>
              <div className="flex space-x-6 text-sm text-gray-400">
                <Link href="/privacy" className="hover:text-gray-300 transition-colors">
                  隐私政策
                </Link>
                <Link href="/terms" className="hover:text-gray-300 transition-colors">
                  服务条款
                </Link>
                <Link href="/sitemap" className="hover:text-gray-300 transition-colors">
                  网站地图
                </Link>
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
