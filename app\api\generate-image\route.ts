import { NextRequest, NextResponse } from 'next/server'
import { KlingAIClient } from '@/lib/kling-ai'
import { z } from 'zod'
import { uploadImageToMinIO } from '@/lib/minio-client'

const generateImageSchema = z.object({
  prompt: z.string().min(1, "Prompt is required"),
  negativePrompt: z.string().optional(),
  n: z.number().min(1).max(6).default(1),
  aspectRatio: z.enum(['1:1', '3:4', '4:3', '16:9', '9:16']).default('1:1'),
  modelName: z.enum(['kling-v1-5', 'kling-v2']).default('kling-v1-5'),
})

export async function POST(request: NextRequest) {
  console.log('🚀 [API] POST request received')
  
  try {
    // 步骤1: 解析请求体
    console.log('📝 [API] Step 1: Parsing request body...')
    const body = await request.json()
    console.log('📝 [API] Request body:', JSON.stringify(body, null, 2))

    // 步骤2: 验证数据
    console.log('✅ [API] Step 2: Validating data...')
    const validatedData = generateImageSchema.parse(body)
    console.log('✅ [API] Validated data:', JSON.stringify(validatedData, null, 2))

    // 步骤3: 检查环境变量
    console.log('🔑 [API] Step 3: Checking environment variables...')
    const accessKey = process.env.KLING_ACCESS_KEY
    const secretKey = process.env.KLING_SECRET_KEY
    
    console.log('🔑 [API] Access key exists:', !!accessKey)
    console.log('🔑 [API] Secret key exists:', !!secretKey)
    console.log('🔑 [API] Access key length:', accessKey?.length || 0)
    console.log('🔑 [API] Secret key length:', secretKey?.length || 0)
    
    // 显示所有环境变量键名（用于调试）
    console.log('🔑 [API] Available env vars containing "KLING":', 
      Object.keys(process.env).filter(key => key.includes('KLING')))

    if (!accessKey || !secretKey) {
      console.error('❌ [API] Missing API keys')
      return NextResponse.json(
        { error: 'Kling AI API keys not configured' },
        { status: 500 }
      )
    }

    // 步骤4: 初始化客户端
    console.log('🔧 [API] Step 4: Initializing Kling AI client...')
    let klingClient
    try {
      klingClient = new KlingAIClient({ accessKey, secretKey })
      console.log('🔧 [API] Client initialized successfully')
    } catch (clientError) {
      console.error('❌ [API] Client initialization failed:', clientError)
      throw new Error(`Client initialization failed: ${clientError instanceof Error ? clientError.message : 'Unknown error'}`)
    }

    // 步骤5: 生成图片
    console.log('🎨 [API] Step 5: Generating image...')
    console.log('🎨 [API] Generation params:', {
      prompt: validatedData.prompt,
      negativePrompt: validatedData.negativePrompt,
      n: validatedData.n,
      aspectRatio: validatedData.aspectRatio,
      modelName: validatedData.modelName,
    })

    let taskId
    try {
      taskId = await klingClient.generateImage({
        prompt: validatedData.prompt,
        negativePrompt: validatedData.negativePrompt,
        n: validatedData.n,
        aspectRatio: validatedData.aspectRatio,
        modelName: validatedData.modelName,
      })
      console.log('🎨 [API] Task created with ID:', taskId)
    } catch (generateError) {
      console.error('❌ [API] Image generation failed:', generateError)
      throw new Error(`Image generation failed: ${generateError instanceof Error ? generateError.message : 'Unknown error'}`)
    }

    // 步骤6: 轮询任务状态
    console.log('⏳ [API] Step 6: Polling task status...')
    let attempts = 0
    const maxAttempts = 60 // 最多查询60次
    
    const pollTaskStatus = async (): Promise<any> => {
      attempts++
      console.log(`⏳ [API] Polling attempt ${attempts}/${maxAttempts}`)
      
      try {
        const status = await klingClient.getTaskStatus(taskId)
        console.log(`⏳ [API] Task status:`, status.data.task_status)
        
        if (status.data.task_status === 'succeed') {
          console.log('✅ [API] Task completed successfully')
          console.log('✅ [API] Result:', JSON.stringify(status.data.task_result, null, 2))
          return status
        } else if (status.data.task_status === 'failed') {
          console.error('❌ [API] Task failed')
          throw new Error('Image generation failed')
        } else if (attempts >= maxAttempts) {
          console.error('❌ [API] Task timeout')
          throw new Error('Image generation timeout')
        }
        
        console.log(`⏳ [API] Waiting 10 seconds before next poll...`)
        await new Promise(resolve => setTimeout(resolve, 10000))
        return pollTaskStatus()
      } catch (pollError) {
        console.error('❌ [API] Polling error:', pollError)
        throw pollError
      }
    }

    const finalStatus = await pollTaskStatus()

    console.log('🎉 [API] Success! Uploading to MinIO...')
    
    // Upload generated images to MinIO
    const uploadedImages = []
    
    for (const image of finalStatus.data.task_result.images) {
      try {
        console.log(`📤 [API] Uploading image: ${image.url}`)
        
        // Download image from Kling AI
        const imageResponse = await fetch(image.url)
        if (!imageResponse.ok) {
          throw new Error(`Failed to download image: ${imageResponse.statusText}`)
        }
        
        const imageBuffer = Buffer.from(await imageResponse.arrayBuffer())
        
        // Upload to MinIO
        const minioUrl = await uploadImageToMinIO(
          imageBuffer,
          `image-${Date.now()}.jpg`,
          'image/jpeg'
        )
        
        console.log(`✅ [API] Successfully uploaded to MinIO: ${minioUrl}`)
        uploadedImages.push({
          url: minioUrl,
          originalUrl: image.url,
          width: image.width,
          height: image.height,
        })
      } catch (uploadError) {
        console.error(`❌ [API] Failed to upload image: ${uploadError}`)
        // Fallback to original URL if upload fails
        uploadedImages.push({
          url: image.url,
          originalUrl: image.url,
          width: image.width,
          height: image.height,
        })
      }
    }

    console.log('🎉 [API] All images uploaded to MinIO successfully')
    return NextResponse.json({
      success: true,
      images: uploadedImages,
      taskId,
    })

  } catch (error) {
    console.error('💥 [API] Error in POST handler:', error)
    console.error('💥 [API] Error stack:', error instanceof Error ? error.stack : 'No stack trace')
    
    if (error instanceof z.ZodError) {
      console.error('💥 [API] Zod validation error:', error.errors)
      return NextResponse.json(
        { error: 'Invalid request data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : 'Image generation failed',
        stack: process.env.NODE_ENV === 'development' && error instanceof Error ? error.stack : undefined
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  console.log('🔍 [API] GET request received - Health check')
  
  // 环境变量检查
  const accessKey = process.env.KLING_ACCESS_KEY
  const secretKey = process.env.KLING_SECRET_KEY
  
  console.log('🔍 [API] Health check - Access key exists:', !!accessKey)
  console.log('🔍 [API] Health check - Secret key exists:', !!secretKey)
  
  return NextResponse.json(
    { 
      message: 'Image generation API is working',
      hasAccessKey: !!accessKey,
      hasSecretKey: !!secretKey,
      timestamp: new Date().toISOString()
    },
    { status: 200 }
  )
}
