"use client"

import { useState, use<PERSON>em<PERSON>, useEffect } from "react"
import { useAuth } from "@/contexts/AuthContext"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { AuthForm } from "@/components/auth/AuthForm"
import {
  Search,
  Plus,
  MoreVertical,
  Edit,
  Trash2,
  Share2,
  Play,
  Clock,
  Calendar,
  Filter,
  Grid3X3,
  List,
  ArrowLeft,
  Video,
  FileText,
  ImageIcon,
  Music,
  Loader2,
} from "lucide-react"
import Link from "next/link"
import Image from "next/image"

interface Project {
  id: string
  title: string
  description: string
  thumbnail: string
  status: "completed" | "in-progress" | "draft"
  createdAt: string
  updatedAt: string
  duration: string
  scenes: number
  currentStep: number
  totalSteps: number
}

const statusConfig = {
  completed: { label: "已完成", color: "bg-green-100 text-green-800", variant: "secondary" as const },
  "in-progress": { label: "进行中", color: "bg-blue-100 text-blue-800", variant: "default" as const },
  draft: { label: "草稿", color: "bg-gray-100 text-gray-800", variant: "outline" as const },
}

export default function ProjectsPage() {
  const { user, loading: authLoading } = useAuth()
  const [projects, setProjects] = useState<Project[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [sortBy, setSortBy] = useState("updatedAt")
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [deleteProjectId, setDeleteProjectId] = useState<string | null>(null)
  const [showAuthDialog, setShowAuthDialog] = useState(false)

  // 测试登录函数
  const handleTestLogin = async () => {
    try {
      const response = await fetch('/api/test-login', {
        method: 'POST'
      })
      const data = await response.json()

      if (data.success) {
        localStorage.setItem('auth_token', data.token)
        window.location.reload() // 简单刷新页面来更新认证状态
      } else {
        alert('测试登录失败: ' + data.error)
      }
    } catch (error) {
      console.error('Test login error:', error)
      alert('测试登录时发生错误')
    }
  }

  // 获取项目数据
  useEffect(() => {
    const fetchProjects = async () => {
      if (!user?.id) {
        setLoading(false)
        return
      }

      try {
        const response = await fetch(`/api/projects?userId=${user.id}`)
        const data = await response.json()

        if (data.success) {
          setProjects(data.projects)
        } else {
          console.error('Failed to fetch projects:', data.error)
        }
      } catch (error) {
        console.error('Error fetching projects:', error)
      } finally {
        setLoading(false)
      }
    }

    if (!authLoading) {
      fetchProjects()
    }
  }, [user?.id, authLoading])

  // 筛选和排序项目
  const filteredProjects = useMemo(() => {
    const filtered = projects.filter((project: Project) => {
      const matchesSearch =
        project.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        project.description.toLowerCase().includes(searchQuery.toLowerCase())
      const matchesStatus = statusFilter === "all" || project.status === statusFilter
      return matchesSearch && matchesStatus
    })

    // 排序
    filtered.sort((a: Project, b: Project) => {
      if (sortBy === "createdAt") {
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      } else {
        return new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
      }
    })

    return filtered
  }, [projects, searchQuery, statusFilter, sortBy])

  const handleDeleteProject = async (projectId: string) => {
    try {
      const response = await fetch(`/api/projects/${projectId}`, {
        method: 'DELETE'
      })

      const data = await response.json()

      if (data.success) {
        // 从本地状态中移除已删除的项目
        setProjects(prev => prev.filter(p => p.id !== projectId))
        setDeleteProjectId(null)
      } else {
        console.error('Failed to delete project:', data.error)
        alert('删除项目失败: ' + data.error)
      }
    } catch (error) {
      console.error('Error deleting project:', error)
      alert('删除项目时发生错误')
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("zh-CN", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  const getStepIcon = (step: number) => {
    const icons = [FileText, ImageIcon, Video, Music, Edit, Video, FileText]
    const Icon = icons[step - 1] || FileText
    return Icon
  }

  // 如果用户未登录，显示登录提示
  if (!authLoading && !user) {
    return (
      <>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <Video className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">请先登录</h3>
            <p className="text-gray-500 mb-6">登录后即可查看您的作品库</p>
            <div className="space-x-4">
              <Button
                className="bg-blue-600 hover:bg-blue-700"
                onClick={() => setShowAuthDialog(true)}
              >
                立即登录
              </Button>
              <Button
                variant="outline"
                onClick={handleTestLogin}
              >
                测试登录
              </Button>
            </div>
          </div>
        </div>

        <Dialog open={showAuthDialog} onOpenChange={setShowAuthDialog}>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>欢迎</DialogTitle>
              <DialogDescription>
                登录或注册账号开始使用AI短剧生成器
              </DialogDescription>
            </DialogHeader>
            <AuthForm onSuccess={() => setShowAuthDialog(false)} />
          </DialogContent>
        </Dialog>
      </>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/50">
      {/* 顶部导航 */}
      <header className="bg-white/80 backdrop-blur-xl border-b border-slate-200/60 shadow-sm">
        <div className="container mx-auto px-6 py-5">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-6">
              <Link href="/" className="group flex items-center text-slate-600 hover:text-slate-900 transition-all duration-300 hover:scale-105">
                <ArrowLeft className="w-5 h-5 mr-2 group-hover:-translate-x-1 transition-transform duration-300" />
                <span className="font-medium">返回首页</span>
              </Link>
              <div className="w-px h-6 bg-slate-300"></div>
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                  <Video className="w-5 h-5 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold bg-gradient-to-r from-slate-800 via-blue-700 to-purple-700 bg-clip-text text-transparent">
                    我的作品库
                  </h1>
                  <p className="text-sm text-slate-600">管理您的AI短剧创作</p>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setViewMode(viewMode === "grid" ? "list" : "grid")}
                className="group bg-white/70 border-slate-300 hover:border-slate-400 hover:bg-white hover:shadow-md transition-all duration-300"
              >
                {viewMode === "grid" ? (
                  <List className="w-4 h-4 group-hover:scale-110 transition-transform duration-300" />
                ) : (
                  <Grid3X3 className="w-4 h-4 group-hover:scale-110 transition-transform duration-300" />
                )}
                <span className="ml-2 font-medium">
                  {viewMode === "grid" ? "列表视图" : "网格视图"}
                </span>
              </Button>
              <Link href="/create">
                <Button className="group bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 px-6 py-2.5">
                  <Plus className="w-4 h-4 mr-2 group-hover:rotate-90 transition-transform duration-300" />
                  新建项目
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-6 py-8">
        {/* 顶部筛选栏 */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-slate-200/60 p-8 mb-8 shadow-lg">
          <div className="flex flex-col lg:flex-row gap-6 items-start lg:items-center justify-between">
            <div className="flex flex-col sm:flex-row gap-4 flex-1">
              {/* 搜索框 */}
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-slate-400 w-5 h-5" />
                <Input
                  placeholder="搜索项目名称或描述..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-12 h-12 bg-white/70 border-slate-300 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 text-base font-medium placeholder:text-slate-400 transition-all duration-300"
                />
              </div>

              {/* 状态筛选 */}
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-44 h-12 bg-white/70 border-slate-300 hover:border-slate-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-300">
                  <Filter className="w-4 h-4 mr-2 text-slate-500" />
                  <SelectValue className="font-medium" />
                </SelectTrigger>
                <SelectContent className="bg-white/95 backdrop-blur-sm border border-slate-200 shadow-xl rounded-xl">
                  <SelectItem value="all" className="font-medium">全部状态</SelectItem>
                  <SelectItem value="completed" className="font-medium">已完成</SelectItem>
                  <SelectItem value="in-progress" className="font-medium">进行中</SelectItem>
                  <SelectItem value="draft" className="font-medium">草稿</SelectItem>
                </SelectContent>
              </Select>

              {/* 排序方式 */}
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-44 h-12 bg-white/70 border-slate-300 hover:border-slate-400 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-300">
                  <Clock className="w-4 h-4 mr-2 text-slate-500" />
                  <SelectValue className="font-medium" />
                </SelectTrigger>
                <SelectContent className="bg-white/95 backdrop-blur-sm border border-slate-200 shadow-xl rounded-xl">
                  <SelectItem value="updatedAt" className="font-medium">修改时间</SelectItem>
                  <SelectItem value="createdAt" className="font-medium">创建时间</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* 统计信息 */}
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-2 bg-gradient-to-r from-blue-50 to-indigo-50 px-4 py-2 rounded-xl border border-blue-200">
                <div className="w-2 h-2 rounded-full bg-blue-500"></div>
                <span className="text-sm font-semibold text-blue-700">
                  共 {filteredProjects.length} 个项目
                </span>
              </div>
              <div className="flex items-center space-x-2 bg-gradient-to-r from-emerald-50 to-green-50 px-4 py-2 rounded-xl border border-emerald-200">
                <div className="w-2 h-2 rounded-full bg-emerald-500"></div>
                <span className="text-sm font-semibold text-emerald-700">
                  已完成 {projects.filter((p: Project) => p.status === "completed").length} 个
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* 作品网格/列表 */}
        {loading || authLoading ? (
          <div className="flex flex-col items-center justify-center py-20">
            <div className="w-16 h-16 rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center mb-6 animate-pulse">
              <Loader2 className="w-8 h-8 animate-spin text-white" />
            </div>
            <h3 className="text-lg font-semibold text-slate-700 mb-2">加载中...</h3>
            <p className="text-slate-500">正在获取您的作品数据</p>
          </div>
        ) : filteredProjects.length === 0 ? (
          <div className="text-center py-20">
            <div className="w-24 h-24 rounded-full bg-gradient-to-br from-slate-100 to-gray-200 flex items-center justify-center mx-auto mb-6">
              <Video className="w-12 h-12 text-slate-400" />
            </div>
            <h3 className="text-xl font-bold text-slate-800 mb-3">
              {searchQuery || statusFilter !== "all" ? "未找到匹配项目" : "暂无项目"}
            </h3>
            <p className="text-slate-600 mb-8 max-w-md mx-auto leading-relaxed">
              {searchQuery || statusFilter !== "all"
                ? "尝试调整搜索条件或筛选器，或者创建一个新项目开始您的创作之旅"
                : "开始创建您的第一个AI短剧作品，让创意在这里绽放"}
            </p>
            <Link href="/create">
              <Button className="group bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 px-8 py-3">
                <Plus className="w-5 h-5 mr-2 group-hover:rotate-90 transition-transform duration-300" />
                开始创作
              </Button>
            </Link>
          </div>
        ) : (
          <div
            className={
              viewMode === "grid" ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6" : "space-y-4"
            }
          >
            {filteredProjects.map((project) => (
              <ProjectCard
                key={project.id}
                project={project}
                viewMode={viewMode}
                onDelete={setDeleteProjectId}
                formatDate={formatDate}
                getStepIcon={getStepIcon}
              />
            ))}
          </div>
        )}
      </div>

      {/* 删除确认对话框 */}
      <AlertDialog open={deleteProjectId !== null} onOpenChange={() => setDeleteProjectId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>确认删除项目</AlertDialogTitle>
            <AlertDialogDescription>
              此操作将永久删除该项目及其所有相关数据，无法恢复。确定要继续吗？
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>取消</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => deleteProjectId && handleDeleteProject(deleteProjectId)}
              className="bg-red-600 hover:bg-red-700"
            >
              删除
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}

// 项目卡片组件
function ProjectCard({
  project,
  viewMode,
  onDelete,
  formatDate,
  getStepIcon,
}: {
  project: Project
  viewMode: "grid" | "list"
  onDelete: (id: string) => void
  formatDate: (date: string) => string
  getStepIcon: (step: number) => React.ComponentType<{ className?: string }>
}) {
  const statusInfo = statusConfig[project.status]
  const progress = (project.currentStep / project.totalSteps) * 100
  const StepIcon = getStepIcon(project.currentStep)

  if (viewMode === "list") {
    return (
      <Card className="hover:shadow-md hover:border-blue-300 hover:bg-blue-50/20 transition-all duration-200 group/card">
        <CardContent className="p-6">
          <div className="flex items-center space-x-6">
            {/* 缩略图 - 可点击编辑 */}
            <Link href={`/create?project=${project.id}`} className="block flex-shrink-0">
              <div className="w-32 h-20 bg-gray-200 rounded-lg overflow-hidden relative group/thumbnail cursor-pointer">
                <Image
                  src={project.thumbnail || "/placeholder.svg"}
                  alt={project.title}
                  width={128}
                  height={80}
                  className="w-full h-full object-cover group-hover/thumbnail:scale-105 transition-transform duration-300"
                />
                {/* 编辑覆盖层 */}
                <div className="absolute inset-0 bg-black/0 group-hover/thumbnail:bg-black/50 transition-all duration-300 flex items-center justify-center">
                  <div className="opacity-0 group-hover/thumbnail:opacity-100 transition-opacity duration-300">
                    <Edit className="w-5 h-5 text-white" />
                  </div>
                </div>
              </div>
            </Link>

            {/* 项目信息 */}
            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <Link href={`/create?project=${project.id}`}>
                    <h3 className="text-lg font-semibold text-gray-900 hover:text-blue-600 truncate cursor-pointer transition-colors duration-200">
                      {project.title}
                    </h3>
                  </Link>
                  <p className="text-gray-600 text-sm mt-1 line-clamp-2">{project.description}</p>

                  <div className="flex items-center space-x-4 mt-3 text-sm text-gray-500">
                    <div className="flex items-center">
                      <Calendar className="w-4 h-4 mr-1" />
                      {formatDate(project.updatedAt)}
                    </div>
                    <div className="flex items-center">
                      <Clock className="w-4 h-4 mr-1" />
                      {project.duration}
                    </div>
                    <div className="flex items-center">
                      <StepIcon className="w-4 h-4 mr-1" />
                      步骤 {project.currentStep}/{project.totalSteps}
                    </div>
                  </div>
                </div>

                <div className="flex items-center space-x-3 ml-4">
                  <Badge variant={statusInfo.variant} className={statusInfo.color}>
                    {statusInfo.label}
                  </Badge>
                  <ProjectActions project={project} onDelete={onDelete} variant="list" />
                </div>
              </div>

              {/* 进度条 */}
              {project.status === "in-progress" && (
                <div className="mt-3">
                  <div className="flex justify-between text-xs text-gray-500 mb-1">
                    <span>创作进度</span>
                    <span>{Math.round(progress)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${progress}%` }}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="group hover:shadow-2xl transition-all duration-300 hover:-translate-y-2 bg-white/80 backdrop-blur-sm border-slate-200/60 overflow-hidden hover:border-blue-300 hover:bg-blue-50/30">
      <div className="relative">
        {/* 缩略图 - 可点击编辑 */}
        <Link href={`/create?project=${project.id}`} className="block">
          <div className="aspect-video bg-gradient-to-br from-slate-200 to-gray-300 rounded-t-xl overflow-hidden cursor-pointer">
            <Image
              src={project.thumbnail || "/placeholder.svg"}
              alt={project.title}
              width={300}
              height={200}
              className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
            />

            {/* 编辑覆盖层 */}
            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/40 transition-all duration-300 flex items-center justify-center">
              <div className="opacity-0 group-hover:opacity-100 transition-all duration-300 flex flex-col items-center space-y-2">
                {project.status === "completed" ? (
                  <>
                    <div className="w-16 h-16 bg-white/95 backdrop-blur-sm rounded-full flex items-center justify-center hover:scale-110 shadow-xl transition-transform duration-200">
                      <Play className="w-6 h-6 text-slate-800 ml-1" />
                    </div>
                    <span className="text-white text-sm font-medium bg-black/50 px-3 py-1 rounded-full backdrop-blur-sm">
                      点击播放或编辑
                    </span>
                  </>
                ) : (
                  <>
                    <div className="w-16 h-16 bg-white/95 backdrop-blur-sm rounded-full flex items-center justify-center hover:scale-110 shadow-xl transition-transform duration-200">
                      <Edit className="w-6 h-6 text-slate-800" />
                    </div>
                    <span className="text-white text-sm font-medium bg-black/50 px-3 py-1 rounded-full backdrop-blur-sm">
                      点击继续编辑
                    </span>
                  </>
                )}
              </div>
            </div>
          </div>
        </Link>

        {/* 状态标签 */}
        <div className="absolute top-4 left-4">
          <Badge
            variant={statusInfo.variant}
            className={`${statusInfo.color} font-semibold px-3 py-1.5 shadow-lg backdrop-blur-sm border-0`}
          >
            {statusInfo.label}
          </Badge>
        </div>

        {/* 操作菜单 */}
        <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-300">
          <ProjectActions project={project} onDelete={onDelete} variant="overlay" />
        </div>
      </div>

        <CardContent className="p-6">
          <div className="space-y-4">
            {/* 标题和描述 - 可点击编辑 */}
            <div>
              <Link href={`/create?project=${project.id}`}>
                <h3 className="font-bold text-slate-800 truncate group-hover:text-blue-600 hover:text-blue-700 transition-colors duration-300 text-lg cursor-pointer">
                  {project.title}
                </h3>
              </Link>
              <p className="text-sm text-slate-600 line-clamp-2 mt-2 leading-relaxed">{project.description}</p>
            </div>

            {/* 项目信息 */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-1 bg-slate-50 px-3 py-1.5 rounded-lg">
                <Calendar className="w-3 h-3 text-slate-500" />
                <span className="text-xs font-medium text-slate-600">{formatDate(project.updatedAt)}</span>
              </div>
              <div className="flex items-center space-x-1 bg-slate-50 px-3 py-1.5 rounded-lg">
                <Clock className="w-3 h-3 text-slate-500" />
                <span className="text-xs font-medium text-slate-600">{project.duration}</span>
              </div>
            </div>

            {/* 进度信息 */}
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-1 bg-blue-50 px-3 py-1.5 rounded-lg">
                <StepIcon className="w-3 h-3 text-blue-600" />
                <span className="text-xs font-semibold text-blue-700">
                  步骤 {project.currentStep}/{project.totalSteps}
                </span>
              </div>
              <div className="flex items-center space-x-1 bg-purple-50 px-3 py-1.5 rounded-lg">
                <Video className="w-3 h-3 text-purple-600" />
                <span className="text-xs font-semibold text-purple-700">{project.scenes} 个场景</span>
              </div>
            </div>

            {/* 进度条 */}
            {project.status === "in-progress" && (
              <div className="space-y-2">
                <div className="w-full bg-slate-200 rounded-full h-2 overflow-hidden">
                  <div
                    className="bg-gradient-to-r from-blue-500 to-indigo-600 h-2 rounded-full transition-all duration-500 shadow-sm"
                    style={{ width: `${progress}%` }}
                  />
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-xs text-slate-500 font-medium">创作进度</span>
                  <span className="text-xs font-bold text-blue-600 bg-blue-50 px-2 py-0.5 rounded-full">
                    {Math.round(progress)}% 完成
                  </span>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </div>
    </Card>
  )
}

// 项目操作菜单组件
function ProjectActions({
  project,
  onDelete,
  variant = "default"
}: {
  project: Project;
  onDelete: (id: string) => void;
  variant?: "default" | "overlay" | "list";
}) {
  // 根据variant调整按钮样式
  const getButtonStyles = () => {
    switch (variant) {
      case "overlay":
        return "h-8 w-8 p-0 bg-black/20 backdrop-blur-sm hover:bg-black/40 border-0 text-white hover:text-white shadow-lg hover:shadow-xl transition-all duration-200 rounded-lg group"
      case "list":
        return "h-8 w-8 p-0 bg-gray-50 hover:bg-gray-100 border border-gray-200 hover:border-gray-300 text-gray-600 hover:text-gray-900 shadow-sm hover:shadow-md transition-all duration-200 rounded-lg group"
      default:
        return "h-9 w-9 p-0 bg-white/95 backdrop-blur-sm hover:bg-white border border-white/20 hover:border-gray-200 text-gray-600 hover:text-gray-900 shadow-sm hover:shadow-md transition-all duration-200 rounded-lg group"
    }
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className={getButtonStyles()}
        >
          <MoreVertical className="w-4 h-4 group-hover:scale-110 transition-transform duration-200" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        align="end"
        className="w-56 bg-white/95 backdrop-blur-sm border border-gray-200/80 shadow-xl rounded-xl p-2 animate-in fade-in-0 zoom-in-95 duration-200"
        sideOffset={8}
      >
        {/* 编辑项目 */}
        <DropdownMenuItem asChild>
          <Link
            href={`/create?project=${project.id}`}
            className="flex items-center px-3 py-2.5 rounded-lg hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 hover:text-blue-700 transition-all duration-200 group cursor-pointer"
          >
            <div className="w-8 h-8 rounded-lg bg-blue-100 group-hover:bg-blue-200 flex items-center justify-center mr-3 transition-colors duration-200">
              <Edit className="w-4 h-4 text-blue-600" />
            </div>
            <div className="flex-1">
              <div className="font-medium text-sm">编辑项目</div>
              <div className="text-xs text-gray-500 group-hover:text-blue-600/70">继续创作和修改</div>
            </div>
          </Link>
        </DropdownMenuItem>

        {project.status === "completed" && (
          <>
            {/* 预览播放 */}
            <DropdownMenuItem className="flex items-center px-3 py-2.5 rounded-lg hover:bg-gradient-to-r hover:from-green-50 hover:to-emerald-50 hover:text-green-700 transition-all duration-200 group cursor-pointer">
              <div className="w-8 h-8 rounded-lg bg-green-100 group-hover:bg-green-200 flex items-center justify-center mr-3 transition-colors duration-200">
                <Play className="w-4 h-4 text-green-600 ml-0.5" />
              </div>
              <div className="flex-1">
                <div className="font-medium text-sm">预览播放</div>
                <div className="text-xs text-gray-500 group-hover:text-green-600/70">观看完整作品</div>
              </div>
            </DropdownMenuItem>

            {/* 分享项目 */}
            <DropdownMenuItem className="flex items-center px-3 py-2.5 rounded-lg hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50 hover:text-purple-700 transition-all duration-200 group cursor-pointer">
              <div className="w-8 h-8 rounded-lg bg-purple-100 group-hover:bg-purple-200 flex items-center justify-center mr-3 transition-colors duration-200">
                <Share2 className="w-4 h-4 text-purple-600" />
              </div>
              <div className="flex-1">
                <div className="font-medium text-sm">分享项目</div>
                <div className="text-xs text-gray-500 group-hover:text-purple-600/70">分享给朋友观看</div>
              </div>
            </DropdownMenuItem>
          </>
        )}

        <DropdownMenuSeparator className="my-2 bg-gradient-to-r from-transparent via-gray-200 to-transparent" />

        {/* 删除项目 */}
        <DropdownMenuItem
          onClick={() => onDelete(project.id)}
          className="flex items-center px-3 py-2.5 rounded-lg hover:bg-gradient-to-r hover:from-red-50 hover:to-pink-50 hover:text-red-700 transition-all duration-200 group cursor-pointer"
        >
          <div className="w-8 h-8 rounded-lg bg-red-100 group-hover:bg-red-200 flex items-center justify-center mr-3 transition-colors duration-200">
            <Trash2 className="w-4 h-4 text-red-600" />
          </div>
          <div className="flex-1">
            <div className="font-medium text-sm">删除项目</div>
            <div className="text-xs text-gray-500 group-hover:text-red-600/70">永久删除此项目</div>
          </div>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
