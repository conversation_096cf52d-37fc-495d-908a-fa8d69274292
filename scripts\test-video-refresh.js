#!/usr/bin/env node

/**
 * 测试视频刷新功能
 *
 * 这个脚本用于测试新的视频刷新功能，包括：
 * 1. 数据库连接测试
 * 2. 视频生成任务表查询
 * 3. Kling API状态查询
 * 4. 数据库和API的集成测试
 *
 * 使用方法：
 * - 运行完整测试: node scripts/test-video-refresh.js
 * - 只显示视频任务: node scripts/test-video-refresh.js --tasks-only
 * - 简写形式: node scripts/test-video-refresh.js -t
 */

const { PrismaClient } = require('../lib/generated/prisma');

async function main() {
  // 检查命令行参数
  const args = process.argv.slice(2);
  const showOnlyTasks = args.includes('--tasks-only') || args.includes('-t');

  if (showOnlyTasks) {
    console.log('🎬 显示视频生成任务');
    console.log('='.repeat(50));
    await displayAllVideoTasks();
    return;
  }

  console.log('🎬 视频刷新功能测试');
  console.log('='.repeat(50));

  await testDatabaseConnection();
  await displayAllVideoTasks();
  await testVideoTables();
  await testVideoTaskOperations();
  await testKlingAPIIntegration();

  console.log('\n✅ 测试完成！');
}

// 专门显示所有视频任务的函数
async function displayAllVideoTasks() {
  console.log('\n📋 显示所有视频生成任务');
  console.log('='.repeat(50));

  try {
    const prisma = new PrismaClient();
    await prisma.$connect();

    const videoTaskCount = await prisma.video_generation_tasks.count();
    console.log(`📊 总共有 ${videoTaskCount} 条视频生成任务记录\n`);

    if (videoTaskCount === 0) {
      console.log('📋 video_generation_tasks 表为空');
      await prisma.$disconnect();
      return;
    }

    const allVideoTasks = await prisma.video_generation_tasks.findMany({
      include: {
        generated_videos: true,
        script_projects: {
          select: {
            id: true,
            title: true
          }
        },
        script_shots: {
          select: {
            id: true,
            shot_number: true,
            location: true
          }
        },
        users: {
          select: {
            id: true,
            email: true,
            username: true
          }
        }
      },
      orderBy: {
        created_at: 'desc'
      }
    });

    console.log('📋 video_generation_tasks 表详细内容:');
    console.log('='.repeat(80));

    allVideoTasks.forEach((task, index) => {
      console.log(`\n🎬 任务 ${index + 1}/${videoTaskCount}:`);
      console.log(`   📝 ID: ${task.id}`);
      console.log(`   🏷️  任务名称: ${task.task_name || '未设置'}`);
      console.log(`   💬 提示词: ${task.prompt.length > 80 ? task.prompt.substring(0, 80) + '...' : task.prompt}`);
      if (task.negative_prompt) {
        console.log(`   🚫 负面提示词: ${task.negative_prompt.length > 60 ? task.negative_prompt.substring(0, 60) + '...' : task.negative_prompt}`);
      }
      console.log(`   🤖 模型: ${task.model_name}`);
      console.log(`   📐 宽高比: ${task.aspect_ratio}`);
      console.log(`   ⏱️  时长: ${task.duration}秒`);
      console.log(`   🖼️  图片参考: ${task.image_reference}`);
      console.log(`   🎯 图片保真度: ${task.image_fidelity}`);
      if (task.base_image_url) {
        console.log(`   🔗 基础图片URL: ${task.base_image_url.length > 60 ? task.base_image_url.substring(0, 60) + '...' : task.base_image_url}`);
      }
      console.log(`   📊 状态: ${getStatusEmoji(task.status)} ${task.status}`);
      console.log(`   🆔 Kling任务ID: ${task.kling_task_id || '无'}`);
      if (task.error_message) {
        console.log(`   ❌ 错误信息: ${task.error_message}`);
      }
      if (task.error_code) {
        console.log(`   🔢 错误代码: ${task.error_code}`);
      }
      console.log(`   📅 创建时间: ${formatDate(task.created_at)}`);
      if (task.started_at) {
        console.log(`   🚀 开始时间: ${formatDate(task.started_at)}`);
      }
      if (task.completed_at) {
        console.log(`   ✅ 完成时间: ${formatDate(task.completed_at)}`);
      }
      console.log(`   🔄 更新时间: ${formatDate(task.updated_at)}`);

      // 关联信息
      if (task.script_projects) {
        console.log(`   📁 关联项目: "${task.script_projects.title}"`);
      }
      if (task.script_shots) {
        console.log(`   🎬 关联镜头: 镜头${task.script_shots.shot_number} - ${task.script_shots.location}`);
      }
      if (task.users) {
        console.log(`   👤 用户: ${task.users.username || task.users.email}`);
      }

      // 生成的视频
      if (task.generated_videos && task.generated_videos.length > 0) {
        console.log(`   🎥 生成的视频 (${task.generated_videos.length}个):`);
        task.generated_videos.forEach((video, videoIndex) => {
          console.log(`     📹 视频${videoIndex + 1}:`);
          console.log(`        🔗 URL: ${video.video_url}`);
          if (video.video_filename) {
            console.log(`        📄 文件名: ${video.video_filename}`);
          }
          console.log(`        📱 格式: ${video.video_format}`);
          if (video.video_duration) {
            console.log(`        ⏱️  时长: ${video.video_duration}秒`);
          }
          console.log(`        💾 存储: ${video.storage_provider}`);
          console.log(`        📅 创建: ${formatDate(video.created_at)}`);
        });
      } else {
        console.log(`   🎥 生成的视频: 无`);
      }

      console.log('   ' + '─'.repeat(70));
    });

    // 统计信息
    console.log(`\n📊 统计信息:`);
    const statusCounts = {};
    allVideoTasks.forEach(task => {
      statusCounts[task.status] = (statusCounts[task.status] || 0) + 1;
    });

    Object.entries(statusCounts).forEach(([status, count]) => {
      console.log(`   ${getStatusEmoji(status)} ${status}: ${count} 个任务`);
    });

    const totalVideos = allVideoTasks.reduce((sum, task) => sum + (task.generated_videos?.length || 0), 0);
    console.log(`   🎥 总生成视频数: ${totalVideos} 个`);

    await prisma.$disconnect();

  } catch (error) {
    console.log(`❌ 显示视频任务失败: ${error.message}`);
    if (error.message.includes('video_generation_tasks')) {
      console.log('💡 提示: 请先运行 scripts/create-video-tables.sql 创建视频表');
    }
  }
}

// 辅助函数：获取状态表情符号
function getStatusEmoji(status) {
  const statusEmojis = {
    'pending': '⏳',
    'queued': '📋',
    'processing': '🔄',
    'completed': '✅',
    'failed': '❌',
    'cancelled': '🚫'
  };
  return statusEmojis[status] || '❓';
}

// 辅助函数：格式化日期
function formatDate(date) {
  if (!date) return '未设置';
  return new Date(date).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });
}

// 1. 数据库连接测试
async function testDatabaseConnection() {
  console.log('\n🗄️  1. 数据库连接测试');
  console.log('-'.repeat(40));
  
  try {
    const prisma = new PrismaClient();
    await prisma.$connect();
    console.log('✅ 数据库连接成功');
    
    // 检查视频相关表
    const videoTaskCount = await prisma.video_generation_tasks.count();
    const videoCount = await prisma.generated_videos.count();

    console.log(`📊 视频生成任务: ${videoTaskCount} 条记录`);
    console.log(`📊 生成视频: ${videoCount} 条记录`);
    
    await prisma.$disconnect();
    
  } catch (error) {
    console.log(`❌ 数据库连接失败: ${error.message}`);
    if (error.message.includes('video_generation_tasks')) {
      console.log('💡 提示: 请先运行 scripts/create-video-tables.sql 创建视频表');
    }
  }
}

// 2. 视频表结构测试
async function testVideoTables() {
  console.log('\n📋 2. 视频表结构测试');
  console.log('-'.repeat(40));
  
  try {
    const prisma = new PrismaClient();
    await prisma.$connect();
    
    // 测试创建视频任务
    console.log('🔧 测试创建视频任务...');
    const testTask = await prisma.video_generation_tasks.create({
      data: {
        prompt: '测试视频生成提示词',
        model_name: 'kling-v1',
        aspect_ratio: '16:9',
        duration: 5,
        status: 'pending',
        kling_task_id: `test_${Date.now()}`,
      }
    });
    console.log(`✅ 创建测试任务成功: ${testTask.id}`);
    
    // 测试查询任务
    console.log('🔍 测试查询任务...');
    const foundTask = await prisma.video_generation_tasks.findUnique({
      where: { id: testTask.id }
    });
    console.log(`✅ 查询任务成功: ${foundTask ? '找到' : '未找到'}`);
    
    // 测试更新任务状态
    console.log('🔄 测试更新任务状态...');
    const updatedTask = await prisma.video_generation_tasks.update({
      where: { id: testTask.id },
      data: { 
        status: 'completed',
        completed_at: new Date()
      }
    });
    console.log(`✅ 更新任务状态成功: ${updatedTask.status}`);
    
    // 测试创建视频记录
    console.log('🎥 测试创建视频记录...');
    const testVideo = await prisma.generated_videos.create({
      data: {
        task_id: testTask.id,
        video_url: 'https://example.com/test-video.mp4',
        video_format: 'mp4',
        video_duration: 5.0,
      }
    });
    console.log(`✅ 创建视频记录成功: ${testVideo.id}`);
    
    // 清理测试数据
    console.log('🧹 清理测试数据...');
    await prisma.generated_videos.delete({ where: { id: testVideo.id } });
    await prisma.video_generation_tasks.delete({ where: { id: testTask.id } });
    console.log('✅ 测试数据清理完成');
    
    await prisma.$disconnect();
    
  } catch (error) {
    console.log(`❌ 视频表测试失败: ${error.message}`);
  }
}

// 3. 视频任务操作测试
async function testVideoTaskOperations() {
  console.log('\n⚙️  3. 视频任务操作测试');
  console.log('-'.repeat(40));
  
  try {
    // 测试导入视频操作函数
    const { 
      createVideoGenerationTask, 
      updateVideoGenerationTask,
      getVideoGenerationTaskByKlingId,
      createGeneratedVideo 
    } = require('../lib/prisma-video-operations');
    
    console.log('🔧 测试创建视频任务...');
    const task = await createVideoGenerationTask({
      prompt: '测试视频生成',
      modelName: 'kling-v1',
      aspectRatio: '16:9',
      duration: 5,
      klingTaskId: `test_ops_${Date.now()}`
    });
    console.log(`✅ 创建任务成功: ${task.id}`);
    
    console.log('🔄 测试更新任务...');
    const updatedTask = await updateVideoGenerationTask(task.id, {
      status: 'processing',
      startedAt: new Date()
    });
    console.log(`✅ 更新任务成功: ${updatedTask.status}`);
    
    console.log('🔍 测试根据Kling ID查询任务...');
    const foundTask = await getVideoGenerationTaskByKlingId(task.kling_task_id);
    console.log(`✅ 查询任务成功: ${foundTask ? '找到' : '未找到'}`);
    
    console.log('🎥 测试创建视频记录...');
    const video = await createGeneratedVideo({
      taskId: task.id,
      videoUrl: 'https://example.com/test-video.mp4',
      videoDuration: 5.0
    });
    console.log(`✅ 创建视频成功: ${video.id}`);
    
    // 清理
    const prisma = new PrismaClient();
    await prisma.generated_videos.delete({ where: { id: video.id } });
    await prisma.video_generation_tasks.delete({ where: { id: task.id } });
    await prisma.$disconnect();
    console.log('✅ 测试数据清理完成');
    
  } catch (error) {
    console.log(`❌ 视频任务操作测试失败: ${error.message}`);
  }
}

// 4. Kling API集成测试
async function testKlingAPIIntegration() {
  console.log('\n🔗 4. Kling API集成测试');
  console.log('-'.repeat(40));
  
  try {
    // 检查环境变量
    const accessKey = process.env.KLING_ACCESS_KEY;
    const secretKey = process.env.KLING_SECRET_KEY;
    
    console.log(`🔑 Access Key: ${accessKey ? '已配置' : '未配置'}`);
    console.log(`🔑 Secret Key: ${secretKey ? '已配置' : '未配置'}`);
    
    if (!accessKey || !secretKey) {
      console.log('⚠️  Kling API 配置不完整，跳过API测试');
      return;
    }
    
    // 测试API端点
    console.log('🌐 测试视频生成API端点...');
    const response = await fetch('http://localhost:3000/api/generate-video', {
      method: 'GET'
    });
    
    if (response.ok) {
      const result = await response.json();
      console.log('✅ 视频生成API端点正常');
      console.log(`📊 API状态: ${result.message}`);
    } else {
      console.log('⚠️  视频生成API端点异常');
    }
    
    console.log('🌐 测试任务状态查询API端点...');
    const statusResponse = await fetch('http://localhost:3000/api/check-task-status', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ taskId: 'test-task-id' })
    });
    
    // 这里预期会失败，因为是测试任务ID
    console.log(`📊 任务状态API响应: ${statusResponse.status}`);
    
  } catch (error) {
    console.log(`❌ Kling API集成测试失败: ${error.message}`);
  }
}

// 运行测试
main().catch(console.error);
