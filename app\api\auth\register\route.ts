import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'

export async function POST(request: NextRequest) {
  try {
    const { email, password, username } = await request.json()

    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      )
    }

    // Check if user already exists
    const existingUser = await prisma.users.findUnique({
      where: { email }
    })

    if (existingUser) {
      return NextResponse.json(
        { error: 'Email already registered' },
        { status: 409 }
      )
    }

    // For test environment, skip password hashing
    // In production: const hashedPassword = await bcrypt.hash(password, 10)

    // Create new user
    const user = await prisma.users.create({
      data: {
        email,
        username: username || email.split('@')[0],
        created_at: new Date(),
        updated_at: new Date()
      }
    })

    // Create user preferences
    await prisma.user_preferences.create({
      data: {
        user_id: user.id,
        default_style: 'modern',
        default_duration: 90,
        favorite_tags: [],
        created_at: new Date(),
        updated_at: new Date()
      }
    })

    const token = jwt.sign(
      { userId: user.id, email: user.email },
      process.env.JWT_SECRET || 'test-secret',
      { expiresIn: '7d' }
    )

    return NextResponse.json({
      user: {
        id: user.id,
        email: user.email,
        username: user.username,
        avatar_url: user.avatar_url
      },
      token
    })
  } catch (error) {
    console.error('Registration error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}