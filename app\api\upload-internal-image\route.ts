import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { imageUrl } = body

    if (!imageUrl) {
      return NextResponse.json({
        success: false,
        error: '没有提供图片URL'
      }, { status: 400 })
    }

    console.log('📤 [UPLOAD-INTERNAL] 开始处理内网图片上传到Supabase Storage...')
    console.log('📤 [UPLOAD-INTERNAL] 原始URL:', imageUrl)

    // 检查是否是内网URL
    const isInternalUrl = imageUrl.includes('***********') ||
                         imageUrl.includes('localhost') ||
                         imageUrl.includes('127.0.0.1') ||
                         imageUrl.includes('192.168.') ||
                         imageUrl.includes('10.') ||
                         imageUrl.startsWith('http://172.') ||
                         imageUrl.startsWith('http://192.168.')

    if (!isInternalUrl) {
      return NextResponse.json({
        success: false,
        error: '提供的URL不是内网地址'
      }, { status: 400 })
    }

    // 检查Supabase配置
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

    if (!supabaseUrl || !supabaseAnonKey) {
      return NextResponse.json({
        success: false,
        error: 'Supabase 配置未完成'
      }, { status: 500 })
    }

    // 初始化Supabase客户端
    const supabase = createClient(supabaseUrl, supabaseAnonKey)

    try {
      // 下载内网图片
      console.log('📥 [UPLOAD-INTERNAL] 下载内网图片...')
      const imageResponse = await fetch(imageUrl)
      if (!imageResponse.ok) {
        throw new Error(`Failed to download image: ${imageResponse.status} ${imageResponse.statusText}`)
      }

      const imageBuffer = await imageResponse.arrayBuffer()
      const buffer = Buffer.from(imageBuffer)

      console.log('📤 [UPLOAD-INTERNAL] 上传到Supabase Storage...')

      // 从URL中提取文件扩展名，或使用默认值
      const urlParts = imageUrl.split('.')
      const fileExtension = urlParts.length > 1 ? urlParts.pop() : 'jpg'
      const fileName = `internal-upload-${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExtension}`
      const filePath = `internal/${fileName}`

      console.log('📤 [UPLOAD-INTERNAL] 生成的文件路径:', filePath)

      // 上传到 Supabase Storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('generated_images')
        .upload(filePath, buffer, {
          contentType: imageResponse.headers.get('content-type') || 'image/jpeg',
          cacheControl: '3600',
          upsert: false
        })

      if (uploadError) {
        console.error('❌ [UPLOAD-INTERNAL] Supabase Storage 上传失败:', uploadError)
        throw new Error(`Supabase Storage upload failed: ${uploadError.message}`)
      }

      console.log('✅ [UPLOAD-INTERNAL] Supabase Storage 上传成功')
      console.log('🖼️ [UPLOAD-INTERNAL] 上传数据:', uploadData)

      // 获取公共URL
      const { data: urlData } = supabase.storage
        .from('generated_images')
        .getPublicUrl(filePath)

      const publicUrl = urlData.publicUrl
      console.log('🖼️ [UPLOAD-INTERNAL] 新的公共URL:', publicUrl)

      // 验证上传的图片URL是否可访问
      console.log('🔍 [UPLOAD-INTERNAL] 验证Supabase Storage URL是否可访问...')
      const verifyResponse = await fetch(publicUrl, { method: 'HEAD' })
      if (!verifyResponse.ok) {
        console.error('❌ [UPLOAD-INTERNAL] Supabase Storage URL验证失败:', verifyResponse.status, verifyResponse.statusText)
        throw new Error('Supabase Storage URL verification failed')
      }
      console.log('✅ [UPLOAD-INTERNAL] Supabase Storage URL验证成功')

      return NextResponse.json({
        success: true,
        message: '内网图片已成功上传到Supabase Storage并验证可访问',
        data: {
          originalUrl: imageUrl,
          supabaseUrl: publicUrl,
          url: publicUrl, // 保持兼容性
          id: uploadData.id || fileName,
          displayUrl: publicUrl,
          deleteUrl: null, // Supabase doesn't provide delete URLs like ImgBB
          filename: fileName,
          size: buffer.length,
          width: null, // We don't extract image dimensions here
          height: null,
          type: imageResponse.headers.get('content-type') || 'image/jpeg',
          uploadTime: Date.now(),
          verified: true,
          path: filePath,
          bucket: 'generated_images'
        }
      })

    } catch (uploadError) {
      console.error('❌ [UPLOAD-INTERNAL] 上传过程中发生错误:', uploadError)
      return NextResponse.json({
        success: false,
        error: '内网图片上传到Supabase Storage失败: ' + (uploadError instanceof Error ? uploadError.message : '未知错误')
      }, { status: 500 })
    }

  } catch (error) {
    console.error('❌ [UPLOAD-INTERNAL] 请求处理失败:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '请求处理失败'
    }, { status: 500 })
  }
}
