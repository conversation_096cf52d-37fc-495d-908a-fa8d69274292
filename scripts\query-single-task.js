#!/usr/bin/env node

/**
 * 查询单个Kling AI任务详情脚本
 * 
 * 用法:
 *   node scripts/query-single-task.js <task_id>
 *   node scripts/query-single-task.js --external-id <external_task_id>
 * 
 * 示例:
 *   node scripts/query-single-task.js task_12345
 *   node scripts/query-single-task.js --external-id my_custom_id_123
 */

// 加载环境变量
require('dotenv').config({ path: '.env.local' });
require('dotenv').config(); // 备用加载 .env 文件

const jwt = require('jsonwebtoken');

// 显示帮助信息
function showHelp() {
  console.log(`
🔍 Kling AI 单任务查询工具

用法:
  node scripts/query-single-task.js <task_id>
  node scripts/query-single-task.js --external-id <external_task_id>

参数:
  task_id                    Kling AI 系统生成的任务ID
  --external-id <id>         用户自定义的外部任务ID
  -h, --help                 显示帮助信息
  -v, --verbose              显示详细信息
  --curl                     生成对应的curl命令

示例:
  node scripts/query-single-task.js task_12345
  node scripts/query-single-task.js --external-id my_custom_id_123
  node scripts/query-single-task.js task_12345 --verbose
  node scripts/query-single-task.js task_12345 --curl

环境变量:
  KLING_ACCESS_KEY          Kling AI Access Key
  KLING_SECRET_KEY          Kling AI Secret Key
`);
}

// 解析命令行参数
function parseArgs() {
  const args = process.argv.slice(2);
  const options = {
    taskId: null,
    externalTaskId: null,
    verbose: false,
    showCurl: false,
    help: false
  };

  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    
    switch (arg) {
      case '--help':
      case '-h':
        options.help = true;
        break;
      case '--external-id':
        if (args[i + 1] && !args[i + 1].startsWith('-')) {
          options.externalTaskId = args[i + 1];
          i++;
        }
        break;
      case '--verbose':
      case '-v':
        options.verbose = true;
        break;
      case '--curl':
        options.showCurl = true;
        break;
      default:
        if (!arg.startsWith('-') && !options.taskId && !options.externalTaskId) {
          options.taskId = arg;
        }
        break;
    }
  }

  return options;
}

// 生成JWT Token
function generateJWTToken() {
  const accessKey = process.env.KLING_ACCESS_KEY;
  const secretKey = process.env.KLING_SECRET_KEY;

  if (!accessKey || !secretKey) {
    throw new Error('请设置 KLING_ACCESS_KEY 和 KLING_SECRET_KEY 环境变量');
  }

  const token = jwt.sign(
    {
      iss: accessKey,
      exp: Math.floor(Date.now() / 1000) + 1800, // 30分钟有效期
      nbf: Math.floor(Date.now() / 1000) - 5     // 5秒前开始生效
    },
    secretKey,
    { algorithm: 'HS256' }
  );

  return token;
}

// 查询单个任务
async function queryTask(options) {
  try {
    console.log('🔍 [QUERY] 开始查询任务详情...');
    
    // 构建URL
    let url = '';
    let queryType = '';

    if (options.taskId) {
      url = `https://api-beijing.klingai.com/v1/videos/image2video/${options.taskId}`;
      queryType = 'task_id';
      console.log(`📋 [QUERY] 任务ID: ${options.taskId}`);
    } else if (options.externalTaskId) {
      url = `https://api-beijing.klingai.com/v1/videos/image2video?external_task_id=${encodeURIComponent(options.externalTaskId)}`;
      queryType = 'external_task_id';
      console.log(`📋 [QUERY] 外部任务ID: ${options.externalTaskId}`);
    } else {
      throw new Error('请提供 task_id 或 external_task_id');
    }
    
    if (options.verbose) {
      console.log(`🔗 [QUERY] 请求URL: ${url}`);
      console.log(`🏷️ [QUERY] 查询类型: ${queryType}`);
    }
    
    // 生成JWT Token
    const token = generateJWTToken();
    
    // 发送请求
    console.log('📡 [QUERY] 发送请求...');
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });
    
    console.log(`📡 [QUERY] HTTP状态: ${response.status} ${response.statusText}`);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ [QUERY] 请求失败:', errorText);
      
      // 提供常见错误的解决建议
      if (response.status === 401) {
        console.error('💡 [QUERY] 建议: 检查 KLING_ACCESS_KEY 和 KLING_SECRET_KEY 是否正确');
      } else if (response.status === 404) {
        console.error('💡 [QUERY] 建议: 检查任务ID是否存在或格式是否正确');
      }
      
      return;
    }
    
    const data = await response.json();
    console.log('✅ [QUERY] 查询成功');
    
    if (options.verbose) {
      console.log('📊 [QUERY] 原始响应数据:');
      console.log(JSON.stringify(data, null, 2));
      console.log('');
    }
    
    // 格式化显示任务信息
    displayTaskInfo(data);
    
    // 显示curl命令
    if (options.showCurl) {
      generateCurlCommand(url, token);
    }
    
  } catch (error) {
    console.error('❌ [QUERY] 查询失败:', error.message);
    
    if (error.message.includes('环境变量')) {
      console.error('💡 [QUERY] 解决方法:');
      console.error('   1. 在项目根目录创建 .env.local 文件');
      console.error('   2. 添加以下内容:');
      console.error('      KLING_ACCESS_KEY=your_access_key');
      console.error('      KLING_SECRET_KEY=your_secret_key');
    }
  }
}

// 格式化显示任务信息
function displayTaskInfo(data) {
  console.log('\n📊 任务详情:');
  console.log('='.repeat(60));
  
  if (data.code !== 0) {
    console.log(`❌ 错误码: ${data.code}`);
    console.log(`❌ 错误信息: ${data.message || '未知错误'}`);
    return;
  }
  
  const task = data.data;
  
  console.log(`📋 任务ID: ${task.task_id}`);
  console.log(`📊 状态: ${getStatusWithEmoji(task.task_status)}`);
  console.log(`📝 状态消息: ${task.task_status_msg || '无'}`);
  console.log(`⏰ 创建时间: ${new Date(task.created_at * 1000).toLocaleString()}`);
  console.log(`🔄 更新时间: ${new Date(task.updated_at * 1000).toLocaleString()}`);
  
  // 显示任务结果
  if (task.task_result) {
    if (task.task_result.videos && task.task_result.videos.length > 0) {
      console.log(`🎬 生成视频数量: ${task.task_result.videos.length}`);
      task.task_result.videos.forEach((video, index) => {
        console.log(`   视频 ${index + 1}: ${video.url}`);
      });
    }
    
    if (task.task_result.images && task.task_result.images.length > 0) {
      console.log(`🖼️ 生成图片数量: ${task.task_result.images.length}`);
      task.task_result.images.forEach((image, index) => {
        console.log(`   图片 ${index + 1}: ${image.url}`);
      });
    }
  }
  
  // 显示任务信息
  if (task.task_info) {
    console.log('📋 任务参数:');
    const taskInfo = task.task_info;
    if (taskInfo.prompt) console.log(`   提示词: ${taskInfo.prompt}`);
    if (taskInfo.negative_prompt) console.log(`   负面提示词: ${taskInfo.negative_prompt}`);
    if (taskInfo.duration) console.log(`   时长: ${taskInfo.duration}秒`);
    if (taskInfo.aspect_ratio) console.log(`   宽高比: ${taskInfo.aspect_ratio}`);
    if (taskInfo.model_name) console.log(`   模型: ${taskInfo.model_name}`);
  }
  
  console.log('='.repeat(60));
}

// 获取带表情符号的状态
function getStatusWithEmoji(status) {
  const statusMap = {
    'submitted': '📝 已提交',
    'processing': '⏳ 处理中',
    'succeed': '✅ 成功',
    'failed': '❌ 失败'
  };
  return statusMap[status] || `❓ ${status}`;
}

// 生成curl命令
function generateCurlCommand(url, token) {
  console.log('\n💡 对应的curl命令:');
  console.log('='.repeat(60));
  console.log(`curl -X GET "${url}" \\`);
  console.log(`  -H "Authorization: Bearer ${token.substring(0, 20)}..." \\`);
  console.log(`  -H "Content-Type: application/json" \\`);
  console.log(`  -v`);
  console.log('='.repeat(60));
}

// 主函数
async function main() {
  const options = parseArgs();
  
  if (options.help) {
    showHelp();
    return;
  }
  
  if (!options.taskId && !options.externalTaskId) {
    console.error('❌ 错误: 请提供任务ID');
    console.error('用法: node scripts/query-single-task.js <task_id>');
    console.error('或者: node scripts/query-single-task.js --external-id <external_task_id>');
    console.error('使用 --help 查看详细帮助');
    process.exit(1);
  }
  
  await queryTask(options);
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  queryTask,
  generateJWTToken,
  displayTaskInfo
};
