import { NextRequest, NextResponse } from 'next/server'
import { KlingAIClient } from '@/lib/kling-ai'

export async function POST(request: NextRequest) {
  try {
    const { taskIds } = await request.json()

    console.log('🔍 [BATCH-KLING-QUERY] 批量查询Kling任务状态:', taskIds)

    if (!taskIds || !Array.isArray(taskIds) || taskIds.length === 0) {
      return NextResponse.json({
        success: false,
        error: '缺少有效的taskIds参数'
      }, { status: 400 })
    }

    const accessKey = process.env.KLING_ACCESS_KEY
    const secretKey = process.env.KLING_SECRET_KEY

    if (!accessKey || !secretKey) {
      console.error('❌ [BATCH-KLING-QUERY] 缺少KLING_ACCESS_KEY或KLING_SECRET_KEY环境变量')
      return NextResponse.json({
        success: false,
        error: 'Kling API密钥未配置'
      }, { status: 500 })
    }

    // 创建 Kling AI 客户端
    const klingClient = new KlingAIClient({ accessKey, secretKey })

    console.log(`🔍 [BATCH-KLING-QUERY] 开始批量查询 ${taskIds.length} 个Kling任务`)

    // 批量查询所有任务状态
    const results: Record<string, any> = {}
    const errors: Record<string, string> = {}

    // 并发查询所有任务，但限制并发数量避免API限制
    const batchSize = 5 // 每批最多5个并发请求
    for (let i = 0; i < taskIds.length; i += batchSize) {
      const batch = taskIds.slice(i, i + batchSize)
      
      const batchPromises = batch.map(async (taskId: string) => {
        try {
          console.log(`🔍 [BATCH-KLING-QUERY] 查询任务: ${taskId}`)

          // 使用 KlingAIClient 查询任务状态（指定为视频任务）
          const result = await klingClient.getTaskStatus(taskId, 'video')

          console.log(`✅ [BATCH-KLING-QUERY] 任务 ${taskId} 查询成功:`, result.data?.task_status)
          results[taskId] = result.data

        } catch (error) {
          console.error(`❌ [BATCH-KLING-QUERY] 任务 ${taskId} 查询异常:`, error)
          errors[taskId] = error instanceof Error ? error.message : '未知错误'
        }
      })

      // 等待当前批次完成
      await Promise.all(batchPromises)
      
      // 批次间添加延迟，避免API限制
      if (i + batchSize < taskIds.length) {
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
    }

    const successCount = Object.keys(results).length
    const errorCount = Object.keys(errors).length

    console.log(`📊 [BATCH-KLING-QUERY] 批量查询完成: 成功 ${successCount}个, 失败 ${errorCount}个`)

    if (errorCount > 0) {
      console.warn('⚠️ [BATCH-KLING-QUERY] 部分任务查询失败:', errors)
    }

    return NextResponse.json({
      success: true,
      data: results,
      errors: errorCount > 0 ? errors : undefined,
      summary: {
        total: taskIds.length,
        success: successCount,
        failed: errorCount
      }
    })

  } catch (error) {
    console.error('❌ [BATCH-KLING-QUERY] 批量查询失败:', error)
    return NextResponse.json({
      success: false,
      error: '服务器内部错误',
      details: error instanceof Error ? error.message : '未知错误'
    }, { status: 500 })
  }
}
