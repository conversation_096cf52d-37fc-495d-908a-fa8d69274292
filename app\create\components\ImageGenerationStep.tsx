"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"

import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

import {
  ImageIcon,
  RefreshCw,
  Upload,
  Save,
  Check,
  X,
} from "lucide-react"
import React from "react"
import { mapStyleToKlingStyle, mapSizeToAspectRatio } from "@/lib/kling-ai"

interface Shot {
  shotNumber: number
  location: string
  action: string
  characters: string[]
  lighting: string
  mood: string
  cameraMovement: string
}

interface ScriptData {
  title?: string
  style?: string
  shots?: Shot[]
}

interface GeneratedImage {
  url: string
  id: string
}

interface ImageGenerationStepProps {
  selectedStyle: string
  setSelectedStyle: (style: string) => void
  scriptData?: ScriptData
  projectId?: string
  userId?: string
  editMode?: boolean
  onImageGenerated?: () => void
}

interface Scene {
  id: number
  title: string
  description: string
  prompt: string
  generatedImages: GeneratedImage[]
}

export function ImageGenerationStep({
  selectedStyle,
  setSelectedStyle,
  scriptData,
  projectId,
  userId,
  editMode = false,
  onImageGenerated,
}: ImageGenerationStepProps) {
  const [currentScene, setCurrentScene] = useState(0)
  const [imageSize, setImageSize] = useState("1280x720")
  const [imageCount, setImageCount] = useState(1)
  const [quality, setQuality] = useState("kling-v1-5")

  const [negativePrompt, setNegativePrompt] = useState("")
  const [isGenerating, setIsGenerating] = useState(false)
  const [generationProgress, setGenerationProgress] = useState(0)
  const [error, setError] = useState<string | null>(null)
  const [selectedImage, setSelectedImage] = useState<GeneratedImage | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [isGeneratingAll, setIsGeneratingAll] = useState(false)
  const [batchProgress, setBatchProgress] = useState({ current: 0, total: 0 })
  
  
  // 根据脚本数据生成场景
  const generateScenesFromScript = React.useCallback((): Scene[] => {
    if (!scriptData || !scriptData.shots) {
      return [
        { 
          id: 1, 
          title: "开场场景", 
          description: "城市街道，黄昏时分",
          prompt: "A busy city street at dusk, golden hour lighting, urban landscape with skyscrapers, warm orange and purple sky, cinematic composition, high quality, detailed",
          generatedImages: []
        },
        { 
          id: 2, 
          title: "对话场景", 
          description: "咖啡厅内部，温馨氛围",
          prompt: "Cozy coffee shop interior, warm lighting, wooden furniture, comfortable seating area, steaming coffee cups, ambient atmosphere, soft natural light",
          generatedImages: []
        },
        { 
          id: 3, 
          title: "冲突场景", 
          description: "公园长椅，雨天",
          prompt: "Park bench in the rain, dramatic weather, wet pavement reflections, moody atmosphere, overcast sky, dramatic lighting, cinematic scene",
          generatedImages: []
        },
        { 
          id: 4, 
          title: "转折场景", 
          description: "办公室，明亮光线",
          prompt: "Modern office space with bright natural lighting, large windows, clean professional environment, contemporary design, business setting",
          generatedImages: []
        },
        { 
          id: 5, 
          title: "高潮场景", 
          description: "天台，夜景城市",
          prompt: "Rooftop terrace at night with city skyline view, twinkling lights, urban nightscape, dramatic perspective, romantic atmosphere",
          generatedImages: []
        },
        { 
          id: 6, 
          title: "结尾场景", 
          description: "海边，日出时分",
          prompt: "Beach at sunrise, golden light reflecting on water, peaceful ocean scene, horizon view, serene morning atmosphere, natural beauty",
          generatedImages: []
        },
      ]
    }

    // 根据剧本数据生成场景
    return scriptData.shots.map((shot: Shot) => ({
      id: shot.shotNumber, // 使用实际的镜头号而不是索引+1
      title: `镜头 ${shot.shotNumber}`,
      description: `${shot.location} - ${shot.action}`,
      prompt: generateImagePrompt(shot, scriptData.style || 'drama'),
      generatedImages: []
    }))
  }, [scriptData])

  // 根据分镜头数据生成图像提示词
  const generateImagePrompt = (shot: Shot, style: string): string => {
    const basePrompt = `${shot.location}, ${shot.action}, ${shot.characters.join(' and ')}, ${shot.lighting}, ${shot.mood} atmosphere, ${shot.cameraMovement}`
    
    // 根据风格添加风格化描述
    const stylePrompts = {
      romantic: "romantic, soft lighting, warm colors, dreamy atmosphere",
      comedy: "bright colors, cheerful atmosphere, dynamic composition",
      thriller: "dramatic lighting, dark shadows, suspenseful mood",
      action: "dynamic action, dramatic angles, high contrast",
      drama: "emotional depth, cinematic lighting, realistic style"
    }
    
    const stylePrompt = stylePrompts[style as keyof typeof stylePrompts] || "cinematic, high quality"
    
    return `${basePrompt}, ${stylePrompt}, detailed, professional photography, 4K resolution`
  }

  const [scenes, setScenes] = useState<Scene[]>(() => generateScenesFromScript())

  // 当scriptData改变时更新场景
  React.useEffect(() => {
    if (scriptData) {
      setScenes(generateScenesFromScript())
      setCurrentScene(0)
    }
  }, [scriptData, generateScenesFromScript])

  // 在编辑模式下加载历史图片
  useEffect(() => {
    if (editMode && projectId) {
      loadHistoryImages()
    }
  }, [editMode, projectId])

  // 加载项目的历史图片
  const loadHistoryImages = async () => {
    try {
      const response = await fetch(`/api/projects/${projectId}/shots-images`)
      const data = await response.json()

      if (data.success && data.data.shots) {
        // 更新场景数据，添加历史图片
        setScenes(prevScenes => {
          return prevScenes.map(scene => {
            // 查找对应的镜头图片数据 - 严格匹配镜头号
            const shotData = data.data.shots.find(
              (shot: any) => shot.shotNumber === scene.id
            )

            if (shotData && shotData.hasImages) {
              const generatedImages = shotData.images.map((img: any) => ({
                id: img.id,
                url: img.url,
                isPrimary: img.isPrimary,
                createdAt: img.createdAt,
                actualPrompt: img.actualPrompt,
                qualityScore: img.qualityScore,
                taskName: img.taskName
              }))

              return {
                ...scene,
                generatedImages,
                hasImages: true,
                imageCount: shotData.imageCount,
                tasks: shotData.tasks
              }
            }

            return {
              ...scene,
              hasImages: false,
              imageCount: 0
            }
          })
        })
      }
    } catch (error) {
      console.error('Error loading history images:', error)
    }
  }

  const updateScenePrompt = (sceneId: number, newPrompt: string) => {
    setScenes(scenes.map((scene: Scene) => 
      scene.id === sceneId ? { ...scene, prompt: newPrompt } : scene
    ))
  }

  const handleGenerateImage = async () => {
    setIsGenerating(true)
    setError(null)
    setGenerationProgress(0)

    try {
      const currentSceneData = scenes[currentScene]
      if (!currentSceneData) return

      // 构建提示词
      const fullPrompt = `${currentSceneData.prompt}, ${selectedStyle} style, ${mapStyleToKlingStyle(selectedStyle)}`
      let taskId = null

      // 只有当项目ID和用户ID都提供时才创建任务记录
      if (projectId && userId) {
        // 步骤1: 创建图像生成任务记录
        const taskResponse = await fetch('/api/save-image-generation', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            projectId,
            userId,
            taskName: currentSceneData.title,
            prompt: fullPrompt,
            negativePrompt: negativePrompt || undefined,
            modelName: quality,
            aspectRatio: mapSizeToAspectRatio(imageSize),
            imageCount,
          }),
        })

        if (!taskResponse.ok) {
          const errorData = await taskResponse.json()
          throw new Error(errorData.error || '创建任务记录失败')
        }

        const taskResult = await taskResponse.json()
        taskId = taskResult.taskId
      }

      // 步骤2: 调用服务器端API生成图片
      const generateResponse = await fetch('/api/generate-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          prompt: fullPrompt,
          negativePrompt: negativePrompt || undefined,
          n: imageCount,
          aspectRatio: mapSizeToAspectRatio(imageSize),
          modelName: quality,
        }),
      })

      if (!generateResponse.ok) {
        const errorData = await generateResponse.json()
        
        // 如果创建了任务记录，更新任务状态为失败
        if (taskId) {
          await fetch('/api/save-image-generation', {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              taskId,
              status: 'failed',
              errorMessage: errorData.error || '图片生成失败',
              startedAt: new Date().toISOString(),
              completedAt: new Date().toISOString(),
            }),
          })
        }
        
        throw new Error(errorData.error || '图片生成失败')
      }

      const generationResult = await generateResponse.json()
      
      // 如果创建了任务记录，更新任务状态为成功
      if (taskId && projectId && userId) {
        const images = generationResult.images.map((img: any, index: number) => ({
          imageUrl: img.url,
          imageWidth: img.width,
          imageHeight: img.height,
          imageFormat: 'jpg',
          isPrimary: index === 0,
          minioObjectName: img.url.includes('minio') ? img.url.split('/').pop() : null,
          minioBucketName: 'images',
          generationTimeSeconds: 0,
          actualPrompt: fullPrompt,
          metadata: {
            originalUrl: img.originalUrl || img.url,
            taskId: generationResult.taskId,
            index,
          }
        }))

        await fetch('/api/save-image-generation', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            taskId,
            status: 'completed',
            klingTaskId: generationResult.taskId,
            startedAt: new Date().toISOString(),
            completedAt: new Date().toISOString(),
            images,
          }),
        })
      }

      // 步骤3: 更新场景中的生成图片
      const newImages = generationResult.images.map((img: any, index: number) => ({
        url: img.url,
        id: `${generationResult.taskId || Date.now()}-${index}`,
        taskId: taskId || generationResult.taskId,
      }))

      setScenes(scenes.map((scene: Scene) =>
        scene.id === currentSceneData.id
          ? { ...scene, generatedImages: [...scene.generatedImages, ...newImages] }
          : scene
      ))

      setIsGenerating(false)
      setGenerationProgress(100)

      // 调用回调函数通知父组件图片已生成
      if (onImageGenerated) {
        onImageGenerated()
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : '生成失败')
      setIsGenerating(false)
    }
  }

  const openImageModal = (image: GeneratedImage) => {
    setSelectedImage(image)
    setIsModalOpen(true)
  }

  const closeImageModal = () => {
    setIsModalOpen(false)
    setSelectedImage(null)
  }

  // 一键生成所有镜头图片
  const handleGenerateAllImages = async () => {
    if (isGeneratingAll || isGenerating) return

    // 筛选出没有图片的场景
    const scenesWithoutImages = scenes.filter(scene => scene.generatedImages.length === 0)

    if (scenesWithoutImages.length === 0) {
      alert('所有镜头都已有图片，无需生成')
      return
    }

    setIsGeneratingAll(true)
    setBatchProgress({ current: 0, total: scenesWithoutImages.length })
    setError(null)

    try {
      for (let i = 0; i < scenesWithoutImages.length; i++) {
        const scene = scenesWithoutImages[i]
        setBatchProgress({ current: i + 1, total: scenesWithoutImages.length })

        // 构建提示词（使用默认设置）
        const fullPrompt = `${scene.prompt}, ${selectedStyle} style, ${mapStyleToKlingStyle(selectedStyle)}`
        let taskId = null

        try {
          // 只有当项目ID和用户ID都提供时才创建任务记录
          if (projectId && userId) {
            // 创建图像生成任务记录
            const taskResponse = await fetch('/api/save-image-generation', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                projectId,
                userId,
                taskName: scene.title,
                prompt: fullPrompt,
                negativePrompt: negativePrompt || undefined,
                modelName: quality,
                aspectRatio: mapSizeToAspectRatio(imageSize),
                imageCount: 1, // 批量生成时默认每个场景生成1张
              }),
            })

            if (taskResponse.ok) {
              const taskResult = await taskResponse.json()
              taskId = taskResult.taskId
            }
          }

          // 调用服务器端API生成图片
          const generateResponse = await fetch('/api/generate-image', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              prompt: fullPrompt,
              negativePrompt: negativePrompt || undefined,
              n: 1, // 批量生成时默认每个场景生成1张
              aspectRatio: mapSizeToAspectRatio(imageSize),
              modelName: quality,
            }),
          })

          if (generateResponse.ok) {
            const generationResult = await generateResponse.json()

            // 更新任务状态为成功
            if (taskId && projectId && userId) {
              const images = generationResult.images.map((img: any, index: number) => ({
                imageUrl: img.url,
                imageWidth: img.width,
                imageHeight: img.height,
                imageFormat: 'jpg',
                isPrimary: index === 0,
                minioObjectName: img.url.includes('minio') ? img.url.split('/').pop() : null,
                minioBucketName: 'images',
                generationTimeSeconds: 0,
                actualPrompt: fullPrompt,
                metadata: {
                  originalUrl: img.originalUrl || img.url,
                  taskId: generationResult.taskId,
                  index,
                }
              }))

              await fetch('/api/save-image-generation', {
                method: 'PUT',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  taskId,
                  status: 'completed',
                  klingTaskId: generationResult.taskId,
                  startedAt: new Date().toISOString(),
                  completedAt: new Date().toISOString(),
                  images,
                }),
              })
            }

            // 更新场景中的生成图片
            const newImages = generationResult.images.map((img: any, index: number) => ({
              url: img.url,
              id: `${generationResult.taskId || Date.now()}-${index}`,
              taskId: taskId || generationResult.taskId,
            }))

            setScenes(prevScenes => prevScenes.map(s =>
              s.id === scene.id
                ? { ...s, generatedImages: [...s.generatedImages, ...newImages] }
                : s
            ))

          } else {
            const errorData = await generateResponse.json()
            console.error(`场景 ${scene.title} 生成失败:`, errorData.error)

            // 更新任务状态为失败
            if (taskId) {
              await fetch('/api/save-image-generation', {
                method: 'PUT',
                headers: {
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  taskId,
                  status: 'failed',
                  errorMessage: errorData.error || '图片生成失败',
                  startedAt: new Date().toISOString(),
                  completedAt: new Date().toISOString(),
                }),
              })
            }
          }
        } catch (sceneError) {
          console.error(`场景 ${scene.title} 生成出错:`, sceneError)

          // 更新任务状态为失败
          if (taskId) {
            await fetch('/api/save-image-generation', {
              method: 'PUT',
              headers: {
                'Content-Type': 'application/json',
              },
              body: JSON.stringify({
                taskId,
                status: 'failed',
                errorMessage: sceneError instanceof Error ? sceneError.message : '图片生成失败',
                startedAt: new Date().toISOString(),
                completedAt: new Date().toISOString(),
              }),
            })
          }
        }

        // 添加延迟避免API限制
        if (i < scenesWithoutImages.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 2000))
        }
      }

      alert(`批量生成完成！成功为 ${scenesWithoutImages.length} 个场景生成了图片`)

      // 调用回调函数通知父组件图片已生成
      if (onImageGenerated) {
        onImageGenerated()
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '批量生成失败')
      console.error('批量生成错误:', err)
    } finally {
      setIsGeneratingAll(false)
      setBatchProgress({ current: 0, total: 0 })
    }
  }

  // 添加键盘事件监听
  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isModalOpen) {
        closeImageModal()
      }
    }

    if (isModalOpen) {
      document.addEventListener('keydown', handleKeyDown)
      // 防止背景滚动
      document.body.style.overflow = 'hidden'
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown)
      document.body.style.overflow = 'unset'
    }
  }, [isModalOpen])

  return (
    <div className="h-full flex">
      {/* 图片预览模态框 */}
      {isModalOpen && selectedImage && (
        <div 
          className="fixed inset-0 z-50 bg-black/90 flex items-center justify-center p-4"
          onClick={closeImageModal}
        >
          <div 
            className="relative max-w-full max-h-full"
            onClick={(e) => e.stopPropagation()}
          >
            <img 
              src={selectedImage.url} 
              alt="预览大图"
              className="max-w-full max-h-full object-contain rounded-lg shadow-2xl"
            />
            <button
              onClick={closeImageModal}
              className="absolute -top-12 right-0 text-white hover:text-gray-300 transition-colors"
            >
              <X className="w-8 h-8" />
            </button>
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-white text-sm bg-black/50 px-3 py-1 rounded-full">
              点击图片外区域或按 ESC 关闭
            </div>
          </div>
        </div>
      )}

      {/* 左侧场景列表 */}
      <div className="w-1/3 bg-white/80 backdrop-blur-sm border-r border-slate-200/60 flex flex-col">
        {/* 固定头部 */}
        <div className="flex-shrink-0 p-8 pb-6">
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center">
                <ImageIcon className="w-5 h-5 text-white" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-slate-800">
                  {editMode ? '场景图片管理' : '场景管理'}
                </h2>
                <p className="text-sm text-slate-600">AI图片生成工坊</p>
              </div>
              {editMode && (
                <Badge className="bg-gradient-to-r from-amber-100 to-orange-100 text-amber-800 border-amber-200 font-medium px-3 py-1">
                  <RefreshCw className="w-3 h-3 mr-1" />
                  编辑模式
                </Badge>
              )}
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                className="group bg-white/70 border-slate-300 hover:border-blue-400 hover:bg-blue-50 hover:text-blue-700 transition-all duration-300"
                onClick={handleGenerateAllImages}
                disabled={isGeneratingAll || isGenerating}
              >
                <ImageIcon className="w-4 h-4 mr-2 group-hover:scale-110 transition-transform duration-300" />
                <span className="font-medium">
                  {isGeneratingAll ? '生成中...' : '一键生成'}
                </span>
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="group bg-white/70 border-slate-300 hover:border-purple-400 hover:bg-purple-50 hover:text-purple-700 transition-all duration-300"
              >
                <Upload className="w-4 h-4 mr-2 group-hover:scale-110 transition-transform duration-300" />
                <span className="font-medium">脚本提取</span>
              </Button>
            </div>
          </div>

          {/* 显示缺少必需参数提示 */}
          {(!projectId || !userId) && (
            <div className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl shadow-sm">
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 rounded-full bg-blue-100 flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-blue-600 text-xs font-bold">!</span>
                </div>
                <div>
                  <h4 className="text-sm font-semibold text-blue-800 mb-1">预览模式</h4>
                  <p className="text-xs text-blue-700 leading-relaxed">
                    当前为预览模式，生成的图片将不会保存到数据库。如需保存记录，请确保已登录并拥有项目ID。
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* 如果是从剧本生成的场景，显示提示 */}
          {scriptData && (
            <div className="mb-6 p-4 bg-gradient-to-r from-emerald-50 to-green-50 border border-emerald-200 rounded-xl shadow-sm">
              <div className="flex items-center space-x-3">
                <div className="w-6 h-6 rounded-full bg-emerald-100 flex items-center justify-center">
                  <Check className="w-3 h-3 text-emerald-600" />
                </div>
                <div>
                  <h4 className="text-sm font-semibold text-emerald-800">脚本已加载</h4>
                  <p className="text-xs text-emerald-700">已从剧本自动生成 {scenes.length} 个场景</p>
                </div>
              </div>
            </div>
          )}

          {/* 批量生成进度提示 */}
          {isGeneratingAll && (
            <div className="mb-6 p-4 bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200 rounded-xl shadow-sm">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-2">
                  <div className="w-5 h-5 rounded-full bg-purple-100 flex items-center justify-center">
                    <RefreshCw className="w-3 h-3 text-purple-600 animate-spin" />
                  </div>
                  <span className="text-sm font-semibold text-purple-800">批量生成中...</span>
                </div>
                <span className="text-sm font-bold text-purple-700 bg-white px-2 py-1 rounded-full">
                  {batchProgress.current}/{batchProgress.total}
                </span>
              </div>
              <div className="w-full bg-purple-200 rounded-full h-2 overflow-hidden">
                <div
                  className="bg-gradient-to-r from-purple-500 to-pink-500 h-2 rounded-full transition-all duration-500 shadow-sm"
                  style={{ width: `${(batchProgress.current / batchProgress.total) * 100}%` }}
                />
              </div>
            </div>
          )}
        </div>

        {/* 可滚动的场景列表 */}
        <div className="flex-1 overflow-y-auto px-8 pb-8">
          <div className="space-y-4">
            {scenes.map((scene, index) => (
              <div
                key={scene.id}
                onClick={() => setCurrentScene(index)}
                className={`group p-5 rounded-xl border-2 cursor-pointer transition-all duration-300 hover:scale-[1.02] ${
                  currentScene === index
                    ? "border-blue-400 bg-gradient-to-r from-blue-50 to-indigo-50 shadow-lg shadow-blue-100/50"
                    : "border-slate-200 hover:border-blue-300 hover:bg-gradient-to-r hover:from-blue-50/50 hover:to-indigo-50/50 hover:shadow-md"
                }`}
              >
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium text-gray-900">{scene.title}</h3>
                  <div className="flex items-center space-x-2">
                    {editMode && (scene as any).hasImages && (
                      <Badge variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">
                        已有图片
                      </Badge>
                    )}
                    <Badge
                      variant={scene.generatedImages.length > 0 ? "default" : "secondary"}
                      className={`${
                        scene.generatedImages.length > 0
                          ? "bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-800 border-blue-200"
                          : "bg-gray-100 text-gray-600 border-gray-200"
                      }`}
                    >
                      {scene.generatedImages.length}
                    </Badge>
                  </div>
                </div>
                <p className="text-sm text-gray-600 line-clamp-2">{scene.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* 右侧编辑和预览区域 */}
      <div className="flex-1 p-8 bg-gradient-to-br from-white/60 to-slate-50/80 backdrop-blur-sm">
        <div className="h-full flex flex-col">
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 rounded-lg bg-gradient-to-br from-indigo-500 to-purple-600 flex items-center justify-center">
                <ImageIcon className="w-4 h-4 text-white" />
              </div>
              <div>
                <h3 className="text-2xl font-bold text-slate-800">
                  {scenes[currentScene]?.title}
                </h3>
                <p className="text-sm text-slate-600 mt-1">{scenes[currentScene]?.description}</p>
              </div>
            </div>
            <div className="flex space-x-3">
              <Button
                variant="outline"
                onClick={() => handleGenerateImage()}
                disabled={isGenerating}
                className="group bg-white/80 border-slate-300 hover:border-indigo-400 hover:bg-indigo-50 hover:text-indigo-700 transition-all duration-300"
              >
                <RefreshCw className={`w-4 h-4 mr-2 group-hover:scale-110 transition-transform duration-300 ${isGenerating ? 'animate-spin' : ''}`} />
                <span className="font-medium">{isGenerating ? '生成中...' : '重新生成'}</span>
              </Button>
              <Button
                onClick={() => handleGenerateImage()}
                disabled={isGenerating}
                className="group bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white font-bold shadow-xl hover:shadow-2xl transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed hover:scale-105 disabled:hover:scale-100 px-6"
              >
                <ImageIcon className="w-4 h-4 mr-2 group-hover:scale-110 transition-transform duration-300" />
                <span>{isGenerating ? '生成中...' : '生成图片'}</span>
              </Button>
            </div>
          </div>

          {/* 错误提示 */}
          {error && (
            <div className="mb-6 p-4 bg-gradient-to-r from-red-50 to-pink-50 border border-red-200 rounded-xl shadow-sm">
              <div className="flex items-start space-x-3">
                <div className="w-6 h-6 rounded-full bg-red-100 flex items-center justify-center flex-shrink-0 mt-0.5">
                  <span className="text-red-600 text-xs font-bold">!</span>
                </div>
                <div>
                  <h4 className="text-sm font-semibold text-red-800 mb-1">生成失败</h4>
                  <p className="text-sm text-red-700">{error}</p>
                </div>
              </div>
            </div>
          )}

          {/* 进度条 */}
          {isGenerating && (
            <div className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl shadow-sm">
              <div className="flex justify-between items-center mb-3">
                <div className="flex items-center space-x-2">
                  <div className="w-5 h-5 rounded-full bg-blue-100 flex items-center justify-center">
                    <RefreshCw className="w-3 h-3 text-blue-600 animate-spin" />
                  </div>
                  <span className="text-sm font-semibold text-blue-800">AI生成中...</span>
                </div>
                <span className="text-sm font-bold text-blue-700 bg-white px-2 py-1 rounded-full">
                  {Math.round(generationProgress)}%
                </span>
              </div>
              <div className="w-full bg-blue-200 rounded-full h-2 overflow-hidden">
                <div
                  className="bg-gradient-to-r from-blue-500 to-indigo-500 h-2 rounded-full transition-all duration-500 shadow-sm"
                  style={{ width: `${generationProgress}%` }}
                />
              </div>
            </div>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 flex-1">
            {/* 生成设置区 */}
            <div className="space-y-4">
              <Card className="h-full border-gray-200 shadow-sm bg-gradient-to-br from-white to-gray-50/30">
                <CardHeader className="pb-4">
                  <CardTitle className="text-lg text-gray-900 flex items-center gap-2">
                    <div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-500 to-purple-500"></div>
                    生成设置
                  </CardTitle>
                </CardHeader>
                <CardContent className="h-full flex flex-col space-y-6 overflow-y-auto">
                  {/* 提示词部分 */}
                  <div className="space-y-4">
                    <div>
                      <Label className="text-base font-medium mb-3 block text-gray-800">正向提示词</Label>
                      <Textarea
                        placeholder="描述你想要生成的图片内容、风格、构图等..."
                        value={scenes[currentScene]?.prompt || ''}
                        onChange={(e) => updateScenePrompt(scenes[currentScene]?.id, e.target.value)}
                        className="h-28 resize-none bg-gradient-to-r from-blue-50/50 to-indigo-50/50 border-blue-200 focus:border-blue-400 focus:ring-2 focus:ring-blue-100 transition-all duration-200 rounded-lg"
                      />
                    </div>

                    <div>
                      <Label className="text-base font-medium mb-3 block text-gray-800">负向提示词</Label>
                      <Textarea
                        placeholder="描述不希望出现的内容，如：模糊、低质量、变形等..."
                        value={negativePrompt}
                        onChange={(e) => setNegativePrompt(e.target.value)}
                        className="h-20 resize-none bg-gradient-to-r from-red-50/50 to-pink-50/50 border-red-200 focus:border-red-400 focus:ring-2 focus:ring-red-100 transition-all duration-200 rounded-lg"
                      />
                    </div>
                  </div>

                  {/* 分隔线 */}
                  <div className="flex items-center gap-4">
                    <div className="flex-1 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div>
                    <span className="text-sm text-gray-500 font-medium">参数设置</span>
                    <div className="flex-1 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div>
                  </div>

                  {/* 参数设置部分 */}
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium mb-2 block text-gray-700">图片尺寸</Label>
                      <Select value={imageSize} onValueChange={setImageSize}>
                        <SelectTrigger className="h-10 bg-white/80 border-gray-200 hover:border-blue-300 focus:border-blue-400 focus:ring-2 focus:ring-blue-100 transition-all duration-200">
                          <SelectValue className="text-gray-600 text-sm" />
                        </SelectTrigger>
                        <SelectContent className="bg-white/95 backdrop-blur-sm border border-gray-200 shadow-lg rounded-lg">
                          <SelectItem value="512x512" className="text-sm hover:bg-blue-50 focus:bg-blue-50">512×512</SelectItem>
                          <SelectItem value="768x768" className="text-sm hover:bg-blue-50 focus:bg-blue-50">768×768</SelectItem>
                          <SelectItem value="1024x1024" className="text-sm hover:bg-blue-50 focus:bg-blue-50">1024×1024</SelectItem>
                          <SelectItem value="1024x768" className="text-sm hover:bg-blue-50 focus:bg-blue-50">1024×768</SelectItem>
                          <SelectItem value="768x1024" className="text-sm hover:bg-blue-50 focus:bg-blue-50">768×1024</SelectItem>
                          <SelectItem value="1280x720" className="text-sm hover:bg-blue-50 focus:bg-blue-50">1280×720</SelectItem>
                          <SelectItem value="720x1280" className="text-sm hover:bg-blue-50 focus:bg-blue-50">720×1280</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label className="text-sm font-medium mb-2 block text-gray-700">生成数量</Label>
                      <Select value={imageCount.toString()} onValueChange={(value) => setImageCount(Number(value))}>
                        <SelectTrigger className="h-10 bg-white/80 border-gray-200 hover:border-blue-300 focus:border-blue-400 focus:ring-2 focus:ring-blue-100 transition-all duration-200">
                          <SelectValue className="text-gray-600 text-sm" />
                        </SelectTrigger>
                        <SelectContent className="bg-white/95 backdrop-blur-sm border border-gray-200 shadow-lg rounded-lg">
                          <SelectItem value="1" className="text-sm hover:bg-blue-50 focus:bg-blue-50">1张图片</SelectItem>
                          <SelectItem value="2" className="text-sm hover:bg-blue-50 focus:bg-blue-50">2张图片</SelectItem>
                          <SelectItem value="4" className="text-sm hover:bg-blue-50 focus:bg-blue-50">4张图片</SelectItem>
                          <SelectItem value="6" className="text-sm hover:bg-blue-50 focus:bg-blue-50">6张图片</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium mb-2 block text-gray-700">艺术风格</Label>
                      <Select value={selectedStyle} onValueChange={setSelectedStyle}>
                        <SelectTrigger className="h-10 bg-white/80 border-gray-200 hover:border-blue-300 focus:border-blue-400 focus:ring-2 focus:ring-blue-100 transition-all duration-200">
                          <SelectValue placeholder="选择风格" className="text-gray-600 text-sm" />
                        </SelectTrigger>
                        <SelectContent className="bg-white/95 backdrop-blur-sm border border-gray-200 shadow-lg rounded-lg">
                          <SelectItem value="realistic" className="text-sm hover:bg-blue-50 focus:bg-blue-50">🎨 写实摄影</SelectItem>
                          <SelectItem value="anime" className="text-sm hover:bg-blue-50 focus:bg-blue-50">🌸 动漫风格</SelectItem>
                          <SelectItem value="cartoon" className="text-sm hover:bg-blue-50 focus:bg-blue-50">🎭 卡通插画</SelectItem>
                          <SelectItem value="oil" className="text-sm hover:bg-blue-50 focus:bg-blue-50">🖼️ 油画艺术</SelectItem>
                          <SelectItem value="watercolor" className="text-sm hover:bg-blue-50 focus:bg-blue-50">💧 水彩画风</SelectItem>
                          <SelectItem value="sketch" className="text-sm hover:bg-blue-50 focus:bg-blue-50">✏️ 素描线稿</SelectItem>
                          <SelectItem value="cyberpunk" className="text-sm hover:bg-blue-50 focus:bg-blue-50">🌃 赛博朋克</SelectItem>
                          <SelectItem value="fantasy" className="text-sm hover:bg-blue-50 focus:bg-blue-50">✨ 奇幻风格</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label className="text-sm font-medium mb-2 block text-gray-700">模型选择</Label>
                      <Select value={quality} onValueChange={setQuality}>
                        <SelectTrigger className="h-10 bg-white/80 border-gray-200 hover:border-blue-300 focus:border-blue-400 focus:ring-2 focus:ring-blue-100 transition-all duration-200">
                          <SelectValue className="text-gray-600 text-sm" />
                        </SelectTrigger>
                        <SelectContent className="bg-white/95 backdrop-blur-sm border border-gray-200 shadow-lg rounded-lg">
                          <SelectItem value="kling-v1-5" className="text-sm hover:bg-blue-50 focus:bg-blue-50">Kling v1.5</SelectItem>
                          <SelectItem value="kling-v2" className="text-sm hover:bg-blue-50 focus:bg-blue-50">Kling v2.0</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* 操作按钮 */}
                  <div className="flex space-x-2 pt-2">
                    <Button variant="outline" size="sm" className="flex-1 bg-white/80 hover:bg-blue-50 border-gray-200 hover:border-blue-300 transition-all duration-200">
                      <Upload className="w-3 h-3 mr-2" />
                      导入模板
                    </Button>
                    <Button variant="outline" size="sm" className="flex-1 bg-white/80 hover:bg-green-50 border-gray-200 hover:border-green-300 transition-all duration-200">
                      <Save className="w-3 h-3 mr-2" />
                      保存模板
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* 图片预览区 */}
            <div>
              <Card className="h-full border-gray-200 shadow-sm">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg text-gray-900">
                      {editMode ? '历史图片预览' : '生成预览'}
                    </CardTitle>
                    {editMode && scenes[currentScene] && (
                      <div className="flex items-center space-x-2">
                        {(scenes[currentScene] as any).hasImages && (
                          <Badge variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">
                            已有 {(scenes[currentScene] as any).imageCount || scenes[currentScene].generatedImages.length} 张图片
                          </Badge>
                        )}
                        {(scenes[currentScene] as any).tasks && (scenes[currentScene] as any).tasks.length > 0 && (
                          <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
                            {(scenes[currentScene] as any).tasks.length} 个任务
                          </Badge>
                        )}
                      </div>
                    )}
                  </div>
                </CardHeader>
                <CardContent className="h-full">
                  <div className="grid grid-cols-2 gap-4 h-full">
                    {(() => {
                      const currentSceneData = scenes[currentScene]
                      if (currentSceneData?.generatedImages?.length > 0) {
                        return currentSceneData.generatedImages.map((image, index) => (
                          <div
                            key={image.id}
                            className="aspect-square bg-gray-100 rounded-lg flex items-center justify-center relative overflow-hidden border-2 border-gray-200 hover:border-blue-300 transition-colors duration-200 cursor-pointer group"
                            onClick={() => openImageModal(image)}
                          >
                            <img 
                              src={image.url} 
                              alt={`Generated image ${index + 1}`}
                              className="w-full h-full object-cover transition-transform duration-200 group-hover:scale-105"
                            />
                            <div className="absolute top-2 right-2 bg-black/60 text-white text-xs px-2 py-1 rounded">
                              {index + 1}
                            </div>
                            {editMode && (image as any).isPrimary && (
                              <div className="absolute top-2 left-2 bg-yellow-500 text-white text-xs px-2 py-1 rounded">
                                主图
                              </div>
                            )}
                            {editMode && (image as any).createdAt && (
                              <div className="absolute bottom-2 left-2 bg-black/60 text-white text-xs px-2 py-1 rounded">
                                {new Date((image as any).createdAt).toLocaleDateString()}
                              </div>
                            )}
                            {editMode && (image as any).taskName && (
                              <div className="absolute bottom-2 right-2 bg-purple-500/80 text-white text-xs px-2 py-1 rounded max-w-[120px] truncate">
                                {(image as any).taskName.replace(/.*镜头/, '镜头')}
                              </div>
                            )}
                            <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center">
                              <span className="text-white text-sm font-medium">点击查看大图</span>
                            </div>
                          </div>
                        ))
                      } else {
                        return Array.from({ length: imageCount }, (_, index) => (
                          <div
                            key={index}
                            className="aspect-square bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg flex items-center justify-center relative overflow-hidden border-2 border-dashed border-gray-300 hover:border-gray-400 transition-colors duration-200"
                          >
                            <div className="text-center text-gray-500">
                              <ImageIcon className="w-8 h-8 mx-auto mb-2" />
                              <p className="text-xs">图片 {index + 1}</p>
                            </div>
                          </div>
                        ))
                      }
                    })()}
                  </div>
                  
                  {(() => {
                    const currentSceneData = scenes[currentScene]
                    return currentSceneData?.generatedImages?.length === 0 && imageCount === 0 && (
                      <div className="h-full flex items-center justify-center text-gray-400">
                        <div className="text-center">
                          <ImageIcon className="w-16 h-16 mx-auto mb-4" />
                          <p>选择生成数量后显示预览区域</p>
                        </div>
                      </div>
                    )
                  })()}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 