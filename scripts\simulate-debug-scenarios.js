#!/usr/bin/env node

/**
 * 模拟不同的调试场景
 * 
 * 这个脚本会模拟前端可能遇到的各种情况，帮助验证调试alert的效果
 */

async function simulateDebugScenarios() {
  console.log('🎭 模拟调试场景');
  console.log('='.repeat(50));
  
  // 场景1: 正常的刷新流程（有完整信息的任务）
  console.log('📋 场景1: 正常刷新流程');
  await testNormalRefresh();
  
  // 场景2: 缺少项目ID或镜头ID
  console.log('\n📋 场景2: 缺少关联信息');
  await testMissingAssociations();
  
  // 场景3: 任务存在但没有Kling任务ID
  console.log('\n📋 场景3: 缺少Kling任务ID');
  await testMissingKlingTaskId();
  
  // 场景4: 数据库中没有找到任务
  console.log('\n📋 场景4: 数据库中无任务');
  await testNoTaskFound();
}

async function testNormalRefresh() {
  try {
    // 使用已知的完成任务进行测试
    const projectId = '4db5bf32-fdda-4bf0-ab45-f8e8315ef302';
    const shotId = '7268a0c0-d00f-470c-a7e6-beca3669474c';
    
    console.log(`   测试项目ID: ${projectId}`);
    console.log(`   测试镜头ID: ${shotId}`);
    
    const response = await fetch(`http://localhost:3001/api/save-video-generation?shotId=${shotId}`);
    
    if (response.ok) {
      const result = await response.json();
      console.log(`   ✅ 查询成功: 找到 ${result.tasks?.length || 0} 个任务`);
      
      if (result.tasks && result.tasks.length > 0) {
        const task = result.tasks[0];
        console.log(`   最新任务状态: ${task.status}`);
        console.log(`   Kling任务ID: ${task.kling_task_id || '无'}`);
        console.log(`   视频数量: ${task.generated_videos?.length || 0}`);
        
        if (task.status === 'completed' && task.generated_videos?.length > 0) {
          console.log('   💡 这种情况下，前端会直接使用数据库中的视频');
        } else if (task.kling_task_id) {
          console.log('   💡 这种情况下，前端会查询Kling API状态');
        } else {
          console.log('   💡 这种情况下，前端会显示"缺少Kling任务ID"的调试信息');
        }
      }
    } else {
      console.log('   ❌ 查询失败');
    }
  } catch (error) {
    console.log(`   ❌ 请求异常: ${error.message}`);
  }
}

async function testMissingAssociations() {
  // 模拟缺少项目ID和镜头ID的情况
  console.log('   模拟场景: 项目ID和镜头ID都为空');
  console.log('   💡 这种情况下，前端会显示详细的缺失信息调试alert');
  console.log('   包含的信息:');
  console.log('   • 场景基本信息');
  console.log('   • 缺失的关键信息列表');
  console.log('   • 镜头数据详情');
  console.log('   • 解决方案建议');
}

async function testMissingKlingTaskId() {
  try {
    // 查找一个没有Kling任务ID的任务
    const response = await fetch('http://localhost:3001/api/save-video-generation?projectId=99833a8b-1d51-40e2-9461-89d3c311847f');
    
    if (response.ok) {
      const result = await response.json();
      
      if (result.tasks && result.tasks.length > 0) {
        const taskWithoutKlingId = result.tasks.find(task => !task.kling_task_id);
        
        if (taskWithoutKlingId) {
          console.log(`   找到测试任务: ${taskWithoutKlingId.id}`);
          console.log(`   任务状态: ${taskWithoutKlingId.status}`);
          console.log(`   错误信息: ${taskWithoutKlingId.error_message || '无'}`);
          console.log('   💡 这种情况下，前端会显示详细的任务状态调试信息');
          console.log('   包含的信息:');
          console.log('   • 任务的完整生命周期时间');
          console.log('   • API请求和响应数据状态');
          console.log('   • 可能的失败原因分析');
          console.log('   • 具体的解决方案');
        } else {
          console.log('   所有任务都有Kling任务ID');
        }
      }
    }
  } catch (error) {
    console.log(`   ❌ 测试异常: ${error.message}`);
  }
}

async function testNoTaskFound() {
  try {
    // 使用一个不存在的镜头ID进行测试
    const fakeshotId = 'fake-shot-id-12345';
    console.log(`   测试不存在的镜头ID: ${fakeshotId}`);
    
    const response = await fetch(`http://localhost:3001/api/save-video-generation?shotId=${fakeshotId}`);
    
    if (response.ok) {
      const result = await response.json();
      console.log(`   查询结果: 找到 ${result.tasks?.length || 0} 个任务`);
      
      if (!result.tasks || result.tasks.length === 0) {
        console.log('   💡 这种情况下，前端会显示"未找到任务"的详细调试信息');
        console.log('   包含的信息:');
        console.log('   • 查询URL和参数');
        console.log('   • 场景和镜头的详细信息');
        console.log('   • 查询参数的状态检查');
        console.log('   • 可能的原因分析');
        console.log('   • 具体的解决步骤');
      }
    }
  } catch (error) {
    console.log(`   ❌ 测试异常: ${error.message}`);
  }
}

// 显示调试功能总结
function showDebugFeaturesSummary() {
  console.log('\n🎯 新增调试功能总结');
  console.log('='.repeat(50));
  
  console.log('📋 1. 刷新按钮点击时（无任务ID）:');
  console.log('   • 显示场景的完整信息');
  console.log('   • 列出所有关联的ID状态');
  console.log('   • 提供具体的解决方案');
  console.log('   • 输出详细日志到控制台');
  
  console.log('\n📋 2. 数据库查询失败时:');
  console.log('   • 显示查询URL和参数');
  console.log('   • 分析错误类型和原因');
  console.log('   • 提供网络和服务器检查建议');
  console.log('   • 输出完整错误堆栈');
  
  console.log('\n📋 3. 未找到相关任务时:');
  console.log('   • 显示查询条件详情');
  console.log('   • 分析镜头和项目关联状态');
  console.log('   • 列出可能的原因');
  console.log('   • 提供创建任务的指导');
  
  console.log('\n📋 4. 缺少Kling任务ID时:');
  console.log('   • 显示任务的完整生命周期');
  console.log('   • 分析API请求响应状态');
  console.log('   • 提供失败原因分析');
  console.log('   • 建议具体的修复步骤');
  
  console.log('\n💡 所有调试信息都会:');
  console.log('   • 同时输出到alert和控制台');
  console.log('   • 包含时间戳和详细上下文');
  console.log('   • 提供可操作的解决方案');
  console.log('   • 便于开发者快速定位问题');
}

// 运行所有测试
async function runAllTests() {
  await simulateDebugScenarios();
  showDebugFeaturesSummary();
  console.log('\n✅ 调试功能测试完成！');
}

runAllTests().catch(console.error);
