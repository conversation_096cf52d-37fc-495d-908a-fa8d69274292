# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Quick Start Commands

```bash
# Install dependencies
npm install

# Start development server with Turbopack
npm run dev

# Build for production
npm run build

# Run linting
npm run lint

# Type checking
npx tsc --noEmit

# Database operations
npx prisma generate        # Generate Prisma client
npx prisma db push        # Push schema changes
npx prisma studio         # Open database GUI
```

## Architecture Overview

**AI短剧生成器** - Next.js 15.4.1 application with AI-powered short drama creation workflow using DeepSeek API. Core 7-step process from script generation to video production.

## Tech Stack

- **Framework**: Next.js 15.4.1 (App Router)
- **Language**: TypeScript
- **Styling**: Tailwind CSS v4 + Radix UI components
- **AI**: DeepSeek API via @ai-sdk/deepseek for script generation, Kling AI for image generation
- **Database**: PostgreSQL via Prisma + Supabase
- **Auth**: Supabase Auth Helpers
- **State**: React hooks with Supabase real-time
- **UI Components**: Custom Radix UI-based design system with gradient styling

## Environment Variables

```bash
# AI Service
DEEPSEEK_API_KEY=your_deepseek_api_key
DEEPSEEK_BASE_URL=https://api.deepseek.com/v1

# Database & Auth
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
DATABASE_URL=your_postgresql_connection_string
```

## Database Schema

**Prisma Models** (PostgreSQL via Supabase):
- `script_projects` - Project metadata 
- `script_shots` - Scene/shot details with AI prompts
- `script_characters` - Character definitions
- `script_generation_configs` - AI generation parameters
- `image_generation_tasks` - Image generation tracking
- `generated_images` - Image storage with metadata
- `user_preferences` - User settings & defaults
- `users` - User authentication data

## Key API Endpoints

**Script Generation**: `POST /api/generate-script`
- Modes: free, keywords, template
- Returns: Zod-validated storyboard JSON
- Uses DeepSeek API for creative generation

**Image Generation**: `POST /api/generate-image`
- Uses Kling AI API for scene visualization
- Supports various styles, sizes, and quality settings

**Auth**: `POST /api/auth/*`
- `/login`, `/register`, `/me`, `/update-profile`
- Uses Supabase Auth Helpers

**Scripts**: `GET|POST /api/scripts`, `GET /api/scripts/[id]`, `POST /api/scripts/save`

## Core Components & Architecture

### 7-Step Workflow (`app/create/page.tsx`)
1. **Story Script** - AI-powered script generation with 3 modes
2. **Image Generation** - Scene visualization with Kling AI
3. **Video Creation** - Image-to-video conversion
4. **Audio** - Voice synthesis and background music
5. **Editing** - Timeline-based video editing
6. **Enhancement** - AI-powered video quality improvement
7. **Subtitles** - Automated subtitle generation

### Script Generation Modes
- **Keywords Mode**: Tag-based generation with predefined categories
- **Template Mode**: Classic story templates (霸道总裁, 校园初恋, etc.)
- **Free Creation**: Custom story outlines with character development

### Key Components
- `ScriptStep.tsx` - Multi-mode script generation with rich UI
- `ImageGenerationStep.tsx` - Advanced image generation with scene management
- `VideoGenerationStep.tsx` - Video creation pipeline
- `AuthContext.tsx` - User authentication state management

### AI Integration Patterns
- **Script Generation**: DeepSeek API with structured output validation
- **Image Generation**: Kling AI with style mapping and prompt engineering
- **State Management**: Real-time updates via Supabase subscriptions

## Development Patterns

### AI Integration
- Uses `@ai-sdk/deepseek` with Zod schemas for structured output validation
- Prompt engineering for different story styles and genres
- Error handling and retry mechanisms for API failures

### State Management
- React hooks for local state
- Supabase real-time for persistent storage
- Optimistic updates with rollback on failure

### UI/UX Design
- 7-step wizard with progress tracking
- Mobile-first responsive design
- Gradient-based visual design system
- Real-time preview and editing capabilities

### Data Flow
1. **Frontend** → **API Routes** → **External AI Services**
2. **Database** → **Prisma** → **Type-safe queries**
3. **Real-time updates** via Supabase subscriptions

## Database Workflow

1. **Schema changes**: Edit `prisma/schema.prisma`
2. **Generate client**: `npx prisma generate`
3. **Apply changes**: `npx prisma db push`
4. **Update types**: `npx supabase gen types typescript --project-id your-project-id > types/supabase.ts`

## Project Structure

```
ai-drama/
├── app/                          # Next.js 15 App Router
│   ├── api/                     # API routes
│   │   ├── generate-script/    # AI script generation
│   │   ├── generate-image/     # AI image generation
│   │   ├── auth/               # Authentication endpoints
│   │   └── scripts/            # Script management
│   ├── create/                 # 7-step workflow wizard
│   ├── gallery/                # User content gallery
│   └── auth/                   # Authentication pages
├── components/                  # Reusable UI components
│   └── ui/                     # Radix UI-based components
├── contexts/                    # React contexts (Auth, etc.)
├── lib/                        # Database clients & utilities
│   ├── prisma.ts              # Prisma client
│   ├── supabase.ts            # Supabase client
│   ├── kling-ai.ts            # Kling AI integration
│   └── prisma-script-operations.ts  # Database operations
├── prisma/                     # Database schema
└── types/                      # TypeScript definitions
```

## Advanced Features

### Script Generation Features
- **Multi-modal creation**: 3 distinct generation modes
- **Character system**: Rich character development with backgrounds
- **Scene management**: Detailed shot-by-shot breakdown
- **Style customization**: Multiple story styles and moods
- **Duration control**: Precise timing for short-form content

### Image Generation Features
- **Scene-based prompts**: Automatic prompt generation from script
- **Style mapping**: Multiple artistic styles (realistic, anime, oil painting, etc.)
- **Quality settings**: From draft to ultra-high quality
- **Batch generation**: Multiple images per scene
- **Advanced parameters**: CFG scale, sampling steps, seed control

### User Experience
- **Progressive enhancement**: Works with partial data
- **Offline capability**: Local storage for draft content
- **Real-time collaboration**: Live updates across sessions
- **Responsive design**: Mobile-first approach
- **Accessibility**: Keyboard navigation and screen reader support

## Common Development Tasks

### Adding New Script Templates
1. Update template definitions in `ScriptStep.tsx`
2. Add corresponding prompt templates in `generate-script/route.ts`
3. Update database schema if needed

### Adding New Image Styles
1. Update style mappings in `kling-ai.ts`
2. Add new style options in `ImageGenerationStep.tsx`
3. Update prompt generation logic

### Database Migrations
1. Modify `prisma/schema.prisma`
2. Run `npx prisma db push` for development
3. Use `npx prisma migrate dev` for production migrations

## Error Handling & Debugging

### Common Issues
- **AI API failures**: Check API keys and rate limits
- **Database connection**: Verify Supabase configuration
- **CORS issues**: Check environment variables
- **Type errors**: Regenerate Prisma client with `npx prisma generate`

### Debug Tools
- **Prisma Studio**: `npx prisma studio`
- **Type checking**: `npx tsc --noEmit`
- **API testing**: Built-in API routes with error responses
- **Real-time debugging**: Supabase dashboard for live queries