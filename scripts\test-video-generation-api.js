#!/usr/bin/env node

/**
 * 测试视频生成 API
 * 
 * 这个脚本用于测试视频生成 API 的数据库保存功能
 */

async function testVideoGenerationAPI() {
  console.log('🎬 测试视频生成 API');
  console.log('='.repeat(50));
  
  const testData = {
    prompt: "测试视频生成提示词 - 城市街道，黄昏时分，平滑的摄像机运动",
    negativePrompt: "低质量, 模糊, 扭曲",
    // 不提供图片，测试纯文本生成
    imageReference: "subject",
    imageFidelity: 0.7,
    duration: 5,
    aspectRatio: "16:9",
    modelName: "kling-v1",
    // 数据库相关字段 (使用有效的UUID格式)
    projectId: "99833a8b-1d51-40e2-9461-89d3c311847f",
    shotId: "7aa27b34-80b6-4379-8ac5-4b01f3357b20",
    userId: "559f045d-5477-443c-92ee-93d03fff3b3c",
    taskName: "API测试 - 视频生成"
  };
  
  console.log('📝 测试数据:', JSON.stringify(testData, null, 2));
  
  try {
    console.log('🌐 调用视频生成 API...');
    const response = await fetch('http://localhost:3001/api/generate-video', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData)
    });
    
    console.log('📊 响应状态:', response.status);
    
    if (response.ok) {
      const result = await response.json();
      console.log('✅ API 调用成功');
      console.log('📋 响应数据:', JSON.stringify(result, null, 2));
    } else {
      const errorData = await response.json();
      console.log('❌ API 调用失败');
      console.log('📋 错误数据:', JSON.stringify(errorData, null, 2));
    }
    
  } catch (error) {
    console.error('❌ 请求失败:', error.message);
  }
  
  // 等待一下，然后检查数据库
  console.log('\n⏳ 等待 2 秒后检查数据库...');
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  await checkDatabase();
}

async function checkDatabase() {
  console.log('\n🗄️  检查数据库中的最新任务');
  console.log('-'.repeat(40));
  
  try {
    const { PrismaClient } = require('../lib/generated/prisma');
    const prisma = new PrismaClient();
    
    await prisma.$connect();
    
    // 获取最新的任务
    const latestTask = await prisma.video_generation_tasks.findFirst({
      include: {
        generated_videos: true
      },
      orderBy: {
        created_at: 'desc'
      }
    });
    
    if (latestTask) {
      console.log('📋 最新任务信息:');
      console.log(`   ID: ${latestTask.id}`);
      console.log(`   任务名称: ${latestTask.task_name}`);
      console.log(`   提示词: ${latestTask.prompt.substring(0, 60)}...`);
      console.log(`   状态: ${latestTask.status}`);
      console.log(`   Kling任务ID: ${latestTask.kling_task_id || '无'}`);
      console.log(`   项目ID: ${latestTask.project_id || '无'}`);
      console.log(`   镜头ID: ${latestTask.shot_id || '无'}`);
      console.log(`   用户ID: ${latestTask.user_id || '无'}`);
      console.log(`   创建时间: ${latestTask.created_at}`);
      if (latestTask.error_message) {
        console.log(`   错误信息: ${latestTask.error_message}`);
      }
    } else {
      console.log('❌ 没有找到任务记录');
    }
    
    await prisma.$disconnect();
    
  } catch (error) {
    console.error('❌ 数据库查询失败:', error.message);
  }
}

// 运行测试
testVideoGenerationAPI().catch(console.error);
