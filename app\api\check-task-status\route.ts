import { NextRequest, NextResponse } from 'next/server'
import { KlingAIClient } from '@/lib/kling-ai'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { taskId } = body

    if (!taskId) {
      return NextResponse.json({
        success: false,
        error: '没有提供任务ID'
      }, { status: 400 })
    }

    console.log('🔍 [CHECK-TASK] 开始查询任务状态...')
    console.log('🔍 [CHECK-TASK] 任务ID:', taskId)

    // 检查环境变量
    const accessKey = process.env.KLING_ACCESS_KEY
    const secretKey = process.env.KLING_SECRET_KEY

    if (!accessKey || !secretKey) {
      console.error('❌ [CHECK-TASK] Kling AI 配置未完成')
      return NextResponse.json({
        success: false,
        error: 'Kling AI 配置未完成'
      }, { status: 500 })
    }

    try {
      // 创建 Kling AI 客户端
      const klingClient = new KlingAIClient({ accessKey, secretKey })

      // 查询任务状态（指定为视频任务）
      console.log('📡 [CHECK-TASK] 调用 Kling AI 查询任务状态...')
      const taskStatus = await klingClient.getTaskStatus(taskId, 'video')

      console.log('✅ [CHECK-TASK] 任务状态查询成功')
      console.log('📊 [CHECK-TASK] 任务状态:', taskStatus.data?.task_status)

      if (taskStatus.code === 0 && taskStatus.data) {
        return NextResponse.json({
          success: true,
          message: '任务状态查询成功',
          data: taskStatus.data
        })
      } else {
        console.error('❌ [CHECK-TASK] Kling AI 返回错误:', taskStatus)
        return NextResponse.json({
          success: false,
          error: taskStatus.message || '查询任务状态失败'
        }, { status: 400 })
      }

    } catch (klingError) {
      console.error('❌ [CHECK-TASK] Kling AI 查询失败:', klingError)
      
      // 解析错误信息
      let errorMessage = 'Kling AI 查询失败'
      if (klingError instanceof Error) {
        errorMessage = klingError.message
        
        // 检查是否是404错误（任务不存在）
        if (errorMessage.includes('404')) {
          errorMessage = '任务不存在或已过期'
        } else if (errorMessage.includes('401')) {
          errorMessage = 'Kling AI 认证失败，请检查配置'
        } else if (errorMessage.includes('403')) {
          errorMessage = 'Kling AI 访问被拒绝'
        } else if (errorMessage.includes('500')) {
          errorMessage = 'Kling AI 服务器错误'
        }
      }

      return NextResponse.json({
        success: false,
        error: errorMessage,
        details: {
          taskId,
          originalError: klingError instanceof Error ? klingError.message : String(klingError)
        }
      }, { status: 500 })
    }

  } catch (error) {
    console.error('❌ [CHECK-TASK] 请求处理失败:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '请求处理失败'
    }, { status: 500 })
  }
}
