import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export interface ScriptShot {
  shotNumber: number
  duration: number
  shotType: string
  location: string
  characters: string[]
  action: string
  dialogue?: string
  cameraMovement: string
  lighting: string
  props?: string[]
  mood: string
  soundEffect: string
  transition: string
}

export interface ScriptData {
  title: string
  totalDuration: number
  style: string
  shotCount: number
  shots: ScriptShot[]
}

export interface SaveScriptRequest {
  userId: string
  title: string
  description?: string
  creationMode: 'keywords' | 'template' | 'free'
  style: string
  duration: number
  scriptContent: string
  scriptData: ScriptData
  generationConfig?: {
    theme?: string
    selectedTags?: string[]
    customKeywords?: string
    selectedMood?: string
    selectedTemplate?: string
    characters?: Array<{
      name: string
      role: string
      background: string
    }>
    backgroundSetting?: string
    character1?: string
    character2?: string
    supportingCharacter?: string
    requiredScenes?: string
    avoidElements?: string
  }
}

export async function POST(request: NextRequest) {
  try {
    const params: SaveScriptRequest = await request.json()

    if (!params.userId) {
      return NextResponse.json({
        success: false,
        error: '用户ID不能为空'
      }, { status: 400 })
    }

    console.log("Saving script via API...")
    
    // 1. 创建剧本项目
    const project = await prisma.script_projects.create({
      data: {
        user_id: params.userId,
        title: params.title,
        description: params.description || '',
        creation_mode: params.creationMode,
        style: params.style,
        duration: params.duration,
        script_content: params.scriptContent,
        script_data: params.scriptData as any,
        status: 'draft'
      }
    })

    const projectId = project.id

    // 2. 保存生成配置
    if (params.generationConfig) {
      const config = params.generationConfig
      await prisma.script_generation_configs.create({
        data: {
          project_id: projectId,
          theme: config.theme,
          selected_tags: config.selectedTags || [],
          custom_keywords: config.customKeywords,
          selected_mood: config.selectedMood,
          selected_template: config.selectedTemplate,
          background_setting: config.backgroundSetting,
          character1: config.character1,
          character2: config.character2,
          supporting_character: config.supportingCharacter,
          required_scenes: config.requiredScenes,
          avoid_elements: config.avoidElements
        }
      })
    }

    // 3. 保存角色信息
    if (params.generationConfig?.characters) {
      const charactersData = params.generationConfig.characters.map((char, index) => ({
        project_id: projectId,
        name: char.name,
        role: char.role,
        background: char.background,
        sort_order: index
      }))
      
      if (charactersData.length > 0) {
        await prisma.script_characters.createMany({
          data: charactersData
        })
      }
    }

    // 4. 保存分镜头信息
    const shotsData = params.scriptData.shots.map((shot, index) => ({
      project_id: projectId,
      shot_number: shot.shotNumber,
      duration: shot.duration,
      shot_type: shot.shotType,
      location: shot.location,
      characters: shot.characters,
      action: shot.action,
      dialogue: shot.dialogue || null,
      camera_movement: shot.cameraMovement,
      lighting: shot.lighting,
      props: shot.props || [],
      mood: shot.mood,
      sound_effect: shot.soundEffect,
      transition: shot.transition
    }))

    await prisma.script_shots.createMany({
      data: shotsData
    })

    return NextResponse.json({
      success: true,
      projectId,
      project
    })

  } catch (error) {
    console.error('Error saving script:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '保存脚本时发生未知错误'
    }, { status: 500 })
  }
}