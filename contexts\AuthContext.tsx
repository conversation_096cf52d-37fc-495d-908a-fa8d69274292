'use client'

import { createContext, useContext, useEffect, useState } from 'react'

interface User {
  id: string
  email: string
  username?: string | null
  avatar_url?: string | null
}

interface DatabaseUser {
  id: string
  email: string
  username?: string | null
  avatar_url?: string | null
  created_at?: string
  updated_at?: string
}

interface AuthContextType {
  user: User | null
  databaseUser: DatabaseUser | null
  loading: boolean
  signIn: (email: string, password: string) => Promise<{ error: Error | null }>
  signUp: (email: string, password: string, username?: string) => Promise<{ error: Error | null }>
  signOut: () => Promise<{ error: Error | null }>
  updateProfile: (updates: Partial<DatabaseUser>) => Promise<{ error: Error | null }>
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  databaseUser: null,
  loading: true,
  signIn: async () => ({ error: new Error('Auth context not initialized') }),
  signUp: async () => ({ error: new Error('Auth context not initialized') }),
  signOut: async () => ({ error: new Error('Auth context not initialized') }),
  updateProfile: async () => ({ error: new Error('Auth context not initialized') }),
})

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: React.ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null)
  const [databaseUser, setDatabaseUser] = useState<DatabaseUser | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // 检查本地token
    const token = localStorage.getItem('auth_token')
    if (token) {
      fetchCurrentUser(token)
    } else {
      setLoading(false)
    }
  }, [])

  const fetchCurrentUser = async (token: string) => {
    try {
      const response = await fetch('/api/auth/me', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setUser(data.user)
        setDatabaseUser(data.user)
      } else {
        localStorage.removeItem('auth_token')
      }
    } catch (error) {
      console.error('Error fetching current user:', error)
      localStorage.removeItem('auth_token')
    } finally {
      setLoading(false)
    }
  }


  const signIn = async (email: string, password: string) => {
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password }),
      })

      const data = await response.json()

      if (!response.ok) {
        return { error: new Error(data.error || 'Login failed') }
      }

      localStorage.setItem('auth_token', data.token)
      setUser(data.user)
      setDatabaseUser(data.user)
      
      return { error: null }
    } catch (error) {
      return { error: new Error('Network error') }
    }
  }

  const signUp = async (email: string, password: string, username?: string) => {
    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email, password, username }),
      })

      const data = await response.json()

      if (!response.ok) {
        return { error: new Error(data.error || 'Registration failed') }
      }

      localStorage.setItem('auth_token', data.token)
      setUser(data.user)
      setDatabaseUser(data.user)
      
      return { error: null }
    } catch (error) {
      return { error: new Error('Network error') }
    }
  }

  const signOut = async () => {
    try {
      localStorage.removeItem('auth_token')
      setUser(null)
      setDatabaseUser(null)
      return { error: null }
    } catch (error) {
      return { error: new Error('Sign out failed') }
    }
  }

  const updateProfile = async (updates: Partial<DatabaseUser>) => {
    if (!user) return { error: new Error('No user logged in') }

    try {
      const token = localStorage.getItem('auth_token')
      if (!token) return { error: new Error('No authentication token') }

      const response = await fetch('/api/auth/update-profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(updates),
      })

      const data = await response.json()

      if (!response.ok) {
        return { error: new Error(data.error || 'Update failed') }
      }

      // Update local state
      setUser(data.user)
      setDatabaseUser(data.user)
      
      return { error: null }
    } catch (error) {
      return { error: new Error('Network error') }
    }
  }

  const value = {
    user,
    databaseUser,
    loading,
    signIn,
    signUp,
    signOut,
    updateProfile,
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}