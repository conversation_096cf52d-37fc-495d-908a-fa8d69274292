#!/usr/bin/env node

/**
 * 环境变量测试脚本
 * 用于验证 .env.local 文件是否正确加载
 */

// 加载环境变量
require('dotenv').config({ path: '.env.local' });
require('dotenv').config(); // 备用加载 .env 文件

console.log('🔧 环境变量测试');
console.log('='.repeat(50));

// 检查必需的环境变量
const requiredEnvVars = [
  'KLING_ACCESS_KEY',
  'KLING_SECRET_KEY'
];

const optionalEnvVars = [
  'NEXT_PUBLIC_SUPABASE_URL',
  'NEXT_PUBLIC_SUPABASE_ANON_KEY',
  'DATABASE_URL'
];

console.log('\n📋 必需的环境变量:');
let allRequired = true;

requiredEnvVars.forEach(varName => {
  const value = process.env[varName];
  if (value) {
    // 只显示前几个字符，保护敏感信息
    const maskedValue = value.length > 10 ? 
      `${value.substring(0, 8)}...${value.substring(value.length - 4)}` : 
      value.substring(0, 8) + '...';
    console.log(`✅ ${varName}: ${maskedValue}`);
  } else {
    console.log(`❌ ${varName}: 未设置`);
    allRequired = false;
  }
});

console.log('\n📋 可选的环境变量:');
optionalEnvVars.forEach(varName => {
  const value = process.env[varName];
  if (value) {
    const maskedValue = value.length > 20 ? 
      `${value.substring(0, 20)}...${value.substring(value.length - 8)}` : 
      value.substring(0, 20) + '...';
    console.log(`✅ ${varName}: ${maskedValue}`);
  } else {
    console.log(`⚪ ${varName}: 未设置`);
  }
});

console.log('\n🔍 环境文件检查:');
const fs = require('fs');
const path = require('path');

const envFiles = ['.env.local', '.env'];
envFiles.forEach(fileName => {
  const filePath = path.join(process.cwd(), fileName);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${fileName}: 存在`);
  } else {
    console.log(`❌ ${fileName}: 不存在`);
  }
});

console.log('\n📊 测试结果:');
if (allRequired) {
  console.log('✅ 所有必需的环境变量都已正确设置');
  console.log('🚀 可以运行 Kling AI 测试脚本了！');
  
  console.log('\n💡 建议的下一步:');
  console.log('   npm run test:kling          # 运行基础测试');
  console.log('   npm run kling:interactive   # 运行交互式管理器');
  
} else {
  console.log('❌ 缺少必需的环境变量');
  console.log('\n🔧 解决方法:');
  console.log('1. 在项目根目录创建 .env.local 文件');
  console.log('2. 添加以下内容:');
  console.log('   KLING_ACCESS_KEY=your_access_key');
  console.log('   KLING_SECRET_KEY=your_secret_key');
  console.log('3. 重新运行此测试脚本');
}

console.log('\n' + '='.repeat(50));

// 如果提供了task_id参数，则查询特定任务
const taskId = process.argv[2];
if (taskId && allRequired) {
  console.log('\n🔍 查询特定任务详情...');
  querySpecificTask(taskId);
}

// 查询特定任务的函数
async function querySpecificTask(taskId) {
  const jwt = require('jsonwebtoken');

  try {
    console.log(`📋 任务ID: ${taskId}`);

    // 生成JWT Token
    const token = jwt.sign(
      {
        iss: process.env.KLING_ACCESS_KEY,
        exp: Math.floor(Date.now() / 1000) + 1800, // 30分钟有效期
        nbf: Math.floor(Date.now() / 1000) - 5     // 5秒前开始生效
      },
      process.env.KLING_SECRET_KEY,
      { algorithm: 'HS256' }
    );

    // 查询任务详情
    const url = `https://api-beijing.klingai.com/v1/videos/image2video/${taskId}`;
    console.log(`🔗 请求URL: ${url}`);

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    console.log(`📡 HTTP状态: ${response.status} ${response.statusText}`);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ 请求失败:', errorText);
      return;
    }

    const data = await response.json();
    console.log('✅ 查询成功');

    // 格式化显示任务信息
    console.log('\n📊 任务详情:');
    console.log('='.repeat(40));
    console.log(`📋 任务ID: ${data.data.task_id}`);
    console.log(`📊 状态: ${data.data.task_status}`);
    console.log(`📝 状态消息: ${data.data.task_status_msg || '无'}`);
    console.log(`⏰ 创建时间: ${new Date(data.data.created_at * 1000).toLocaleString()}`);
    console.log(`🔄 更新时间: ${new Date(data.data.updated_at * 1000).toLocaleString()}`);

    if (data.data.task_result && data.data.task_result.videos) {
      console.log(`🎬 生成视频数量: ${data.data.task_result.videos.length}`);
      data.data.task_result.videos.forEach((video, index) => {
        console.log(`   视频 ${index + 1}: ${video.url}`);
      });
    }

    if (data.data.task_info) {
      console.log('📋 任务信息:');
      console.log(JSON.stringify(data.data.task_info, null, 2));
    }

    console.log('='.repeat(40));

    // 生成对应的curl命令
    console.log('\n💡 对应的curl命令:');
    console.log(`curl -X GET "${url}" \\`);
    console.log(`  -H "Authorization: Bearer ${token.substring(0, 20)}..." \\`);
    console.log(`  -H "Content-Type: application/json"`);

  } catch (error) {
    console.error('❌ 查询任务失败:', error.message);
  }
}
