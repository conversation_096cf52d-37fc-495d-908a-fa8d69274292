<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI视频剪辑工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f5f5;
            color: #333;
        }

        .header {
            background: white;
            padding: 12px 20px;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 600;
        }

        .logo-icon {
            width: 24px;
            height: 24px;
            background: #00d4aa;
            border-radius: 4px;
        }

        .header-actions {
            display: flex;
            gap: 12px;
            align-items: center;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
        }

        .btn-primary {
            background: #00d4aa;
            color: white;
        }

        .btn-secondary {
            background: #f0f0f0;
            color: #666;
        }

        .main-container {
            display: flex;
            height: calc(100vh - 60px);
        }

        .sidebar {
            width: 240px;
            background: white;
            border-right: 1px solid #e0e0e0;
            padding: 20px;
        }

        .sidebar-section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #666;
        }

        .video-item {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 12px;
            margin-bottom: 15px;
            border: 1px solid #e9ecef;
        }

        .video-thumbnail {
            width: 100%;
            height: 120px;
            background: #333;
            border-radius: 6px;
            margin-bottom: 10px;
            position: relative;
            overflow: hidden;
        }

        .video-thumbnail img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .video-info {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
        }

        .video-actions {
            display: flex;
            gap: 8px;
        }

        .btn-small {
            padding: 4px 8px;
            font-size: 12px;
            border-radius: 4px;
        }

        .content-area {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .content-header {
            background: white;
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .content-title {
            font-size: 18px;
            font-weight: 600;
        }

        .workspace {
            flex: 1;
            padding: 20px;
            display: flex;
            gap: 20px;
        }

        .preview-area {
            flex: 1;
            background: white;
            border-radius: 8px;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 400px;
        }

        .preview-placeholder {
            width: 100%;
            max-width: 600px;
            height: 300px;
            background: #f0f0f0;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 16px;
        }

        .timeline-area {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
        }

        .timeline-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .timeline-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .timeline-track {
            height: 80px;
            background: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #e9ecef;
            display: flex;
            align-items: center;
            padding: 10px;
            gap: 5px;
        }

        .timeline-clip {
            height: 60px;
            background: #00d4aa;
            border-radius: 4px;
            flex-shrink: 0;
            position: relative;
            overflow: hidden;
        }

        .timeline-clip img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .properties-panel {
            width: 300px;
            background: white;
            border-radius: 8px;
            padding: 20px;
        }

        .property-group {
            margin-bottom: 20px;
        }

        .property-label {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 8px;
        }

        .property-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }

        .toggle-switch {
            position: relative;
            width: 44px;
            height: 24px;
            background: #00d4aa;
            border-radius: 12px;
            cursor: pointer;
        }

        .toggle-switch::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            top: 2px;
            right: 2px;
            transition: all 0.2s;
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="logo">
            <div class="logo-icon"></div>
            <span>Soko</span>
            <span style="color: #999; margin-left: 10px;">剪辑项目</span>
        </div>
        <div class="header-actions">
            <button class="btn btn-secondary">保存</button>
            <button class="btn btn-primary">导出视频</button>
            <div style="display: flex; gap: 8px; align-items: center;">
                <span>🔔</span>
                <span>📁</span>
                <span>⚙️</span>
                <div style="width: 32px; height: 32px; background: #00d4aa; border-radius: 50%;"></div>
            </div>
        </div>
    </header>

    <div class="main-container">
        <!-- 左侧边栏 -->
        <aside class="sidebar">
            <div class="sidebar-section">
                <div class="section-title">📁 媒体库</div>
                
                <div class="video-item">
                    <div class="video-thumbnail">
                        <div style="background: linear-gradient(45deg, #ff6b35, #f7931e); width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; color: white;">
                            🎬
                        </div>
                    </div>
                    <div class="video-info">
                        📹 Soko<br>
                        时长: 00:15 | 分辨率: 1920x1080
                    </div>
                    <div class="video-actions">
                        <button class="btn btn-primary btn-small">添加到时间轴</button>
                    </div>
                </div>

                <div class="video-item">
                    <div class="video-thumbnail">
                        <div style="background: linear-gradient(45deg, #4ecdc4, #44a08d); width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; color: white;">
                            🎭
                        </div>
                    </div>
                    <div class="video-info">
                        📹 Soko<br>
                        时长: 00:23 | 分辨率: 1920x1080
                    </div>
                    <div class="video-actions">
                        <button class="btn btn-primary btn-small">智能剪辑/生成动态内容</button>
                    </div>
                </div>
            </div>

            <div class="sidebar-section">
                <div class="section-title">🎵 音频</div>
                <div style="padding: 10px; background: #f8f9fa; border-radius: 6px; font-size: 12px; color: #666;">
                    拖拽音频文件到此处或点击上传
                </div>
            </div>
        </aside>

        <!-- 主内容区域 -->
        <main class="content-area">
            <div class="content-header">
                <h1 class="content-title">智能剪辑 - AI内容生成项目</h1>
                <div class="header-actions">
                    <button class="btn btn-secondary">预览</button>
                    <button class="btn btn-primary">生成内容</button>
                </div>
            </div>

            <div class="workspace">
                <div style="flex: 1;">
                    <!-- 预览区域 -->
                    <div class="preview-area">
                        <div class="preview-placeholder">
                            <div style="text-align: center;">
                                <div style="font-size: 48px; margin-bottom: 10px;">🎬</div>
                                <div>视频预览区域</div>
                                <div style="font-size: 12px; color: #ccc; margin-top: 5px;">选择素材开始编辑</div>
                            </div>
                        </div>
                    </div>

                    <!-- 时间轴区域 -->
                    <div class="timeline-area">
                        <div class="timeline-header">
                            <div class="section-title">时间轴</div>
                            <div class="timeline-controls">
                                <div class="toggle-switch"></div>
                                <span style="font-size: 12px; color: #666;">智能模式</span>
                            </div>
                        </div>
                        
                        <div class="timeline-track">
                            <div class="timeline-clip" style="width: 100px;">
                                <div style="background: linear-gradient(45deg, #ff6b35, #f7931e); width: 100%; height: 100%;"></div>
                            </div>
                            <div class="timeline-clip" style="width: 120px;">
                                <div style="background: linear-gradient(45deg, #4ecdc4, #44a08d); width: 100%; height: 100%;"></div>
                            </div>
                            <div class="timeline-clip" style="width: 80px;">
                                <div style="background: linear-gradient(45deg, #a8e6cf, #7fcdcd); width: 100%; height: 100%;"></div>
                            </div>
                            <div class="timeline-clip" style="width: 90px;">
                                <div style="background: linear-gradient(45deg, #ffd93d, #ff6b6b); width: 100%; height: 100%;"></div>
                            </div>
                            <div class="timeline-clip" style="width: 110px;">
                                <div style="background: linear-gradient(45deg, #a8e6cf, #88d8c0); width: 100%; height: 100%;"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 右侧属性面板 -->
                <div class="properties-panel">
                    <div class="section-title">属性面板</div>
                    
                    <div class="property-group">
                        <div class="property-label">视频设置</div>
                        <input type="text" class="property-input" placeholder="标题" value="AI生成内容">
                        <br><br>
                        <input type="text" class="property-input" placeholder="描述">
                    </div>

                    <div class="property-group">
                        <div class="property-label">AI参数</div>
                        <input type="range" style="width: 100%;" min="0" max="100" value="75">
                        <div style="font-size: 12px; color: #666; margin-top: 5px;">创意度: 75%</div>
                    </div>

                    <div class="property-group">
                        <div class="property-label">输出设置</div>
                        <select class="property-input">
                            <option>1920x1080 (HD)</option>
                            <option>3840x2160 (4K)</option>
                        </select>
                        <br><br>
                        <select class="property-input">
                            <option>MP4</option>
                            <option>MOV</option>
                            <option>AVI</option>
                        </select>
                    </div>

                    <div class="property-group">
                        <button class="btn btn-primary" style="width: 100%;">应用AI增强</button>
                    </div>
                </div>
            </div>
        </main>
    </div>
</body>
</html>
