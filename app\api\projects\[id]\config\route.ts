import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET /api/projects/[id]/config - 获取项目的生成配置
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const projectId = params.id

    const config = await prisma.script_generation_configs.findFirst({
      where: {
        project_id: projectId
      }
    })

    if (!config) {
      return NextResponse.json({
        success: false,
        error: '项目配置不存在'
      }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      config: {
        theme: config.theme,
        selectedTags: config.selected_tags || [],
        customKeywords: config.custom_keywords,
        selectedMood: config.selected_mood,
        selectedTemplate: config.selected_template,
        backgroundSetting: config.background_setting,
        character1: config.character1,
        character2: config.character2,
        supportingCharacter: config.supporting_character,
        requiredScenes: config.required_scenes,
        avoidElements: config.avoid_elements
      }
    })

  } catch (error) {
    console.error('Error fetching project config:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '获取项目配置时发生未知错误'
    }, { status: 500 })
  }
}
