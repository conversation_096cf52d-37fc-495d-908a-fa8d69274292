-- 创建视频生成任务表
CREATE TABLE IF NOT EXISTS public.video_generation_tasks (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  project_id uuid,
  shot_id uuid,
  user_id uuid,
  task_name character varying,
  prompt text NOT NULL,
  negative_prompt text,
  model_name character varying DEFAULT 'kling-v1',
  aspect_ratio character varying DEFAULT '16:9',
  duration integer DEFAULT 5,
  image_reference character varying DEFAULT 'subject',
  image_fidelity numeric DEFAULT 0.5,
  base_image_url text,
  status character varying DEFAULT 'pending',
  kling_task_id character varying,
  api_request_payload jsonb,
  api_response_data jsonb,
  error_message text,
  error_code character varying,
  created_at timestamp with time zone DEFAULT now(),
  started_at timestamp with time zone,
  completed_at timestamp with time zone,
  updated_at timestamp with time zone DEFAULT now(),
  CONSTRAINT video_generation_tasks_pkey PRIMARY KEY (id),
  CONSTRAINT video_generation_tasks_kling_task_id_key UNIQUE (kling_task_id),
  CONSTRAINT video_generation_tasks_duration_check CHECK (duration >= 1 AND duration <= 10),
  CONSTRAINT video_generation_tasks_image_fidelity_check CHECK (image_fidelity >= 0 AND image_fidelity <= 1),
  CONSTRAINT video_generation_tasks_status_check CHECK (status IN ('pending', 'queued', 'processing', 'completed', 'failed', 'cancelled')),
  CONSTRAINT video_generation_tasks_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.script_projects(id),
  CONSTRAINT video_generation_tasks_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id),
  CONSTRAINT video_generation_tasks_shot_id_fkey FOREIGN KEY (shot_id) REFERENCES public.script_shots(id)
);

-- 创建生成视频表
CREATE TABLE IF NOT EXISTS public.generated_videos (
  id uuid NOT NULL DEFAULT gen_random_uuid(),
  task_id uuid,
  video_url text NOT NULL,
  video_filename character varying,
  video_size_bytes bigint,
  video_width integer,
  video_height integer,
  video_format character varying DEFAULT 'mp4',
  video_duration numeric,
  quality_score numeric,
  storage_provider character varying DEFAULT 'kling',
  storage_path text,
  cdn_url text,
  generation_time_seconds numeric,
  metadata jsonb,
  created_at timestamp with time zone DEFAULT now(),
  CONSTRAINT generated_videos_pkey PRIMARY KEY (id),
  CONSTRAINT generated_videos_task_id_fkey FOREIGN KEY (task_id) REFERENCES public.video_generation_tasks(id)
);

-- 创建索引以提高查询性能
CREATE INDEX IF NOT EXISTS idx_video_generation_tasks_project_id ON public.video_generation_tasks(project_id);
CREATE INDEX IF NOT EXISTS idx_video_generation_tasks_shot_id ON public.video_generation_tasks(shot_id);
CREATE INDEX IF NOT EXISTS idx_video_generation_tasks_user_id ON public.video_generation_tasks(user_id);
CREATE INDEX IF NOT EXISTS idx_video_generation_tasks_kling_task_id ON public.video_generation_tasks(kling_task_id);
CREATE INDEX IF NOT EXISTS idx_video_generation_tasks_status ON public.video_generation_tasks(status);
CREATE INDEX IF NOT EXISTS idx_video_generation_tasks_created_at ON public.video_generation_tasks(created_at);

CREATE INDEX IF NOT EXISTS idx_generated_videos_task_id ON public.generated_videos(task_id);
CREATE INDEX IF NOT EXISTS idx_generated_videos_created_at ON public.generated_videos(created_at);

-- 添加注释
COMMENT ON TABLE public.video_generation_tasks IS '视频生成任务表';
COMMENT ON TABLE public.generated_videos IS '生成的视频表';

COMMENT ON COLUMN public.video_generation_tasks.id IS '任务唯一标识';
COMMENT ON COLUMN public.video_generation_tasks.project_id IS '关联的项目ID';
COMMENT ON COLUMN public.video_generation_tasks.shot_id IS '关联的镜头ID';
COMMENT ON COLUMN public.video_generation_tasks.user_id IS '用户ID';
COMMENT ON COLUMN public.video_generation_tasks.task_name IS '任务名称';
COMMENT ON COLUMN public.video_generation_tasks.prompt IS '视频生成提示词';
COMMENT ON COLUMN public.video_generation_tasks.negative_prompt IS '负面提示词';
COMMENT ON COLUMN public.video_generation_tasks.model_name IS '使用的模型名称';
COMMENT ON COLUMN public.video_generation_tasks.aspect_ratio IS '视频宽高比';
COMMENT ON COLUMN public.video_generation_tasks.duration IS '视频时长（秒）';
COMMENT ON COLUMN public.video_generation_tasks.image_reference IS '图片参考类型';
COMMENT ON COLUMN public.video_generation_tasks.image_fidelity IS '图片保真度';
COMMENT ON COLUMN public.video_generation_tasks.base_image_url IS '基础图片URL';
COMMENT ON COLUMN public.video_generation_tasks.status IS '任务状态';
COMMENT ON COLUMN public.video_generation_tasks.kling_task_id IS 'Kling AI任务ID';
COMMENT ON COLUMN public.video_generation_tasks.api_request_payload IS 'API请求参数';
COMMENT ON COLUMN public.video_generation_tasks.api_response_data IS 'API响应数据';
COMMENT ON COLUMN public.video_generation_tasks.error_message IS '错误信息';
COMMENT ON COLUMN public.video_generation_tasks.error_code IS '错误代码';

COMMENT ON COLUMN public.generated_videos.id IS '视频唯一标识';
COMMENT ON COLUMN public.generated_videos.task_id IS '关联的任务ID';
COMMENT ON COLUMN public.generated_videos.video_url IS '视频URL';
COMMENT ON COLUMN public.generated_videos.video_filename IS '视频文件名';
COMMENT ON COLUMN public.generated_videos.video_size_bytes IS '视频文件大小（字节）';
COMMENT ON COLUMN public.generated_videos.video_width IS '视频宽度';
COMMENT ON COLUMN public.generated_videos.video_height IS '视频高度';
COMMENT ON COLUMN public.generated_videos.video_format IS '视频格式';
COMMENT ON COLUMN public.generated_videos.video_duration IS '视频时长';
COMMENT ON COLUMN public.generated_videos.quality_score IS '质量评分';
COMMENT ON COLUMN public.generated_videos.storage_provider IS '存储提供商';
COMMENT ON COLUMN public.generated_videos.storage_path IS '存储路径';
COMMENT ON COLUMN public.generated_videos.cdn_url IS 'CDN URL';
COMMENT ON COLUMN public.generated_videos.generation_time_seconds IS '生成耗时（秒）';
COMMENT ON COLUMN public.generated_videos.metadata IS '元数据';
