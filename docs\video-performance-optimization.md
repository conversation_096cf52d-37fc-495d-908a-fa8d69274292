# 图生视频性能优化实现

## 优化概述

本次优化主要解决了图生视频功能中的性能问题，通过在主页预加载数据库数据来提升页面响应速度。

## 实现的优化

### 1. 创建视频任务状态API路由
- **文件**: `app/api/video-tasks/[id]/route.ts`
- **功能**: 根据任务ID查询视频生成状态
- **支持**: 
  - Kling Task ID 查询
  - 数据库任务ID查询
  - 包含生成的视频信息

### 2. 主页预加载视频任务数据
- **文件**: `app/create/page.tsx`
- **新增状态**:
  ```typescript
  const [videoTasksData, setVideoTasksData] = useState<{[key: string]: any}>({})
  const [videoTasksLoading, setVideoTasksLoading] = useState(false)
  ```
- **新增函数**:
  - `loadVideoTasksData()`: 预加载项目的所有视频任务
  - `refreshVideoTasksForShot()`: 刷新特定镜头的视频任务数据

### 3. 优化VideoGenerationStep组件
- **文件**: `app/create/components/VideoGenerationStep.tsx`
- **新增Props**:
  ```typescript
  preloadedVideoTasks?: {[key: string]: any}
  videoTasksLoading?: boolean
  onVideoTasksUpdate?: (tasks: {[key: string]: any}) => void
  onRefreshVideoTasks?: (shotId: string) => Promise<void>
  ```
- **优化逻辑**:
  - 优先使用预加载的数据
  - 减少数据库查询次数
  - 实时更新缓存数据

### 4. 数据缓存机制
- **缓存结构**: `{[shotId: string]: VideoTask[]}`
- **缓存更新**: 
  - 视频生成成功后自动更新缓存
  - 任务状态变更后同步更新缓存
  - 支持单个镜头的缓存刷新

## 性能提升效果

### 优化前
1. 进入图生视频页面时需要查询数据库
2. 每个镜头的状态查询都是独立的API调用
3. 页面初始化较慢，用户体验不佳

### 优化后
1. 主页加载时预先获取所有视频任务数据
2. 图生视频页面直接使用缓存数据，响应更快
3. 批量处理任务状态查询，减少API调用次数
4. 实时缓存更新，保持数据一致性

## 数据流程

```mermaid
graph TD
    A[用户进入主页] --> B[loadProject]
    B --> C[loadVideoTasksData 预加载]
    C --> D[缓存视频任务数据]
    D --> E[用户进入图生视频页面]
    E --> F[使用预加载数据]
    F --> G[processPreloadedVideoTasks]
    G --> H[更新前端显示]
    
    I[视频生成成功] --> J[更新缓存]
    J --> K[同步前端状态]
```

## API调用优化

### 优化前的调用流程
1. 进入VideoGenerationStep → 查询数据库
2. 自动刷新 → 批量查询数据库
3. 手动刷新 → 单独查询数据库

### 优化后的调用流程
1. 主页加载 → 一次性预加载所有数据
2. 进入VideoGenerationStep → 直接使用缓存
3. 状态更新 → 同步更新缓存和前端

## 使用方法

### 开发者使用
1. 主页会自动预加载视频任务数据
2. VideoGenerationStep组件会优先使用预加载数据
3. 缓存会在任务状态变更时自动更新

### 用户体验
1. 图生视频页面加载更快
2. 镜头状态显示更及时
3. 减少了等待时间

## 注意事项

1. 预加载数据会增加主页的初始加载时间，但总体提升了用户体验
2. 缓存数据需要与数据库保持同步，已实现自动更新机制
3. 大项目的视频任务数据可能较多，已按镜头分组优化存储结构

## 后续优化建议

1. 可以考虑添加缓存过期机制
2. 实现更细粒度的缓存更新策略
3. 添加缓存大小限制，避免内存占用过多
4. 考虑使用Web Workers处理大量数据的预加载
