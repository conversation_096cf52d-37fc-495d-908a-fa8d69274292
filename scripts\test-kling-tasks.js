// 加载环境变量
require('dotenv').config({ path: '.env.local' });
require('dotenv').config(); // 备用加载 .env 文件

const jwt = require('jsonwebtoken');

// Kling AI API 配置
const KLING_CONFIG = {
  baseUrl: 'https://api-beijing.klingai.com',
  accessKey: process.env.KLING_ACCESS_KEY,
  secretKey: process.env.KLING_SECRET_KEY
};

// 生成JWT Token
function generateJWTToken() {
  if (!KLING_CONFIG.accessKey || !KLING_CONFIG.secretKey) {
    throw new Error('请设置 KLING_ACCESS_KEY 和 KLING_SECRET_KEY 环境变量');
  }

  const headers = {
    alg: "HS256",
    typ: "JWT"
  };
  
  const payload = {
    iss: KLING_CONFIG.accessKey,
    exp: Math.floor(Date.now() / 1000) + 1800, // 有效时间30分钟
    nbf: Math.floor(Date.now() / 1000) - 5 // 开始生效时间（当前时间-5秒）
  };
  
  const token = jwt.sign(payload, KLING_CONFIG.secretKey, { 
    algorithm: 'HS256',
    header: headers
  });
  
  return token;
}

// 查询任务列表
async function getTaskList(options = {}) {
  const token = generateJWTToken();

  // 构建查询参数
  const params = new URLSearchParams();

  // 可选参数 - 使用新的参数名
  if (options.status) params.append('status', options.status);
  if (options.page) params.append('pageNum', options.page.toString());
  if (options.size) params.append('pageSize', options.size.toString());
  if (options.start_time) params.append('start_time', options.start_time);
  if (options.end_time) params.append('end_time', options.end_time);

  const url = `${KLING_CONFIG.baseUrl}/v1/videos/image2video?${params.toString()}`;

  console.log('🔍 [KLING-TEST] 查询任务列表...');
  console.log('🔍 [KLING-TEST] URL:', url);
  console.log('🔍 [KLING-TEST] 查询参数:', Object.fromEntries(params));
  
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });
    
    console.log('📡 [KLING-TEST] HTTP状态:', response.status, response.statusText);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ [KLING-TEST] 请求失败:', errorText);
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }
    
    const data = await response.json();
    console.log('✅ [KLING-TEST] 查询成功');

    // 打印完整的JSON响应体
    console.log('📄 [KLING-TEST] 完整响应体:');
    console.log(JSON.stringify(data, null, 2));

    return data;
    
  } catch (error) {
    console.error('❌ [KLING-TEST] 查询任务列表失败:', error);
    throw error;
  }
}

// 查询单个任务详情
async function getTaskDetail(taskId) {
  const token = generateJWTToken();
  const url = `${KLING_CONFIG.baseUrl}/v1/videos/image2video/${taskId}`;

  console.log('🔍 [KLING-TEST] 查询任务详情...');
  console.log('🔍 [KLING-TEST] Task ID:', taskId);
  console.log('🔍 [KLING-TEST] URL:', url);
  
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });
    
    console.log('📡 [KLING-TEST] HTTP状态:', response.status, response.statusText);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ [KLING-TEST] 请求失败:', errorText);
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }
    
    const data = await response.json();
    console.log('✅ [KLING-TEST] 查询成功');

    // 打印完整的JSON响应体
    console.log('📄 [KLING-TEST] 完整响应体:');
    console.log(JSON.stringify(data, null, 2));

    return data;
    
  } catch (error) {
    console.error('❌ [KLING-TEST] 查询任务详情失败:', error);
    throw error;
  }
}

// 格式化显示任务信息
function displayTaskInfo(task) {
  console.log('📋 [TASK-INFO] ==========================================');
  console.log('📋 [TASK-INFO] 任务ID:', task.task_id);
  console.log('📋 [TASK-INFO] 状态:', task.task_status);
  console.log('📋 [TASK-INFO] 状态消息:', task.task_status_msg || '无');
  console.log('📋 [TASK-INFO] 创建时间:', new Date(task.created_at * 1000).toLocaleString());
  console.log('📋 [TASK-INFO] 更新时间:', new Date(task.updated_at * 1000).toLocaleString());
  
  if (task.task_result && task.task_result.videos) {
    console.log('📋 [TASK-INFO] 生成视频数量:', task.task_result.videos.length);
    task.task_result.videos.forEach((video, index) => {
      console.log(`📋 [TASK-INFO] 视频 ${index + 1}:`, video.url);
    });
  }
  
  if (task.task_info) {
    console.log('📋 [TASK-INFO] 任务信息:', JSON.stringify(task.task_info, null, 2));
  }
  
  console.log('📋 [TASK-INFO] ==========================================');
}

// 主测试函数
async function main() {
  try {
    console.log('🚀 [KLING-TEST] 开始测试 Kling AI 任务查询功能');
    console.log('🚀 [KLING-TEST] ==========================================');
    
    // 测试1: 查询最近的任务列表
    console.log('\n📝 [TEST-1] 查询最近的任务列表 (最多10个)');
    const taskList = await getTaskList({
      size: 10,
      page: 1
    });
    
    console.log('\n' + '='.repeat(60));
    console.log('📊 [RESULT] 任务列表结果解析:');
    console.log('📊 [RESULT] 返回码:', taskList.code);
    console.log('📊 [RESULT] 消息:', taskList.message || '无');
    
    if (taskList.data && taskList.data.tasks) {
      console.log('📊 [RESULT] 任务总数:', taskList.data.total || taskList.data.tasks.length);
      console.log('📊 [RESULT] 当前页任务数:', taskList.data.tasks.length);
      
      // 显示每个任务的基本信息
      taskList.data.tasks.forEach((task, index) => {
        console.log(`\n📋 [TASK-${index + 1}] ID: ${task.task_id}`);
        console.log(`📋 [TASK-${index + 1}] 状态: ${task.task_status}`);
        console.log(`📋 [TASK-${index + 1}] 创建时间: ${new Date(task.created_at * 1000).toLocaleString()}`);
      });
      
      // 测试2: 查询第一个任务的详细信息
      if (taskList.data.tasks.length > 0) {
        const firstTaskId = taskList.data.tasks[0].task_id;
        console.log(`\n📝 [TEST-2] 查询任务详情: ${firstTaskId}`);
        
        const taskDetail = await getTaskDetail(firstTaskId);
        console.log('\n' + '='.repeat(60));
        console.log('📊 [RESULT] 任务详情结果解析:');
        console.log('📊 [RESULT] 返回码:', taskDetail.code);
        
        if (taskDetail.data) {
          displayTaskInfo(taskDetail.data);
        }
      }
    } else {
      console.log('📊 [RESULT] 没有找到任务数据');
    }
    
    // 测试3: 查询特定状态的任务
    console.log('\n📝 [TEST-3] 查询成功状态的任务');
    const successTasks = await getTaskList({
      status: 'succeed',
      size: 5
    });
    
    console.log('\n' + '='.repeat(60));
    console.log('📊 [RESULT] 成功任务结果解析:');
    console.log('📊 [RESULT] 返回码:', successTasks.code);
    if (successTasks.data && successTasks.data.tasks) {
      console.log('📊 [RESULT] 成功任务数量:', successTasks.data.tasks.length);
    }
    
    console.log('\n✅ [KLING-TEST] 测试完成!');
    
  } catch (error) {
    console.error('❌ [KLING-TEST] 测试失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = {
  getTaskList,
  getTaskDetail,
  displayTaskInfo
};
