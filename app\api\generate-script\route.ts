import { createDeepSeek } from '@ai-sdk/deepseek';
import { generateObject } from 'ai';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';

// 创建自定义 DeepSeek 实例
const deepseek = createDeepSeek({
  apiKey: process.env.DEEPSEEK_API_KEY ?? '',
  baseURL: process.env.DEEPSEEK_BASE_URL ?? 'https://api.deepseek.com/v1',
});

// 定义分镜头脚本的 Zod schema
const scriptSchema = z.object({
  title: z.string(),
  totalDuration: z.number(),
  style: z.string(),
  shotCount: z.number(),
  shots: z.array(
    z.object({
      shotNumber: z.number(),
      duration: z.number(),
      shotType: z.string(),
      location: z.string(),
      characters: z.array(z.string()),
      action: z.string(),
      dialogue: z.string().optional(),
      cameraMovement: z.string(),
      lighting: z.string(),
      props: z.array(z.string()).optional(),
      mood: z.string(),
      soundEffect: z.string(),
      transition: z.string(),
    })
  ),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { 
      creationMode, 
      theme, 
      style, 
      duration, 
      character1,
      character2,
      supportingCharacter,
      requiredScenes,
      avoidElements,
      // 关键词生成模式的参数
      selectedTags,
      customKeywords,
      selectedMood,
      // 模板选择模式的参数
      selectedTemplate,
      characters,
      backgroundSetting
    } = body;

    // 根据创作模式构建不同的prompt
    let prompt = '';
    
    if (creationMode === 'free') {
      prompt = buildFreeCreationPrompt({
        theme,
        style,
        duration,
        character1,
        character2,
        supportingCharacter,
        requiredScenes,
        avoidElements
      });
    } else if (creationMode === 'keywords') {
      prompt = buildKeywordsPrompt({ 
        theme, 
        style, 
        duration, 
        selectedTags, 
        customKeywords, 
        selectedMood 
      });
    } else if (creationMode === 'template') {
      prompt = buildTemplatePrompt({ 
        selectedTemplate, 
        style, 
        duration, 
        characters, 
        backgroundSetting 
      });
    }

    if (!prompt) {
      return NextResponse.json(
        { error: '不支持的创作模式' },
        { status: 400 }
      );
    }

    const { object } = await generateObject({
      system: "你是专业的影视分镜师，请根据用户的需求，创作一个完整的分镜头脚本。外围不允许有 \```json 和 \```，这不是 JSON 的一部分，只返回json数据。",
      model: deepseek('deepseek-v3-250324'),
      schema: scriptSchema,
      prompt: prompt,
      temperature: 1,
    });

    return NextResponse.json({ script: JSON.stringify(object, null, 2) });
  } catch (error) {
    console.error('脚本生成失败:', error);
    return NextResponse.json(
      { error: '脚本生成失败，请重试' },
      { status: 500 }
    );
  }
}

function buildFreeCreationPrompt({
  theme,
  style,
  duration,
  character1,
  character2,
  supportingCharacter,
  requiredScenes,
  avoidElements
}: {
  theme: string;
  style: string;
  duration: number;
  character1: string;
  character2: string;
  supportingCharacter: string;
  requiredScenes: string;
  avoidElements: string;
}) {
  const styleMap: { [key: string]: string } = {
    romantic: '浪漫爱情',
    comedy: '轻松喜剧',
    thriller: '悬疑紧张',
    action: '动作刺激',
    drama: '情感剧情'
  };

  const selectedStyle = styleMap[style] || '情感剧情';
  
  // 根据时长计算镜头数量 (每个镜头5-10秒)
  const shotCount = Math.ceil(duration / 7.5); // duration现在是秒，直接除以平均镜头时长7.5秒
  const durationInMinutes = duration / 60; // 转换为分钟用于显示

  return `你是一个专业的影视分镜师，请根据以下要求为${durationInMinutes.toFixed(1)}分钟的${selectedStyle}风格短剧设计分镜头脚本。

# 创作方式
**自由创作模式** - 基于用户提供的详细故事大纲和角色设定进行创作

# 项目信息

## 基本设定
- **故事主题**: ${theme}
- **影片风格**: ${selectedStyle}
- **总时长**: ${duration}秒 (${durationInMinutes.toFixed(1)}分钟)
- **预计镜头数**: ${shotCount}个左右

## 角色信息
${character1 ? `- **主角1**: ${character1}` : ''}
${character2 ? `- **主角2**: ${character2}` : ''}
${supportingCharacter ? `- **配角/反派**: ${supportingCharacter}` : ''}

## 特殊要求
${requiredScenes ? `- **必须包含场景**: ${requiredScenes}` : ''}
${avoidElements ? `- **避免元素**: ${avoidElements}` : ''}

# 分镜设计原则

## 镜头类型说明
- **特写(CU)**: 突出情感表达
- **中景(MS)**: 展现人物互动
- **全景(LS)**: 建立环境氛围
- **近景(CS)**: 平衡情感与环境

## 时长分配
- 每个镜头控制在5-10秒
- 情感高潮镜头可适当延长
- 快节奏场景镜头更短促
- 总时长严格控制在${duration}秒内 (${durationInMinutes.toFixed(1)}分钟)

## 技术要求
1. **镜头连贯性**: 确保镜头间逻辑顺畅
2. **情绪递进**: 每个镜头都要推进故事情感
3. **视觉节奏**: 根据${selectedStyle}风格调整镜头节奏
4. **实用性**: 考虑实际拍摄的可行性

## 风格适配
- **${selectedStyle}风格**: 镜头语言要符合该风格特点
- **色调建议**: 根据情绪设定合适的色彩基调
- **音效配合**: 每个镜头的音效要服务于整体氛围

请基于以上要求，创作一个完整的分镜头脚本。为${durationInMinutes.toFixed(1)}分钟的短剧设计合适的标题，并创建约${shotCount}个镜头的详细分镜头方案。

注意：总时长应该严格控制在${duration}秒内，每个镜头的时长请以秒为单位。totalDuration字段应该填写${duration}。`;
}

function buildKeywordsPrompt({
  theme,
  style,
  duration,
  selectedTags,
  customKeywords,
  selectedMood
}: {
  theme: string;
  style: string;
  duration: number;
  selectedTags?: string[];
  customKeywords?: string;
  selectedMood?: string;
}) {
  const styleMap: { [key: string]: string } = {
    romantic: '浪漫爱情',
    comedy: '轻松喜剧',
    thriller: '悬疑紧张',
    action: '动作刺激',
    drama: '情感剧情'
  };

  const selectedStyle = styleMap[style] || '情感剧情';
  const shotCount = Math.ceil(duration / 7.5); // duration现在是秒
  const durationInMinutes = duration / 60; // 转换为分钟用于显示

  // 处理关键词标签
  const allKeywords = [];
  if (selectedTags && selectedTags.length > 0) {
    allKeywords.push(...selectedTags);
  }
  if (customKeywords && customKeywords.trim()) {
    allKeywords.push(...customKeywords.split(',').map(k => k.trim()).filter(k => k));
  }

  return `你是一个专业的影视分镜师，请根据关键词生成${durationInMinutes.toFixed(1)}分钟的${selectedStyle}风格短剧分镜头脚本。

# 创作方式
**关键词生成模式** - 基于用户选择的关键词标签和情感基调进行创作

# 项目信息

## 基本设定
- **核心主题**: ${theme}
- **影片风格**: ${selectedStyle}
- **总时长**: ${duration}秒 (${durationInMinutes.toFixed(1)}分钟)
- **预计镜头数**: ${shotCount}个左右

## 关键词标签
${allKeywords.length > 0 ? `- **选择标签**: ${allKeywords.join('、')}` : ''}

## 情感基调
${selectedMood ? `- **情感基调**: ${selectedMood}` : ''}

# 创作要求
请围绕"${theme}"这个核心主题，结合以下要素创作一个符合${selectedStyle}风格的短剧：

${allKeywords.length > 0 ? `## 关键词融入
- 请将选中的关键词标签（${allKeywords.join('、')}）自然融入到故事情节中
- 确保这些元素与故事主题和谐统一，不显突兀` : ''}

${selectedMood ? `## 情感基调要求
- 整体情感基调应该体现"${selectedMood}"的特点
- 镜头语言和节奏要配合这种情感表达` : ''}

## 分镜设计原则
- 每个镜头控制在5-10秒
- 镜头类型包括：特写(CU)、中景(MS)、全景(LS)、近景(CS)
- 确保镜头间逻辑顺畅，情绪递进自然
- 根据${selectedStyle}风格和${selectedMood || '整体基调'}调整镜头节奏和氛围

请创作一个完整的分镜头脚本，为故事设计合适的标题、场景和角色，创建约${shotCount}个镜头的详细分镜头方案。

注意：总时长应该严格控制在${duration}秒内，每个镜头的时长请以秒为单位。totalDuration字段应该填写${duration}。`;
}

function buildTemplatePrompt({
  selectedTemplate,
  style,
  duration,
  characters,
  backgroundSetting
}: {
  selectedTemplate: string;
  style: string;
  duration: number;
  characters?: Array<{id: string, name: string, role: string, background: string}>;
  backgroundSetting?: string;
}) {
  const styleMap: { [key: string]: string } = {
    romantic: '浪漫爱情',
    comedy: '轻松喜剧',
    thriller: '悬疑紧张',
    action: '动作刺激',
    drama: '情感剧情'
  };

  const selectedStyle = styleMap[style] || '情感剧情';
  const shotCount = Math.ceil(duration / 7.5); // duration现在是秒
  const durationInMinutes = duration / 60; // 转换为分钟用于显示

  // 个性化设置部分
  const characterInfo = characters && characters.length > 0 && characters.some(c => c.name.trim()) 
    ? `\n- **角色设定**: ${characters.filter(c => c.name.trim()).map(c => {
        const characterDesc = `${c.name}(${c.role})`
        return c.background.trim() ? `${characterDesc} - ${c.background}` : characterDesc
      }).join('、')}` 
    : '';
  const backgroundInfo = backgroundSetting ? `\n- **背景设定**: ${backgroundSetting}` : '';

  return `你是一个专业的影视分镜师，请根据模板创建${durationInMinutes.toFixed(1)}分钟的${selectedStyle}风格短剧分镜头脚本。

# 创作方式
**模板选择模式** - 基于用户选择的经典剧本模板进行创作

# 项目信息
- **选择模板**: ${selectedTemplate}
- **影片风格**: ${selectedStyle}
- **总时长**: ${duration}秒 (${durationInMinutes.toFixed(1)}分钟)
- **预计镜头数**: ${shotCount}个左右${characterInfo}${backgroundInfo}

# 创作要求
请根据"${selectedTemplate}"这个模板类型，创作一个符合${selectedStyle}风格的经典短剧。

## 模板特色融入
- 严格按照"${selectedTemplate}"的经典结构和情节发展
- 采用该模板标志性的角色设定和冲突设计
- 融入该模板类型的经典桥段和转折点
- 确保故事情节完整且引人入胜

${characters && characters.length > 0 && characters.some(c => c.name.trim()) ? `## 角色个性化
- 角色设定：${characters.filter(c => c.name.trim()).map(c => {
    const characterDesc = `${c.name}(${c.role})`
    return c.background.trim() ? `${characterDesc} - ${c.background}` : characterDesc
  }).join('、')}
- 请在剧本中使用这些角色名称，角色性格和行为要结合其背景设定
- 确保每个角色的言行举止都符合其身份背景和${selectedTemplate}模板的经典人设` : ''}

${backgroundSetting ? `## 背景环境设定
- 故事背景设定在"${backgroundSetting}"
- 场景描述和环境氛围要与此背景设定相符
- 道具和场景元素要贴合这个环境特色` : ''}

## 分镜设计原则
- 每个镜头控制在5-10秒
- 镜头类型包括：特写(CU)、中景(MS)、全景(LS)、近景(CS)
- 确保镜头间逻辑顺畅，情绪递进自然
- 根据${selectedStyle}风格和${selectedTemplate}特色调整镜头节奏和氛围

请创作一个完整的分镜头脚本，基于"${selectedTemplate}"设计合适的标题、场景和角色，创建约${shotCount}个镜头的详细分镜头方案。

注意：总时长应该严格控制在${duration}秒内，每个镜头的时长请以秒为单位。totalDuration字段应该填写${duration}。`;
}