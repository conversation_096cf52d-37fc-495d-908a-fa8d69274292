import { prisma } from '@/lib/prisma'

export interface ImageGenerationTaskData {
  projectId?: string
  shotId?: string
  userId?: string
  taskName?: string
  prompt: string
  negativePrompt?: string
  modelName?: string
  aspectRatio?: string
  imageCount?: number
  imageFidelity?: number
  humanFidelity?: number
  cfgScale?: number
  seed?: bigint | string
  status?: string
  klingTaskId?: string
  apiRequestPayload?: any
  referenceImageUrl?: string
  referenceImageStrength?: number
  referenceImageObjectName?: string
}

export interface GeneratedImageData {
  taskId: string
  imageUrl: string
  imageFilename?: string
  imageSizeBytes?: bigint | number
  imageWidth?: number
  imageHeight?: number
  imageFormat?: string
  qualityScore?: number
  isPrimary?: boolean
  storagePath?: string
  minioObjectName?: string
  minioBucketName?: string
  cdnUrl?: string
  cdnBaseUrl?: string
  generationTimeSeconds?: number
  actualPrompt?: string
  metadata?: any
}

export async function createImageGenerationTask(data: ImageGenerationTaskData) {
  const taskData: any = {
    prompt: data.prompt,
    negative_prompt: data.negativePrompt,
    model_name: data.modelName || 'kling-v1-5',
    aspect_ratio: data.aspectRatio || '16:9',
    image_count: data.imageCount || 1,
    image_fidelity: data.imageFidelity || 0.5,
    human_fidelity: data.humanFidelity || 0.45,
    cfg_scale: data.cfgScale || 7.5,
    status: data.status || 'pending',
    kling_task_id: data.klingTaskId,
    api_request_payload: data.apiRequestPayload,
  }

  // Only add optional fields if they are provided
  if (data.projectId) taskData.project_id = data.projectId
  if (data.shotId) taskData.shot_id = data.shotId
  if (data.userId) taskData.user_id = data.userId
  if (data.taskName) taskData.task_name = data.taskName
  if (data.seed) taskData.seed = BigInt(data.seed)
  if (data.referenceImageUrl) taskData.reference_image_url = data.referenceImageUrl
  if (data.referenceImageStrength) taskData.reference_image_strength = data.referenceImageStrength
  if (data.referenceImageObjectName) taskData.reference_image_object_name = data.referenceImageObjectName

  return await prisma.image_generation_tasks.create({
    data: taskData
  })
}

export async function updateImageGenerationTask(
  taskId: string, 
  data: Partial<ImageGenerationTaskData> & { 
    startedAt?: Date
    completedAt?: Date
    apiResponseData?: any
    errorMessage?: string
    errorCode?: string
  }
) {
  const updateData: any = {
    updated_at: new Date(),
  }

  // Only add fields that are provided
  if (data.status) updateData.status = data.status
  if (data.klingTaskId) updateData.kling_task_id = data.klingTaskId
  if (data.apiResponseData) updateData.api_response_data = data.apiResponseData
  if (data.startedAt) updateData.started_at = data.startedAt
  if (data.completedAt) updateData.completed_at = data.completedAt
  if (data.errorMessage) updateData.error_message = data.errorMessage
  if (data.errorCode) updateData.error_code = data.errorCode
  if (data.referenceImageUrl) updateData.reference_image_url = data.referenceImageUrl
  if (data.referenceImageStrength) updateData.reference_image_strength = data.referenceImageStrength
  if (data.referenceImageObjectName) updateData.reference_image_object_name = data.referenceImageObjectName

  return await prisma.image_generation_tasks.update({
    where: { id: taskId },
    data: updateData
  })
}

export async function createGeneratedImage(data: GeneratedImageData) {
  const imageData: any = {
    task_id: data.taskId,
    image_url: data.imageUrl,
    image_filename: data.imageFilename,
    image_width: data.imageWidth,
    image_height: data.imageHeight,
    image_format: data.imageFormat || 'jpg',
    quality_score: data.qualityScore,
    is_primary: data.isPrimary || false,
    storage_path: data.storagePath,
    minio_object_name: data.minioObjectName,
    minio_bucket_name: data.minioBucketName || 'images',
    cdn_url: data.cdnUrl,
    cdn_base_url: data.cdnBaseUrl,
    generation_time_seconds: data.generationTimeSeconds,
    actual_prompt: data.actualPrompt,
    metadata: data.metadata,
  }

  // Handle bigint conversion for imageSizeBytes
  if (data.imageSizeBytes) {
    imageData.image_size_bytes = BigInt(data.imageSizeBytes)
  }

  return await prisma.generated_images.create({
    data: imageData
  })
}

export async function getImageGenerationTasksByProject(projectId: string) {
  return await prisma.image_generation_tasks.findMany({
    where: { project_id: projectId },
    include: {
      generated_images: true
    },
    orderBy: {
      created_at: 'desc'
    }
  })
}

export async function getGeneratedImagesByTask(taskId: string) {
  return await prisma.generated_images.findMany({
    where: { task_id: taskId },
    orderBy: {
      created_at: 'asc'
    }
  })
}

export async function getGeneratedImagesByProject(projectId: string) {
  return await prisma.generated_images.findMany({
    where: {
      image_generation_tasks: {
        project_id: projectId
      }
    },
    include: {
      image_generation_tasks: true
    },
    orderBy: {
      created_at: 'desc'
    }
  })
}

export async function updateGeneratedImage(
  imageId: string,
  data: Partial<GeneratedImageData>
) {
  return await prisma.generated_images.update({
    where: { id: imageId },
    data: {
      ...data,
      updated_at: new Date()
    }
  })
}

export async function deleteGeneratedImage(imageId: string) {
  return await prisma.generated_images.delete({
    where: { id: imageId }
  })
}

export async function getImageGenerationTaskByKlingTaskId(klingTaskId: string) {
  return await prisma.image_generation_tasks.findUnique({
    where: { kling_task_id: klingTaskId },
    include: {
      generated_images: true
    }
  })
}