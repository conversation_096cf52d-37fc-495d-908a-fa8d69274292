"use client"

import { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Slider } from '@/components/ui/slider'
import { Video, Settings, RefreshCw } from 'lucide-react'

export default function TestVideoSettingsPage() {
  // 视频设置状态
  const [videoRatio, setVideoRatio] = useState("16:9")
  const [videoQuality, setVideoQuality] = useState("1080p")
  const [fps, setFps] = useState("30")
  const [transitionEffect, setTransitionEffect] = useState("fade")
  const [animationStrength, setAnimationStrength] = useState([50])
  const [motionScale, setMotionScale] = useState([30])
  const [seed, setSeed] = useState("")
  const [videoPrompt, setVideoPrompt] = useState("Smooth camera movement through a busy city street at golden hour, cinematic lighting, urban landscape with gentle motion")

  return (
    <div className="container mx-auto p-6 max-w-7xl">
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Settings className="w-5 h-5" />
            <span>视频生成设置页面布局测试</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600">
            这个页面展示了新的视频生成设置布局，将原来分离的"生成设置"整合到"视频提示词编辑"区域，并重命名为"视频生成设置"。
          </p>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 左侧：视频生成设置 */}
        <Card className="h-full border-gray-200 shadow-sm">
          <CardHeader>
            <CardTitle className="text-lg text-gray-900 flex items-center space-x-2">
              <Settings className="w-5 h-5" />
              <span>视频生成设置</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* 视频提示词编辑 */}
            <div>
              <Label className="text-base font-medium mb-2 block">运动描述</Label>
              <Textarea
                placeholder="描述画面中的运动、相机移动、物体动作等..."
                value={videoPrompt}
                onChange={(e) => setVideoPrompt(e.target.value)}
                className="h-32 resize-none bg-gradient-to-r from-purple-50 to-pink-50 border-purple-200 focus:border-purple-400"
              />
            </div>

            {/* 视频设置区域 - 统一背景框 */}
            <div className="bg-gradient-to-r from-gray-50 to-slate-50 rounded-lg border border-gray-200 p-4 space-y-3">
              {/* 基础设置 */}
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <Label className="text-sm font-medium mb-1 block">视频比例</Label>
                  <Select value={videoRatio} onValueChange={setVideoRatio}>
                    <SelectTrigger className="h-9 bg-white border-gray-200 hover:border-gray-300 focus:border-purple-400">
                      <SelectValue className="text-gray-600" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="16:9">16:9 (横屏)</SelectItem>
                      <SelectItem value="9:16">9:16 (竖屏)</SelectItem>
                      <SelectItem value="1:1">1:1 (正方形)</SelectItem>
                      <SelectItem value="4:3">4:3 (传统)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label className="text-sm font-medium mb-1 block">视频质量</Label>
                  <Select value={videoQuality} onValueChange={setVideoQuality}>
                    <SelectTrigger className="h-9 bg-white border-gray-200 hover:border-gray-300 focus:border-purple-400">
                      <SelectValue className="text-gray-600" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="720p">720p HD</SelectItem>
                      <SelectItem value="1080p">1080p Full HD</SelectItem>
                      <SelectItem value="4k">4K Ultra HD</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-3">
                <div>
                  <Label className="text-sm font-medium mb-1 block">帧率</Label>
                  <Select value={fps} onValueChange={setFps}>
                    <SelectTrigger className="h-9 bg-white border-gray-200 hover:border-gray-300 focus:border-purple-400">
                      <SelectValue className="text-gray-600" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="24">24 FPS (电影)</SelectItem>
                      <SelectItem value="30">30 FPS (标准)</SelectItem>
                      <SelectItem value="60">60 FPS (高帧率)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label className="text-sm font-medium mb-1 block">转场效果</Label>
                  <Select value={transitionEffect} onValueChange={setTransitionEffect}>
                    <SelectTrigger className="h-9 bg-white border-gray-200 hover:border-gray-300 focus:border-purple-400">
                      <SelectValue className="text-gray-600" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="fade">🌅 淡入淡出</SelectItem>
                      <SelectItem value="slide">➡️ 滑动切换</SelectItem>
                      <SelectItem value="zoom">🔍 缩放效果</SelectItem>
                      <SelectItem value="dissolve">✨ 溶解过渡</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* 动画设置 */}
              <div>
                <Label className="text-sm font-medium mb-1 block">动画强度</Label>
                <div className="px-3 py-2 bg-gradient-to-r from-blue-50 to-indigo-50 rounded border border-blue-200">
                  <Slider
                    value={animationStrength}
                    onValueChange={setAnimationStrength}
                    max={100}
                    min={0}
                    step={10}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-gray-600 mt-1">
                    <span>静态</span>
                    <span className="font-bold text-blue-700 bg-white px-2 py-0.5 rounded-full text-xs">{animationStrength[0]}%</span>
                    <span>动态</span>
                  </div>
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium mb-1 block">运动幅度</Label>
                <div className="px-3 py-2 bg-gradient-to-r from-green-50 to-emerald-50 rounded border border-green-200">
                  <Slider
                    value={motionScale}
                    onValueChange={setMotionScale}
                    max={100}
                    min={0}
                    step={10}
                    className="w-full"
                  />
                  <div className="flex justify-between text-xs text-gray-600 mt-1">
                    <span>微动</span>
                    <span className="font-bold text-green-700 bg-white px-2 py-0.5 rounded-full text-xs">{motionScale[0]}%</span>
                    <span>大幅</span>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-3">
                <div>
                  <Label className="text-sm font-medium mb-1 block">随机种子 (可选)</Label>
                  <Input
                    placeholder="留空随机生成"
                    value={seed}
                    onChange={(e) => setSeed(e.target.value)}
                    className="h-9 bg-white border-gray-200 focus:border-purple-400"
                  />
                </div>

                <div>
                  <Label className="text-sm font-medium mb-1 block">
                    视频时长
                    <span className="text-xs text-gray-500 ml-1">(统一5秒)</span>
                  </Label>
                  <div className="relative">
                    <Input
                      type="number"
                      value={5}
                      readOnly
                      className="h-9 bg-gradient-to-r from-green-50 to-emerald-50 border-green-200 text-gray-600 cursor-not-allowed"
                      title="时长已统一设置为5秒"
                    />
                    <div className="absolute right-2 top-1/2 transform -translate-y-1/2">
                      <span className="text-xs bg-green-100 text-green-700 px-1.5 py-0.5 rounded-full">
                        5秒
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 右侧：视频预览 */}
        <Card className="h-full border-gray-200 shadow-sm">
          <CardHeader>
            <CardTitle className="text-lg text-gray-900">视频预览</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="aspect-video bg-gray-100 rounded-lg flex items-center justify-center">
              <div className="text-center text-gray-500">
                <Video className="w-16 h-16 mx-auto mb-4" />
                <p>视频预览区域</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 底部说明 */}
      <Card className="mt-6">
        <CardContent className="pt-6">
          <div className="text-center">
            <h3 className="font-medium text-gray-900 mb-2">布局改进说明</h3>
            <p className="text-sm text-gray-600">
              ✅ 整合设置 📦 统一背景框 📱 紧凑间距 🎨 统一风格
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
