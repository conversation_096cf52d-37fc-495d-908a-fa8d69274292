# DeepSeek V3 API 配置说明

## 1. 获取 API Key

1. 访问 [DeepSeek 官网](https://www.deepseek.com/)
2. 注册账户并登录
3. 前往 API 密钥管理页面
4. 创建新的 API 密钥并复制

## 2. 环境变量配置

在项目根目录创建 `.env.local` 文件：

```bash
# DeepSeek API配置
DEEPSEEK_API_KEY=your_actual_deepseek_api_key_here
```

**注意**：
- 请将 `your_actual_deepseek_api_key_here` 替换为你的真实 API 密钥
- `.env.local` 文件不会被 git 跟踪，确保密钥安全
- 重启开发服务器以使环境变量生效

## 3. 功能说明

目前已实现：
- ✅ 自由创作模式的脚本生成
- ✅ 基于用户输入的角色设定、故事大纲等生成完整剧本
- ✅ 支持多种风格选择（浪漫、喜剧、悬疑、动作、剧情）
- ✅ 实时生成状态显示和错误处理

## 4. 使用方法

1. 选择"自由创作"模式
2. 填写故事大纲（必填）
3. 设置角色信息
4. 选择故事风格（必填）
5. 调整时长和角色数量
6. 点击"生成自由创作剧本"按钮

## 5. 下一步计划

- [ ] 接入关键词生成模式
- [ ] 接入模板选择模式
- [ ] 优化 prompt 提升生成质量
- [ ] 添加更多自定义选项

## 6. 故障排除

如果遇到问题：
1. 检查 API 密钥是否正确设置
2. 确认网络连接正常
3. 查看浏览器控制台错误信息
4. 重启开发服务器

---

配置完成后，即可体验 AI 驱动的短剧脚本生成功能！ 