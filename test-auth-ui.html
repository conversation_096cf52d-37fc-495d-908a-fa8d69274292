<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI短剧生成器 - 认证界面预览</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    animation: {
                        'pulse': 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'bounce': 'bounce 1s infinite',
                        'ping': 'ping 1s cubic-bezier(0, 0, 0.2, 1) infinite',
                    }
                }
            }
        }
    </script>
    <style>
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: .5; }
        }
        @keyframes bounce {
            0%, 100% { transform: translateY(-25%); animation-timing-function: cubic-bezier(0.8, 0, 1, 1); }
            50% { transform: translateY(0); animation-timing-function: cubic-bezier(0, 0, 0.2, 1); }
        }
        @keyframes ping {
            75%, 100% { transform: scale(2); opacity: 0; }
        }
    </style>
</head>
<body class="bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-4xl font-bold text-center mb-8 bg-gradient-to-r from-violet-600 via-purple-600 to-blue-600 bg-clip-text text-transparent">
            AI短剧生成器 - UI预览
        </h1>
        
        <!-- 认证表单预览 -->
        <div class="max-w-md mx-auto mb-12">
            <div class="w-full max-w-md mx-auto">
                <!-- 头部装饰 -->
                <div class="text-center mb-8">
                    <div class="relative inline-flex items-center justify-center w-20 h-20 mb-6">
                        <!-- 背景光晕效果 -->
                        <div class="absolute inset-0 bg-gradient-to-br from-violet-500 via-purple-500 via-blue-500 to-cyan-500 rounded-3xl blur-lg opacity-60 animate-pulse"></div>
                        <!-- 主图标容器 -->
                        <div class="relative bg-gradient-to-br from-violet-600 via-purple-600 via-blue-600 to-cyan-600 rounded-3xl p-4 shadow-2xl transform hover:scale-105 transition-all duration-300">
                            <svg class="w-8 h-8 text-white drop-shadow-lg" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                            </svg>
                            <!-- 装饰性星星 -->
                            <div class="absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full animate-ping"></div>
                            <div class="absolute -bottom-1 -left-1 w-2 h-2 bg-pink-400 rounded-full animate-bounce delay-300"></div>
                        </div>
                    </div>
                    <h1 class="text-3xl font-bold bg-gradient-to-r from-violet-600 via-purple-600 via-blue-600 to-cyan-600 bg-clip-text text-transparent mb-2 tracking-tight">
                        AI短剧生成器
                    </h1>
                    <p class="text-gray-600 text-lg font-medium">创造属于你的精彩故事</p>
                    <div class="flex items-center justify-center gap-1 mt-2 opacity-70">
                        <div class="w-1 h-1 bg-violet-400 rounded-full animate-pulse"></div>
                        <div class="w-1 h-1 bg-purple-400 rounded-full animate-pulse delay-100"></div>
                        <div class="w-1 h-1 bg-blue-400 rounded-full animate-pulse delay-200"></div>
                        <div class="w-1 h-1 bg-cyan-400 rounded-full animate-pulse delay-300"></div>
                    </div>
                </div>

                <!-- 标签页 -->
                <div class="grid w-full grid-cols-2 mb-8 bg-gradient-to-r from-gray-50 via-white to-gray-50 backdrop-blur-xl p-1.5 rounded-2xl shadow-lg border border-gray-200/50">
                    <button class="rounded-xl bg-gradient-to-r from-violet-500 to-purple-600 text-white shadow-lg shadow-purple-500/25 transition-all duration-300 font-medium py-3 px-4">
                        <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        登录
                    </button>
                    <button class="rounded-xl font-medium py-3 px-4 hover:bg-gray-50 transition-all duration-300">
                        <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                        </svg>
                        注册
                    </button>
                </div>

                <!-- 登录表单 -->
                <div class="border-0 shadow-2xl bg-white/90 backdrop-blur-xl relative overflow-hidden rounded-2xl">
                    <!-- 背景装饰 -->
                    <div class="absolute inset-0 bg-gradient-to-br from-violet-50/50 via-purple-50/30 to-blue-50/50"></div>
                    <div class="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-violet-200/20 to-transparent rounded-full blur-2xl"></div>
                    <div class="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-blue-200/20 to-transparent rounded-full blur-xl"></div>
                    
                    <div class="text-center p-6 relative z-10">
                        <h2 class="text-2xl font-bold text-gray-800 mb-2">欢迎回来</h2>
                        <p class="text-gray-600 text-base">请输入您的账号信息登录</p>
                    </div>
                    
                    <div class="space-y-6 p-6 relative z-10">
                        <!-- 邮箱输入 -->
                        <div class="space-y-3">
                            <label class="text-sm font-semibold text-gray-700 flex items-center gap-2">
                                <svg class="w-4 h-4 text-violet-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                </svg>
                                邮箱地址
                            </label>
                            <div class="relative group">
                                <div class="absolute inset-0 bg-gradient-to-r from-violet-500/10 to-purple-500/10 rounded-xl blur-sm group-focus-within:blur-none group-focus-within:from-violet-500/20 group-focus-within:to-purple-500/20 transition-all duration-300"></div>
                                <input type="email" placeholder="请输入您的邮箱" class="relative w-full h-12 pl-4 pr-4 border-2 border-gray-200/80 focus:border-violet-400 focus:ring-4 focus:ring-violet-400/20 rounded-xl transition-all duration-300 bg-white/80 backdrop-blur-sm hover:border-violet-300 font-medium placeholder:text-gray-400 focus:outline-none">
                            </div>
                        </div>

                        <!-- 密码输入 -->
                        <div class="space-y-3">
                            <label class="text-sm font-semibold text-gray-700 flex items-center gap-2">
                                <svg class="w-4 h-4 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                </svg>
                                密码
                            </label>
                            <div class="relative group">
                                <div class="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-blue-500/10 rounded-xl blur-sm group-focus-within:blur-none group-focus-within:from-purple-500/20 group-focus-within:to-blue-500/20 transition-all duration-300"></div>
                                <input type="password" placeholder="请输入您的密码" class="relative w-full h-12 pl-4 pr-12 border-2 border-gray-200/80 focus:border-purple-400 focus:ring-4 focus:ring-purple-400/20 rounded-xl transition-all duration-300 bg-white/80 backdrop-blur-sm hover:border-purple-300 font-medium placeholder:text-gray-400 focus:outline-none">
                                <button type="button" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-purple-600 transition-all duration-200 p-1 rounded-lg hover:bg-purple-50">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <!-- 登录按钮 -->
                        <button class="w-full h-14 bg-gradient-to-r from-violet-600 via-purple-600 to-blue-600 hover:from-violet-700 hover:via-purple-700 hover:to-blue-700 text-white font-semibold rounded-xl shadow-xl hover:shadow-2xl hover:shadow-purple-500/25 transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98] relative overflow-hidden group">
                            <!-- 按钮光效 -->
                            <div class="absolute inset-0 bg-gradient-to-r from-white/20 via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                            <div class="relative flex items-center justify-center">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                立即登录
                            </div>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 用户菜单预览 -->
        <div class="max-w-sm mx-auto">
            <h2 class="text-2xl font-bold text-center mb-6 text-gray-800">用户菜单预览</h2>
            <div class="bg-white/90 backdrop-blur-xl rounded-2xl shadow-2xl p-6">
                <!-- 用户头像按钮 -->
                <div class="flex justify-center mb-6">
                    <button class="relative h-12 w-12 rounded-full hover:bg-gradient-to-br hover:from-violet-50 hover:to-purple-50 transition-all duration-300 ring-2 ring-transparent hover:ring-violet-200/50 hover:shadow-lg group">
                        <div class="h-10 w-10 shadow-lg ring-2 ring-white group-hover:ring-violet-200 transition-all duration-300 rounded-full bg-gradient-to-br from-violet-500 via-purple-500 via-blue-500 to-cyan-500 flex items-center justify-center text-white font-bold text-sm mx-auto">
                            U
                        </div>
                        <!-- 在线状态指示器 -->
                        <div class="absolute -bottom-0.5 -right-0.5 w-3.5 h-3.5 bg-emerald-500 border-2 border-white rounded-full shadow-sm">
                            <div class="w-full h-full bg-emerald-400 rounded-full animate-ping opacity-75"></div>
                        </div>
                        <!-- VIP标识 -->
                        <div class="absolute -top-1 -left-1 w-4 h-4 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center shadow-sm">
                            <svg class="w-2.5 h-2.5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                        </div>
                    </button>
                </div>

                <!-- 下拉菜单内容 -->
                <div class="w-full bg-white/95 backdrop-blur-xl border-gray-200/50 shadow-2xl rounded-2xl p-3">
                    <!-- 用户信息 -->
                    <div class="font-normal p-4 bg-gradient-to-r from-violet-50 via-purple-50 to-blue-50 rounded-xl mb-3 relative overflow-hidden">
                        <!-- 背景装饰 -->
                        <div class="absolute top-0 right-0 w-16 h-16 bg-gradient-to-bl from-violet-200/30 to-transparent rounded-full blur-xl"></div>
                        <div class="absolute bottom-0 left-0 w-12 h-12 bg-gradient-to-tr from-blue-200/30 to-transparent rounded-full blur-lg"></div>
                        
                        <div class="flex items-center space-x-4 relative z-10">
                            <div class="relative">
                                <div class="h-12 w-12 ring-2 ring-white shadow-lg rounded-full bg-gradient-to-br from-violet-500 via-purple-500 via-blue-500 to-cyan-500 flex items-center justify-center text-white font-bold">
                                    U
                                </div>
                                <!-- VIP徽章 -->
                                <div class="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center shadow-md">
                                    <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="flex flex-col space-y-1.5 flex-1">
                                <p class="text-sm font-bold text-gray-900 leading-none">用户名</p>
                                <p class="text-xs text-gray-500 leading-none"><EMAIL></p>
                                <div class="flex items-center gap-1 mt-1">
                                    <div class="w-1.5 h-1.5 bg-emerald-400 rounded-full"></div>
                                    <span class="text-xs text-emerald-600 font-medium">在线</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 菜单项 -->
                    <div class="space-y-1">
                        <div class="rounded-xl p-3 hover:bg-gradient-to-r hover:from-violet-50 hover:to-purple-50 transition-all duration-200 cursor-pointer group flex items-center">
                            <svg class="mr-3 h-4 w-4 text-violet-500 group-hover:text-violet-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            <span class="text-gray-700 font-medium group-hover:text-gray-900">个人资料</span>
                        </div>
                        <div class="rounded-xl p-3 hover:bg-gradient-to-r hover:from-blue-50 hover:to-cyan-50 transition-all duration-200 cursor-pointer group flex items-center">
                            <svg class="mr-3 h-4 w-4 text-blue-500 group-hover:text-blue-600 transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            <span class="text-gray-700 font-medium group-hover:text-gray-900">设置</span>
                        </div>
                        <div class="rounded-xl p-3 hover:bg-gradient-to-r hover:from-red-50 hover:to-pink-50 transition-all duration-200 cursor-pointer text-red-600 hover:text-red-700 group flex items-center">
                            <svg class="mr-3 h-4 w-4 group-hover:scale-110 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                            </svg>
                            <span class="font-medium">退出登录</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
