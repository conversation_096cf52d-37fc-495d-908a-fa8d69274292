import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

interface RouteParams {
  params: Promise<{ id: string }>
}

// GET /api/scripts/[id] - 获取单个剧本详情
export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  const { id } = await params
  try {
    const script = await prisma.script_projects.findUnique({
      where: {
        id: id
      },
      include: {
        script_characters: {
          orderBy: {
            sort_order: 'asc'
          }
        },
        script_shots: {
          orderBy: {
            shot_number: 'asc'
          }
        },
        script_generation_configs: true
      }
    })

    if (!script) {
      return NextResponse.json({
        success: false,
        error: '剧本不存在'
      }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      script
    })

  } catch (error) {
    console.error('Error fetching script:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '获取剧本详情时发生未知错误'
    }, { status: 500 })
  }
}

// PUT /api/scripts/[id] - 更新剧本
export async function PUT(
  request: NextRequest,
  { params }: RouteParams
) {
  const { id } = await params
  try {
    const updates = await request.json()

    const script = await prisma.script_projects.update({
      where: {
        id: id
      },
      data: {
        title: updates.title,
        description: updates.description,
        script_content: updates.scriptContent,
        script_data: updates.scriptData as any,
        updated_at: new Date()
      }
    })

    return NextResponse.json({
      success: true,
      script
    })

  } catch (error) {
    console.error('Error updating script:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '更新剧本时发生未知错误'
    }, { status: 500 })
  }
}

// DELETE /api/scripts/[id] - 删除剧本
export async function DELETE(
  request: NextRequest,
  { params }: RouteParams
) {
  const { id } = await params
  try {
    // 使用事务确保数据一致性
    await prisma.$transaction(async (tx) => {
      // 删除相关记录（由于外键约束，顺序很重要）
      await tx.script_shots.deleteMany({
        where: { project_id: id }
      })
      
      await tx.script_characters.deleteMany({
        where: { project_id: id }
      })
      
      await tx.script_generation_configs.deleteMany({
        where: { project_id: id }
      })
      
      // 删除项目
      await tx.script_projects.delete({
        where: { id: id }
      })
    })

    return NextResponse.json({
      success: true
    })

  } catch (error) {
    console.error('Error deleting script:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '删除剧本时发生未知错误'
    }, { status: 500 })
  }
}