import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// GET /api/projects/[id]/shots-images - 获取项目中每个镜头的图片生成任务
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const projectId = params.id

    // 首先获取项目的基本信息
    const project = await prisma.script_projects.findUnique({
      where: { id: projectId },
      include: {
        script_shots: {
          orderBy: { shot_number: 'asc' }
        }
      }
    })

    if (!project) {
      return NextResponse.json({
        success: false,
        error: '项目不存在'
      }, { status: 404 })
    }

    // 获取所有相关的图片生成任务，使用task_name进行匹配
    const imageTasks = await prisma.image_generation_tasks.findMany({
      where: {
        project_id: projectId,
        // 使用LIKE查询匹配包含"镜头 X"的task_name
        task_name: {
          contains: '镜头'
        }
      },
      include: {
        generated_images: {
          orderBy: {
            created_at: 'desc'
          }
        }
      },
      orderBy: {
        created_at: 'desc'
      }
    })

    // 为每个镜头匹配对应的图片生成任务
    const shotsWithImages = project.script_shots.map(shot => {
      // 查找匹配的图片生成任务，使用task_name精确匹配"镜头 X"格式
      const matchingTasks = imageTasks.filter(task => {
        if (!task.task_name) return false

        const taskNameLower = task.task_name.toLowerCase()
        const shotNumber = shot.shot_number.toString()

        // 使用正则表达式进行精确匹配，确保镜头编号是完整的数字
        // 匹配格式如："项目名 - 镜头1" 或 "镜头 1" 等，但不匹配 "镜头10" 当查找 "镜头1" 时
        const patterns = [
          new RegExp(`镜头\\s*${shotNumber}(?![0-9])`, 'i'), // 镜头1, 镜头 1 (后面不能跟数字)
          new RegExp(`shot\\s*${shotNumber}(?![0-9])`, 'i')   // shot1, shot 1 (后面不能跟数字)
        ]

        return patterns.some(pattern => pattern.test(taskNameLower))
      })

      // 获取所有匹配任务的图片
      const images = matchingTasks.flatMap(task => 
        task.generated_images.map(image => ({
          id: image.id,
          url: image.cdn_url || image.image_url,
          filename: image.image_filename,
          width: image.image_width,
          height: image.image_height,
          format: image.image_format,
          isPrimary: image.is_primary,
          createdAt: image.created_at,
          actualPrompt: image.actual_prompt,
          qualityScore: image.quality_score,
          taskId: image.task_id,
          taskName: task.task_name
        }))
      ).sort((a, b) => new Date(b.createdAt || 0).getTime() - new Date(a.createdAt || 0).getTime())

      return {
        shotNumber: shot.shot_number,
        shotId: shot.id,
        location: shot.location,
        action: shot.action,
        dialogue: shot.dialogue,
        mood: shot.mood,
        duration: shot.duration,
        hasImages: images.length > 0,
        imageCount: images.length,
        images: images,
        tasks: matchingTasks.map(task => ({
          id: task.id,
          taskName: task.task_name,
          status: task.status,
          createdAt: task.created_at,
          completedAt: task.completed_at,
          imageCount: task.generated_images.length
        }))
      }
    })

    // 统计信息
    const totalShots = project.script_shots.length
    const shotsWithImagesCount = shotsWithImages.filter(shot => shot.hasImages).length
    const totalImages = shotsWithImages.reduce((sum, shot) => sum + shot.imageCount, 0)

    return NextResponse.json({
      success: true,
      data: {
        projectId: project.id,
        projectTitle: project.title,
        totalShots,
        shotsWithImagesCount,
        totalImages,
        shots: shotsWithImages
      }
    })

  } catch (error) {
    console.error('Error fetching shots images:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '获取镜头图片时发生未知错误'
    }, { status: 500 })
  }
}
