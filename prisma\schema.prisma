generator client {
  provider = "prisma-client-js"
  output   = "../lib/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model generated_images {
  id                      String                  @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  task_id                 String?                 @db.Uuid
  image_url               String?
  image_filename          String?                 @db.VarChar
  image_size_bytes        BigInt?
  image_width             Int?
  image_height            Int?
  image_format            String?                 @default("jpg") @db.VarChar
  quality_score           Decimal?                @db.Decimal
  is_primary              Boolean?                @default(false)
  storage_provider        String?                 @default("minio") @db.VarChar
  storage_path            String?
  cdn_url                 String?
  minio_object_name       String?                 @db.VarChar(255)
  minio_bucket_name       String?                 @default("images") @db.VarChar(100)
  cdn_base_url            String?                 @db.VarChar(255)
  generation_time_seconds Decimal?                @db.Decimal
  actual_prompt           String?
  metadata                Json?
  created_at              DateTime?               @default(now()) @db.Timestamptz(6)
  image_generation_tasks  image_generation_tasks? @relation(fields: [task_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
model image_generation_tasks {
  id                       String             @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  project_id               String?            @db.Uuid
  shot_id                  String?            @db.Uuid
  user_id                  String?            @db.Uuid
  task_name                String?            @db.VarChar
  prompt                   String
  negative_prompt          String?
  model_name               String?            @default("kling-v1-5") @db.VarChar
  aspect_ratio             String?            @default("16:9") @db.VarChar
  image_count              Int?               @default(1)
  image_fidelity           Decimal?           @default(0.5) @db.Decimal
  human_fidelity           Decimal?           @default(0.45) @db.Decimal
  cfg_scale                Decimal?           @default(7.5) @db.Decimal
  seed                     BigInt?
  reference_image_url            String?
  reference_image_strength       Decimal?           @default(0.7) @db.Decimal
  reference_image_object_name    String?            @db.VarChar(255)
  status                   String?            @default("pending") @db.VarChar
  kling_task_id            String?            @unique @db.VarChar
  api_request_payload      Json?
  api_response_data        Json?
  error_message            String?
  error_code               String?            @db.VarChar
  created_at               DateTime?          @default(now()) @db.Timestamptz(6)
  started_at               DateTime?          @db.Timestamptz(6)
  completed_at             DateTime?          @db.Timestamptz(6)
  updated_at               DateTime?          @default(now()) @db.Timestamptz(6)
  generated_images         generated_images[]
  script_projects          script_projects?   @relation(fields: [project_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  script_shots             script_shots?      @relation(fields: [shot_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  users                    users?             @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
model script_characters {
  id              String           @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  project_id      String?          @db.Uuid
  name            String           @db.VarChar
  role            String           @db.VarChar
  background      String?
  sort_order      Int?             @default(0)
  created_at      DateTime?        @default(now()) @db.Timestamptz(6)
  script_projects script_projects? @relation(fields: [project_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model script_generation_configs {
  id                   String           @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  project_id           String?          @db.Uuid
  theme                String?
  selected_tags        String[]
  custom_keywords      String?
  selected_mood        String?          @db.VarChar
  selected_template    String?          @db.VarChar
  background_setting   String?
  character1           String?
  character2           String?
  supporting_character String?
  required_scenes      String?
  avoid_elements       String?
  created_at           DateTime?        @default(now()) @db.Timestamptz(6)
  script_projects      script_projects? @relation(fields: [project_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
model script_projects {
  id                        String                      @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  user_id                   String?                     @db.Uuid
  title                     String                      @db.VarChar
  description               String?
  creation_mode             String                      @db.VarChar
  style                     String                      @db.VarChar
  duration                  Int
  status                    String?                     @default("draft") @db.VarChar
  script_content            String?
  script_data               Json?
  created_at                DateTime?                   @default(now()) @db.Timestamptz(6)
  updated_at                DateTime?                   @default(now()) @db.Timestamptz(6)
  image_generation_tasks    image_generation_tasks[]
  video_generation_tasks    video_generation_tasks[]
  script_characters         script_characters[]
  script_generation_configs script_generation_configs[]
  users                     users?                      @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  script_shots              script_shots[]
}

model script_shots {
  id                     String                   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  project_id             String?                  @db.Uuid
  shot_number            Int
  duration               Int
  shot_type              String                   @db.VarChar
  location               String
  characters             String[]
  action                 String
  dialogue               String?
  camera_movement        String                   @db.VarChar
  lighting               String                   @db.VarChar
  props                  String[]
  mood                   String                   @db.VarChar
  sound_effect           String
  transition             String                   @db.VarChar
  created_at             DateTime?                @default(now()) @db.Timestamptz(6)
  image_generation_tasks image_generation_tasks[]
  video_generation_tasks video_generation_tasks[]
  script_projects        script_projects?         @relation(fields: [project_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model user_preferences {
  id               String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  user_id          String?   @unique @db.Uuid
  default_style    String?   @db.VarChar
  default_duration Int?      @default(90)
  favorite_tags    String[]
  ui_settings      Json?
  created_at       DateTime? @default(now()) @db.Timestamptz(6)
  updated_at       DateTime? @default(now()) @db.Timestamptz(6)
  users            users?    @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model users {
  id                     String                   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  email                  String                   @unique @db.VarChar
  username               String?                  @db.VarChar
  avatar_url             String?
  created_at             DateTime?                @default(now()) @db.Timestamptz(6)
  updated_at             DateTime?                @default(now()) @db.Timestamptz(6)
  image_generation_tasks image_generation_tasks[]
  video_generation_tasks video_generation_tasks[]
  script_projects        script_projects[]
  user_preferences       user_preferences?
}

/// This table contains check constraints and requires additional setup for migrations. Visit https://pris.ly/d/check-constraints for more info.
model video_generation_tasks {
  id                   String             @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  project_id           String?            @db.Uuid
  shot_id              String?            @db.Uuid
  user_id              String?            @db.Uuid
  task_name            String?            @db.VarChar
  prompt               String
  negative_prompt      String?
  image_url            String             // 必需字段，匹配数据库
  model_name           String?            @default("kling-v1-6") @db.VarChar
  aspect_ratio         String?            @default("16:9") @db.VarChar
  duration             Int?               @default(5)
  cfg_scale            Decimal?           @default(0.5) @db.Decimal
  mode                 String?            @default("pro") @db.VarChar
  seed                 BigInt?
  status               String?            @default("pending") @db.VarChar
  kling_task_id        String?            @unique @db.VarChar
  api_request_payload  Json?
  api_response_data    Json?
  error_message        String?
  error_code           String?            @db.VarChar
  created_at           DateTime?          @default(now()) @db.Timestamptz(6)
  started_at           DateTime?          @db.Timestamptz(6)
  completed_at         DateTime?          @db.Timestamptz(6)
  updated_at           DateTime?          @default(now()) @db.Timestamptz(6)
  generated_videos     generated_videos[]
  script_projects      script_projects?   @relation(fields: [project_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  script_shots         script_shots?      @relation(fields: [shot_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
  users                users?             @relation(fields: [user_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}

model generated_videos {
  id                      String                  @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  task_id                 String?                 @db.Uuid
  video_url               String?
  video_filename          String?                 @db.VarChar
  video_size_bytes        BigInt?
  video_width             Int?
  video_height            Int?
  video_format            String?                 @default("mp4") @db.VarChar
  video_duration_seconds  Decimal?                @db.Decimal
  quality_score           Decimal?                @db.Decimal
  is_primary              Boolean?                @default(false)
  storage_provider        String?                 @default("minio") @db.VarChar
  storage_path            String?
  cdn_url                 String?
  minio_object_name       String?                 @db.VarChar
  minio_bucket_name       String?                 @default("videos") @db.VarChar
  cdn_base_url            String?                 @db.VarChar
  generation_time_seconds Decimal?                @db.Decimal
  actual_prompt           String?
  metadata                Json?
  created_at              DateTime?               @default(now()) @db.Timestamptz(6)
  video_generation_tasks  video_generation_tasks? @relation(fields: [task_id], references: [id], onDelete: NoAction, onUpdate: NoAction)
}
