// Simple test script to verify the image generation API endpoints
const fetch = require('node-fetch');

async function testImageAPI() {
  console.log('🧪 Testing image generation API endpoints...');
  
  try {
    // Test POST endpoint - create image generation task
    console.log('\n1. Testing POST /api/save-image-generation');
    const postResponse = await fetch('http://localhost:3000/api/save-image-generation', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        projectId: 'test-project-123',
        userId: 'test-user-123',
        taskName: '测试场景',
        prompt: 'A beautiful sunset over mountains, cinematic style',
        imageCount: 2,
        modelName: 'kling-v1-5',
      }),
    });

    const postResult = await postResponse.json();
    console.log('POST Response:', postResult);

    if (postResult.success) {
      const taskId = postResult.taskId;
      
      // Test PUT endpoint - update task with images
      console.log('\n2. Testing PUT /api/save-image-generation');
      const putResponse = await fetch('http://localhost:3000/api/save-image-generation', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          taskId,
          status: 'completed',
          images: [
            {
              imageUrl: 'https://example.com/image1.jpg',
              imageWidth: 1024,
              imageHeight: 768,
              imageFormat: 'jpg',
              isPrimary: true,
              minioObjectName: 'test-image-1.jpg',
              minioBucketName: 'images',
              generationTimeSeconds: 5,
              actualPrompt: 'A beautiful sunset over mountains, cinematic style',
              metadata: {
                test: true,
                index: 0,
              }
            },
            {
              imageUrl: 'https://example.com/image2.jpg',
              imageWidth: 1024,
              imageHeight: 768,
              imageFormat: 'jpg',
              isPrimary: false,
              minioObjectName: 'test-image-2.jpg',
              minioBucketName: 'images',
              generationTimeSeconds: 5,
              actualPrompt: 'A beautiful sunset over mountains, cinematic style',
              metadata: {
                test: true,
                index: 1,
              }
            }
          ]
        }),
      });

      const putResult = await putResponse.json();
      console.log('PUT Response:', putResult);
      
      if (putResult.success) {
        console.log('✅ Both endpoints working correctly!');
        console.log(`✅ Created ${putResult.images.length} images in generated_images table`);
      } else {
        console.error('❌ PUT endpoint failed:', putResult);
      }
    } else {
      console.error('❌ POST endpoint failed:', postResult);
    }

  } catch (error) {
    console.error('❌ API test failed:', error);
  }
}

// Run the test if this script is executed directly
if (require.main === module) {
  testImageAPI().catch(console.error);
}

module.exports = { testImageAPI };