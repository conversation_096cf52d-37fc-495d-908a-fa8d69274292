#!/usr/bin/env node

/**
 * 测试修复后的列表查询功能
 * 专门用于验证 /v1/videos/image2video 端点是否正常工作
 */

// 加载环境变量
require('dotenv').config({ path: '.env.local' });
require('dotenv').config();

const jwt = require('jsonwebtoken');

// 生成JWT Token
function generateJWTToken() {
  const accessKey = process.env.KLING_ACCESS_KEY;
  const secretKey = process.env.KLING_SECRET_KEY;

  if (!accessKey || !secretKey) {
    throw new Error('请设置 KLING_ACCESS_KEY 和 KLING_SECRET_KEY 环境变量');
  }

  const token = jwt.sign(
    {
      iss: accessKey,
      exp: Math.floor(Date.now() / 1000) + 1800, // 30分钟有效期
      nbf: Math.floor(Date.now() / 1000) - 5     // 5秒前开始生效
    },
    secretKey,
    { algorithm: 'HS256' }
  );

  return token;
}

// 测试列表查询
async function testListQuery() {
  console.log('🔧 [LIST-TEST] 测试修复后的列表查询功能');
  console.log('='.repeat(60));

  try {
    const token = generateJWTToken();
    console.log('✅ [LIST-TEST] JWT Token 生成成功');

    // 测试1: 基础列表查询（无参数）
    console.log('\n📝 [TEST-1] 基础列表查询（默认参数）');
    const url1 = 'https://api-beijing.klingai.com/v1/videos/image2video';
    console.log('🔗 [TEST-1] URL:', url1);
    
    const response1 = await fetch(url1, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });
    
    console.log('📡 [TEST-1] HTTP状态:', response1.status, response1.statusText);
    
    if (response1.ok) {
      const data1 = await response1.json();
      console.log('✅ [TEST-1] 查询成功');
      console.log('📊 [TEST-1] 返回码:', data1.code);
      console.log('📊 [TEST-1] 消息:', data1.message || '无');
      
      if (data1.data && data1.data.tasks) {
        console.log('📊 [TEST-1] 任务数量:', data1.data.tasks.length);
        console.log('📊 [TEST-1] 总数:', data1.data.total || '未提供');
      }
    } else {
      const errorText1 = await response1.text();
      console.error('❌ [TEST-1] 查询失败:', errorText1);
    }

    // 测试2: 带参数的列表查询
    console.log('\n📝 [TEST-2] 带参数的列表查询');
    const params = new URLSearchParams({
      pageNum: '1',
      pageSize: '5'
    });
    const url2 = `https://api-beijing.klingai.com/v1/videos/image2video?${params.toString()}`;
    console.log('🔗 [TEST-2] URL:', url2);
    console.log('📋 [TEST-2] 参数:', Object.fromEntries(params));
    
    const response2 = await fetch(url2, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });
    
    console.log('📡 [TEST-2] HTTP状态:', response2.status, response2.statusText);
    
    if (response2.ok) {
      const data2 = await response2.json();
      console.log('✅ [TEST-2] 查询成功');
      console.log('📊 [TEST-2] 返回码:', data2.code);
      
      if (data2.data && data2.data.tasks) {
        console.log('📊 [TEST-2] 任务数量:', data2.data.tasks.length);
        console.log('📊 [TEST-2] 总数:', data2.data.total || '未提供');
        
        // 显示前3个任务的基本信息
        const tasksToShow = data2.data.tasks.slice(0, 3);
        tasksToShow.forEach((task, index) => {
          console.log(`📋 [TASK-${index + 1}] ID: ${task.task_id}`);
          console.log(`📋 [TASK-${index + 1}] 状态: ${task.task_status}`);
          console.log(`📋 [TASK-${index + 1}] 创建时间: ${new Date(task.created_at * 1000).toLocaleString()}`);
        });
      }
    } else {
      const errorText2 = await response2.text();
      console.error('❌ [TEST-2] 查询失败:', errorText2);
    }

    // 测试3: 按状态筛选查询
    console.log('\n📝 [TEST-3] 按状态筛选查询（成功任务）');
    const params3 = new URLSearchParams({
      status: 'succeed',
      pageNum: '1',
      pageSize: '3'
    });
    const url3 = `https://api-beijing.klingai.com/v1/videos/image2video?${params3.toString()}`;
    console.log('🔗 [TEST-3] URL:', url3);
    console.log('📋 [TEST-3] 参数:', Object.fromEntries(params3));
    
    const response3 = await fetch(url3, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });
    
    console.log('📡 [TEST-3] HTTP状态:', response3.status, response3.statusText);
    
    if (response3.ok) {
      const data3 = await response3.json();
      console.log('✅ [TEST-3] 查询成功');
      console.log('📊 [TEST-3] 返回码:', data3.code);
      
      if (data3.data && data3.data.tasks) {
        console.log('📊 [TEST-3] 成功任务数量:', data3.data.tasks.length);
        
        data3.data.tasks.forEach((task, index) => {
          console.log(`✅ [SUCCESS-${index + 1}] ${task.task_id} - ${task.task_status}`);
        });
      }
    } else {
      const errorText3 = await response3.text();
      console.error('❌ [TEST-3] 查询失败:', errorText3);
    }

    // 测试4: 错误的URL格式（带斜杠）
    console.log('\n📝 [TEST-4] 测试错误的URL格式（带斜杠）');
    const wrongUrl = 'https://api-beijing.klingai.com/v1/videos/image2video/?pageNum=1&pageSize=5';
    console.log('🔗 [TEST-4] 错误URL:', wrongUrl);
    
    const response4 = await fetch(wrongUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });
    
    console.log('📡 [TEST-4] HTTP状态:', response4.status, response4.statusText);
    
    if (response4.ok) {
      console.log('⚠️ [TEST-4] 意外：带斜杠的URL也能工作');
    } else {
      const errorText4 = await response4.text();
      console.log('❌ [TEST-4] 预期的错误（带斜杠URL失败）:', errorText4);
    }

    console.log('\n📊 [LIST-TEST] 测试总结:');
    console.log('='.repeat(60));
    console.log('✅ 正确格式: /v1/videos/image2video?pageNum=1&pageSize=30');
    console.log('❌ 错误格式: /v1/videos/image2video/?pageNum=1&pageSize=30 (多了斜杠)');
    console.log('📋 支持的参数: pageNum, pageSize, status, start_time, end_time');
    console.log('📈 默认值: pageNum=1, pageSize=30');
    console.log('📊 取值范围: pageNum[1,1000], pageSize[1,500]');

    console.log('\n✅ [LIST-TEST] 列表查询修复测试完成!');

  } catch (error) {
    console.error('❌ [LIST-TEST] 测试失败:', error.message);
    console.error('❌ [LIST-TEST] 错误详情:', error);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  testListQuery();
}

module.exports = {
  testListQuery,
  generateJWTToken
};
