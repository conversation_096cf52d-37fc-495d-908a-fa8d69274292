# 视频刷新功能实现文档

## 概述

本文档描述了视频刷新功能的完整实现，该功能允许用户查询视频生成任务的状态，首先从数据库查询任务信息，然后调用 Kling API 获取最新状态。

## 功能特性

### 1. 数据库存储
- 新增 `video_generation_tasks` 表存储视频生成任务
- 新增 `generated_videos` 表存储生成的视频记录
- 支持任务状态跟踪和历史记录

### 2. 智能刷新策略
- **步骤1**: 首先查询数据库中的任务信息
- **步骤2**: 如果数据库中已有完成的视频，直接使用
- **步骤3**: 否则调用 Kling API 查询最新状态
- **步骤4**: 更新数据库中的任务状态和视频记录

### 3. 用户界面增强
- 场景卡片显示任务状态徽章
- 支持单个场景刷新和批量刷新
- 实时状态更新和进度显示

## 数据库架构

### video_generation_tasks 表
```sql
CREATE TABLE public.video_generation_tasks (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id uuid REFERENCES script_projects(id),
  shot_id uuid REFERENCES script_shots(id),
  user_id uuid REFERENCES users(id),
  task_name varchar,
  prompt text NOT NULL,
  negative_prompt text,
  model_name varchar DEFAULT 'kling-v1',
  aspect_ratio varchar DEFAULT '16:9',
  duration integer DEFAULT 5,
  image_reference varchar DEFAULT 'subject',
  image_fidelity numeric DEFAULT 0.5,
  base_image_url text,
  status varchar DEFAULT 'pending',
  kling_task_id varchar UNIQUE,
  api_request_payload jsonb,
  api_response_data jsonb,
  error_message text,
  error_code varchar,
  created_at timestamptz DEFAULT now(),
  started_at timestamptz,
  completed_at timestamptz,
  updated_at timestamptz DEFAULT now()
);
```

### generated_videos 表
```sql
CREATE TABLE public.generated_videos (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  task_id uuid REFERENCES video_generation_tasks(id),
  video_url text NOT NULL,
  video_filename varchar,
  video_size_bytes bigint,
  video_width integer,
  video_height integer,
  video_format varchar DEFAULT 'mp4',
  video_duration numeric,
  quality_score numeric,
  storage_provider varchar DEFAULT 'kling',
  storage_path text,
  cdn_url text,
  generation_time_seconds numeric,
  metadata jsonb,
  created_at timestamptz DEFAULT now()
);
```

## API 端点

### 1. 视频生成 API (`/api/generate-video`)
**增强功能**:
- 支持数据库字段: `projectId`, `shotId`, `userId`, `taskName`
- 自动保存任务到数据库
- 任务完成时自动更新数据库状态

**请求示例**:
```json
{
  "prompt": "视频生成提示词",
  "image": "https://example.com/image.jpg",
  "duration": 5,
  "aspectRatio": "16:9",
  "projectId": "uuid",
  "shotId": "uuid",
  "userId": "uuid",
  "taskName": "场景1 - 视频生成"
}
```

### 2. 视频任务管理 API (`/api/save-video-generation`)
**功能**:
- `POST`: 创建视频生成任务
- `PUT`: 更新任务状态和视频记录
- `GET`: 查询任务列表

**查询示例**:
```
GET /api/save-video-generation?klingTaskId=task123
GET /api/save-video-generation?projectId=uuid
```

### 3. 任务状态查询 API (`/api/check-task-status`)
**功能**: 查询 Kling API 任务状态
**用法**: 由前端刷新功能调用

## 前端组件更新

### VideoGenerationStep.tsx 主要变更

#### 1. 增强的刷新功能
```typescript
const handleRefreshVideo = async (sceneId: number) => {
  // 步骤1: 查询数据库
  const dbResponse = await fetch(`/api/save-video-generation?klingTaskId=${taskId}`);
  
  // 步骤2: 如果数据库有完成的视频，直接使用
  if (dbTask.status === 'completed' && dbTask.generated_videos.length > 0) {
    // 更新场景状态
    return;
  }
  
  // 步骤3: 调用 Kling API 查询
  const response = await fetch('/api/check-task-status', {
    method: 'POST',
    body: JSON.stringify({ taskId })
  });
  
  // 步骤4: 处理结果并更新状态
};
```

#### 2. 数据库字段支持
视频生成 API 调用现在包含数据库相关字段:
```typescript
body: JSON.stringify({
  // ... 原有字段
  projectId: projectId,
  shotId: currentSceneData.shotData?.shotId,
  userId: userId,
  taskName: `${currentSceneData.title} - 视频生成`,
})
```

#### 3. 状态显示增强
- 任务状态徽章: 显示 `处理中`、`已提交`、`失败` 等状态
- 图片来源标识: `Supabase ✅`、`内网 📤`、`外部 ⚠️`
- 刷新按钮: 支持单个场景和批量刷新

## 使用流程

### 1. 数据库初始化
```bash
# 执行 SQL 脚本创建表
psql -d your_database -f scripts/create-video-tables.sql
```

### 2. 生成 Prisma 客户端
```bash
npx prisma generate
```

### 3. 测试功能
```bash
# 运行测试脚本
node scripts/test-video-refresh.js
```

### 4. 使用界面
1. 在视频生成步骤中选择场景
2. 点击"生成视频"按钮
3. 任务提交后，可点击"刷新视频"查询状态
4. 支持批量刷新所有待处理视频

## 错误处理

### 1. 数据库错误
- 数据库操作失败时记录日志但不影响主流程
- 提供降级方案，仍可使用原有的内存状态管理

### 2. API 错误
- Kling API 调用失败时显示详细错误信息
- 支持重试机制和超时处理

### 3. 状态同步
- 数据库和前端状态保持同步
- 支持多用户环境下的状态更新

## 性能优化

### 1. 数据库索引
- 为常用查询字段创建索引
- 支持高效的任务状态查询

### 2. 缓存策略
- 前端缓存任务状态，避免频繁查询
- 30秒内不重复查询相同任务

### 3. 批量操作
- 支持批量刷新多个任务
- 添加延迟避免 API 限制

## 监控和日志

### 1. 详细日志
- 每个步骤都有详细的控制台日志
- 错误日志包含完整的上下文信息

### 2. 状态跟踪
- 任务创建、更新、完成的完整记录
- 支持任务执行时间统计

### 3. 用户反馈
- 清晰的状态提示和错误消息
- 进度显示和操作确认

## 未来扩展

### 1. 实时通知
- WebSocket 支持实时状态更新
- 任务完成时自动通知用户

### 2. 任务队列
- 支持任务优先级和队列管理
- 批量任务的智能调度

### 3. 统计分析
- 任务成功率和耗时统计
- 用户使用行为分析
