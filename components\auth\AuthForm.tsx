'use client'

import { useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useToast } from '@/components/ui/use-toast'
import { Eye, EyeOff, Mail, Lock, User, Sparkles, Video, Loader2 } from 'lucide-react'

interface AuthFormProps {
  onSuccess?: () => void
}

export function AuthForm({ onSuccess }: AuthFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [username, setUsername] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const { signIn, signUp } = useAuth()
  const { toast } = useToast()

  const handleSignIn = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    const { error } = await signIn(email, password)
    
    if (error) {
      toast({
        title: '登录失败',
        description: error.message,
        variant: 'destructive',
      })
    } else {
      toast({
        title: '登录成功',
        description: '欢迎回来！',
      })
      onSuccess?.()
    }
    
    setIsLoading(false)
  }

  const handleSignUp = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    if (password.length < 6) {
      toast({
        title: '注册失败',
        description: '密码至少需要6位字符',
        variant: 'destructive',
      })
      setIsLoading(false)
      return
    }

    const { error } = await signUp(email, password, username)
    
    if (error) {
      toast({
        title: '注册失败',
        description: error.message,
        variant: 'destructive',
      })
    } else {
      toast({
        title: '注册成功',
        description: '请检查您的邮箱进行验证',
      })
      onSuccess?.()
    }
    
    setIsLoading(false)
  }

  return (
    <div className="w-full max-w-md mx-auto">
      {/* 头部装饰 */}
      <div className="text-center mb-8">
        <div className="relative inline-flex items-center justify-center w-20 h-20 mb-6">
          {/* 背景光晕效果 */}
          <div className="absolute inset-0 bg-gradient-to-br from-violet-500 via-purple-500 via-blue-500 to-cyan-500 rounded-3xl blur-lg opacity-60 animate-pulse"></div>
          {/* 主图标容器 */}
          <div className="relative bg-gradient-to-br from-violet-600 via-purple-600 via-blue-600 to-cyan-600 rounded-3xl p-4 shadow-2xl transform hover:scale-105 transition-all duration-300">
            <Video className="w-8 h-8 text-white drop-shadow-lg" />
            {/* 装饰性星星 */}
            <div className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 rounded-full animate-ping"></div>
            <div className="absolute -bottom-1 -left-1 w-2 h-2 bg-pink-400 rounded-full animate-bounce delay-300"></div>
          </div>
        </div>
        <h1 className="text-3xl font-bold bg-gradient-to-r from-violet-600 via-purple-600 via-blue-600 to-cyan-600 bg-clip-text text-transparent mb-2 tracking-tight">
          AI短剧生成器
        </h1>
        <p className="text-gray-600 text-lg font-medium">创造属于你的精彩故事</p>
        <div className="flex items-center justify-center gap-1 mt-2 opacity-70">
          <div className="w-1 h-1 bg-violet-400 rounded-full animate-pulse"></div>
          <div className="w-1 h-1 bg-purple-400 rounded-full animate-pulse delay-100"></div>
          <div className="w-1 h-1 bg-blue-400 rounded-full animate-pulse delay-200"></div>
          <div className="w-1 h-1 bg-cyan-400 rounded-full animate-pulse delay-300"></div>
        </div>
      </div>

      <Tabs defaultValue="signin" className="w-full">
        <TabsList className="grid w-full grid-cols-2 mb-8 bg-gradient-to-r from-gray-50 via-white to-gray-50 backdrop-blur-xl p-1.5 rounded-2xl shadow-lg border border-gray-200/50">
          <TabsTrigger
            value="signin"
            className="rounded-xl data-[state=active]:bg-gradient-to-r data-[state=active]:from-violet-500 data-[state=active]:to-purple-600 data-[state=active]:text-white data-[state=active]:shadow-lg data-[state=active]:shadow-purple-500/25 transition-all duration-300 font-medium hover:bg-gray-50"
          >
            <User className="w-4 h-4 mr-2" />
            登录
          </TabsTrigger>
          <TabsTrigger
            value="signup"
            className="rounded-xl data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-500 data-[state=active]:to-cyan-600 data-[state=active]:text-white data-[state=active]:shadow-lg data-[state=active]:shadow-blue-500/25 transition-all duration-300 font-medium hover:bg-gray-50"
          >
            <Sparkles className="w-4 h-4 mr-2" />
            注册
          </TabsTrigger>
        </TabsList>

        <TabsContent value="signin" className="mt-0">
          <Card className="border-0 shadow-2xl bg-white/90 backdrop-blur-xl relative overflow-hidden">
            {/* 背景装饰 */}
            <div className="absolute inset-0 bg-gradient-to-br from-violet-50/50 via-purple-50/30 to-blue-50/50"></div>
            <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-violet-200/20 to-transparent rounded-full blur-2xl"></div>
            <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-blue-200/20 to-transparent rounded-full blur-xl"></div>

            <CardHeader className="text-center pb-6 relative z-10">
              <CardTitle className="text-2xl font-bold text-gray-800 mb-2">欢迎回来</CardTitle>
              <CardDescription className="text-gray-600 text-base">
                请输入您的账号信息登录
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6 relative z-10">
              <form onSubmit={handleSignIn} className="space-y-6">
                <div className="space-y-3">
                  <Label htmlFor="email" className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                    <Mail className="w-4 h-4 text-violet-500" />
                    邮箱地址
                  </Label>
                  <div className="relative group">
                    <div className="absolute inset-0 bg-gradient-to-r from-violet-500/10 to-purple-500/10 rounded-xl blur-sm group-focus-within:blur-none group-focus-within:from-violet-500/20 group-focus-within:to-purple-500/20 transition-all duration-300"></div>
                    <Input
                      id="email"
                      type="email"
                      placeholder="请输入您的邮箱"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="relative h-12 pl-4 pr-4 border-2 border-gray-200/80 focus:border-violet-400 focus:ring-4 focus:ring-violet-400/20 rounded-xl transition-all duration-300 bg-white/80 backdrop-blur-sm hover:border-violet-300 font-medium placeholder:text-gray-400"
                      required
                    />
                  </div>
                </div>

                <div className="space-y-3">
                  <Label htmlFor="password" className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                    <Lock className="w-4 h-4 text-purple-500" />
                    密码
                  </Label>
                  <div className="relative group">
                    <div className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-blue-500/10 rounded-xl blur-sm group-focus-within:blur-none group-focus-within:from-purple-500/20 group-focus-within:to-blue-500/20 transition-all duration-300"></div>
                    <Input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      placeholder="请输入您的密码"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      className="relative h-12 pl-4 pr-12 border-2 border-gray-200/80 focus:border-purple-400 focus:ring-4 focus:ring-purple-400/20 rounded-xl transition-all duration-300 bg-white/80 backdrop-blur-sm hover:border-purple-300 font-medium placeholder:text-gray-400"
                      required
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-purple-600 transition-all duration-200 p-1 rounded-lg hover:bg-purple-50"
                    >
                      {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                    </button>
                  </div>
                </div>

                <Button
                  type="submit"
                  className="w-full h-14 bg-gradient-to-r from-violet-600 via-purple-600 to-blue-600 hover:from-violet-700 hover:via-purple-700 hover:to-blue-700 text-white font-semibold rounded-xl shadow-xl hover:shadow-2xl hover:shadow-purple-500/25 transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98] relative overflow-hidden group"
                  disabled={isLoading}
                >
                  {/* 按钮光效 */}
                  <div className="absolute inset-0 bg-gradient-to-r from-white/20 via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative flex items-center justify-center">
                    {isLoading ? (
                      <>
                        <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                        登录中...
                      </>
                    ) : (
                      <>
                        <User className="w-5 h-5 mr-2" />
                        立即登录
                      </>
                    )}
                  </div>
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="signup" className="mt-0">
          <Card className="border-0 shadow-2xl bg-white/90 backdrop-blur-xl relative overflow-hidden">
            {/* 背景装饰 */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 via-cyan-50/30 to-teal-50/50"></div>
            <div className="absolute top-0 left-0 w-32 h-32 bg-gradient-to-br from-blue-200/20 to-transparent rounded-full blur-2xl"></div>
            <div className="absolute bottom-0 right-0 w-24 h-24 bg-gradient-to-tl from-cyan-200/20 to-transparent rounded-full blur-xl"></div>

            <CardHeader className="text-center pb-6 relative z-10">
              <CardTitle className="text-2xl font-bold text-gray-800 mb-2">创建账号</CardTitle>
              <CardDescription className="text-gray-600 text-base">
                加入我们，开始创作您的专属短剧
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6 relative z-10">
              <form onSubmit={handleSignUp} className="space-y-6">
                <div className="space-y-3">
                  <Label htmlFor="new-username" className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                    <User className="w-4 h-4 text-blue-500" />
                    用户名 <span className="text-gray-400 text-xs font-normal">(可选)</span>
                  </Label>
                  <div className="relative group">
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-cyan-500/10 rounded-xl blur-sm group-focus-within:blur-none group-focus-within:from-blue-500/20 group-focus-within:to-cyan-500/20 transition-all duration-300"></div>
                    <Input
                      id="new-username"
                      type="text"
                      placeholder="请输入您的用户名"
                      value={username}
                      onChange={(e) => setUsername(e.target.value)}
                      className="relative h-12 pl-4 pr-4 border-2 border-gray-200/80 focus:border-blue-400 focus:ring-4 focus:ring-blue-400/20 rounded-xl transition-all duration-300 bg-white/80 backdrop-blur-sm hover:border-blue-300 font-medium placeholder:text-gray-400"
                    />
                  </div>
                </div>

                <div className="space-y-3">
                  <Label htmlFor="new-email" className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                    <Mail className="w-4 h-4 text-cyan-500" />
                    邮箱地址
                  </Label>
                  <div className="relative group">
                    <div className="absolute inset-0 bg-gradient-to-r from-cyan-500/10 to-teal-500/10 rounded-xl blur-sm group-focus-within:blur-none group-focus-within:from-cyan-500/20 group-focus-within:to-teal-500/20 transition-all duration-300"></div>
                    <Input
                      id="new-email"
                      type="email"
                      placeholder="请输入您的邮箱"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="relative h-12 pl-4 pr-4 border-2 border-gray-200/80 focus:border-cyan-400 focus:ring-4 focus:ring-cyan-400/20 rounded-xl transition-all duration-300 bg-white/80 backdrop-blur-sm hover:border-cyan-300 font-medium placeholder:text-gray-400"
                      required
                    />
                  </div>
                </div>

                <div className="space-y-3">
                  <Label htmlFor="new-password" className="text-sm font-semibold text-gray-700 flex items-center gap-2">
                    <Lock className="w-4 h-4 text-teal-500" />
                    密码
                  </Label>
                  <div className="relative group">
                    <div className="absolute inset-0 bg-gradient-to-r from-teal-500/10 to-emerald-500/10 rounded-xl blur-sm group-focus-within:blur-none group-focus-within:from-teal-500/20 group-focus-within:to-emerald-500/20 transition-all duration-300"></div>
                    <Input
                      id="new-password"
                      type={showPassword ? "text" : "password"}
                      placeholder="至少6位字符"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      className="relative h-12 pl-4 pr-12 border-2 border-gray-200/80 focus:border-teal-400 focus:ring-4 focus:ring-teal-400/20 rounded-xl transition-all duration-300 bg-white/80 backdrop-blur-sm hover:border-teal-300 font-medium placeholder:text-gray-400"
                      required
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-teal-600 transition-all duration-200 p-1 rounded-lg hover:bg-teal-50"
                    >
                      {showPassword ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
                    </button>
                  </div>
                  <p className="text-xs text-gray-500 mt-2 flex items-center gap-1">
                    <div className="w-1 h-1 bg-teal-400 rounded-full"></div>
                    密码长度至少6位，建议包含字母和数字
                  </p>
                </div>

                <Button
                  type="submit"
                  className="w-full h-14 bg-gradient-to-r from-blue-600 via-cyan-600 to-teal-600 hover:from-blue-700 hover:via-cyan-700 hover:to-teal-700 text-white font-semibold rounded-xl shadow-xl hover:shadow-2xl hover:shadow-cyan-500/25 transition-all duration-300 transform hover:scale-[1.02] active:scale-[0.98] relative overflow-hidden group"
                  disabled={isLoading}
                >
                  {/* 按钮光效 */}
                  <div className="absolute inset-0 bg-gradient-to-r from-white/20 via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <div className="relative flex items-center justify-center">
                    {isLoading ? (
                      <>
                        <Loader2 className="w-5 h-5 mr-2 animate-spin" />
                        注册中...
                      </>
                    ) : (
                      <>
                        <Sparkles className="w-5 h-5 mr-2" />
                        立即注册
                      </>
                    )}
                  </div>
                </Button>
              </form>

              {/* 注册提示 */}
              <div className="text-center pt-4 border-t border-gray-100">
                <p className="text-xs text-gray-500 leading-relaxed">
                  注册即表示您同意我们的
                  <a href="#" className="text-cyan-600 hover:text-cyan-700 underline underline-offset-2 transition-colors duration-200 mx-1">服务条款</a>
                  和
                  <a href="#" className="text-cyan-600 hover:text-cyan-700 underline underline-offset-2 transition-colors duration-200 mx-1">隐私政策</a>
                </p>
                <div className="flex items-center justify-center gap-1 mt-3 opacity-60">
                  <div className="w-1 h-1 bg-blue-400 rounded-full animate-pulse"></div>
                  <div className="w-1 h-1 bg-cyan-400 rounded-full animate-pulse delay-100"></div>
                  <div className="w-1 h-1 bg-teal-400 rounded-full animate-pulse delay-200"></div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}