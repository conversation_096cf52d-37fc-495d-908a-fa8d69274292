# 可灵AI图像生成参数更新

## 概述
根据可灵AI官方API文档，对前端图像生成设置相关选项进行了修改，确保完全遵从可灵官方的说明。

## 主要变更

### 1. 前端组件更新 (`app/create/components/ImageGenerationStep.tsx`)

#### 移除的参数：
- `cfgScale` - CFG引导强度（可灵AI不支持此参数）
- `steps` - 采样步数（可灵AI不支持此参数）
- `seed` - 随机种子（暂时移除，可灵AI支持但前端暂不使用）

#### 新增的参数：
- `imageFidelity` - 图像保真度 (0-1，默认0.5)
- `humanFidelity` - 人物保真度 (0-1，默认0.45)

#### UI变更：
- 将"引导强度 (CFG Scale)"滑块替换为"图像保真度"滑块
- 将"采样步数"滑块替换为"人物保真度"滑块
- 移除了"随机种子"输入框
- 为新参数添加了说明文字

### 2. API路由更新

#### `app/api/generate-image/route.ts`
- 更新schema验证，添加`imageFidelity`和`humanFidelity`参数
- 在调用KlingAI客户端时传递新参数

#### `app/api/generate-and-save-image/route.ts`
- 更新schema验证，添加新参数
- 在调用KlingAI客户端时传递新参数

#### `app/api/save-image-generation/route.ts`
- 移除`cfgScale`参数
- 添加`humanFidelity`参数

### 3. 数据库更新

#### Prisma Schema (`prisma/schema.prisma`)
- 在`image_generation_tasks`模型中添加`human_fidelity`字段
- 设置默认值为0.45

#### SQL创建脚本 (`create_sql.txt`)
- 在表定义中添加`human_fidelity`字段
- 添加约束检查确保值在0-1范围内

#### 数据库操作 (`lib/prisma-image-operations.ts`)
- 更新`ImageGenerationTaskData`接口，添加`humanFidelity`字段
- 在`createImageGenerationTask`函数中处理新字段

### 4. 数据库迁移
创建了迁移脚本 `migrations/add_human_fidelity_field.sql` 用于更新现有数据库。

## 可灵AI支持的参数

根据代码分析和API实现，可灵AI图像生成API支持以下参数：

### 核心参数：
- `model_name` - 模型名称 ('kling-v1-5', 'kling-v2')
- `prompt` - 正向提示词
- `negative_prompt` - 负向提示词
- `aspect_ratio` - 宽高比 ('1:1', '3:4', '4:3', '16:9', '9:16')
- `n` - 生成数量 (1-6)

### 质量控制参数：
- `image_fidelity` - 图像保真度 (0-1，默认0.5)
- `human_fidelity` - 人物保真度 (0-1，默认0.45)

### 参考图像参数：
- `image` - 参考图像
- `image_reference` - 参考类型 ('subject', 'face')

### 其他参数：
- `callback_url` - 回调URL

## 不支持的参数

以下参数在可灵AI API中不被支持，已从前端移除：
- `cfg_scale` - CFG引导强度
- `steps` - 采样步数
- `seed` - 随机种子（API支持但前端暂不使用）

## 使用说明

### 图像保真度 (Image Fidelity)
- 范围：0-1
- 默认值：0.5
- 作用：控制生成图像与提示词的匹配程度
- 值越高，生成的图像越严格遵循提示词描述

### 人物保真度 (Human Fidelity)
- 范围：0-1
- 默认值：0.45
- 作用：控制人物特征的保真度和一致性
- 值越高，人物特征越一致和准确

## 兼容性说明

- 数据库中保留了`cfg_scale`字段以保持向后兼容性
- 新的`human_fidelity`字段已添加到所有相关表和接口中
- 现有项目不会受到影响，新参数有合理的默认值

## 部署注意事项

1. 运行数据库迁移脚本 `migrations/add_human_fidelity_field.sql`
2. 重新生成Prisma客户端：`npx prisma generate`
3. 确保环境变量中配置了正确的可灵AI API密钥
4. 测试新的参数设置功能

## 最新更新 (模型选择直接化)

### 图片质量选择优化
- **移除了质量描述**：不再使用"草图质量"、"标准质量"、"高质量"、"超高质量"等描述
- **直接模型选择**：现在直接提供 `kling-v1-5` 和 `kling-v2` 两个选项
- **简化逻辑**：移除了质量到模型的映射逻辑，直接使用选择的模型名称

### 变更详情
1. **前端组件**：
   - 标签从"图片质量"改为"模型选择"
   - 选项直接显示模型名称：`kling-v1-5` 和 `kling-v2`
   - 默认值改为 `kling-v1-5`

2. **API调用**：
   - 移除了 `quality === 'ultra' ? 'kling-v2' : 'kling-v1-5'` 的映射逻辑
   - 直接使用 `quality` 变量作为 `modelName`

3. **API路由**：
   - 移除了 `quality` 参数的验证
   - 保留 `modelName` 参数，支持 `kling-v1-5` 和 `kling-v2`

## 测试建议

1. 测试不同的图像保真度设置对生成结果的影响
2. 测试人物保真度设置对人物一致性的影响
3. 验证参数验证和错误处理
4. 确认数据库记录正确保存新参数
5. **新增**：测试两个模型的直接选择功能
