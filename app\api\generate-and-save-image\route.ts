import { NextRequest, NextResponse } from 'next/server'
import { KlingAIClient } from '@/lib/kling-ai'
import { uploadImageToMinIO } from '@/lib/minio-client'
import { createImageGenerationTask, createGeneratedImage, updateImageGenerationTask } from '@/lib/prisma-image-operations'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const generateAndSaveSchema = z.object({
  projectId: z.string().uuid(),
  shotNumber: z.number().int().positive(),
  userId: z.string().uuid(),
  prompt: z.string().min(1),
  negativePrompt: z.string().optional(),
  n: z.number().min(1).max(6).default(1),
  aspectRatio: z.enum(['1:1', '3:4', '4:3', '16:9', '9:16']).default('1:1'),
  modelName: z.enum(['kling-v1-5', 'kling-v2']).default('kling-v1-5'),
  taskName: z.string().optional(),
})

export async function POST(request: NextRequest) {
  console.log('🚀 [GENERATE-AND-SAVE] Starting complete image generation workflow...')
  
  try {
    // 1. 验证请求数据
    const body = await request.json()
    const validatedData = generateAndSaveSchema.parse(body)

    console.log('📝 [GENERATE-AND-SAVE] Validated data:', {
      projectId: validatedData.projectId,
      shotNumber: validatedData.shotNumber,
      userId: validatedData.userId,
      prompt: validatedData.prompt.substring(0, 100) + '...',
      imageCount: validatedData.n
    })

    // 2. 获取对应的shot_id
    const shot = await prisma.script_shots.findFirst({
      where: {
        project_id: validatedData.projectId,
        shot_number: validatedData.shotNumber
      }
    })

    if (!shot) {
      return NextResponse.json({
        success: false,
        error: `找不到镜头编号 ${validatedData.shotNumber}`
      }, { status: 404 })
    }

    // 3. 创建图像生成任务记录
    const task = await createImageGenerationTask({
      projectId: validatedData.projectId,
      shotId: shot.id,
      userId: validatedData.userId,
      taskName: validatedData.taskName || `镜头 ${validatedData.shotNumber} 图片生成`,
      prompt: validatedData.prompt,
      negativePrompt: validatedData.negativePrompt,
      modelName: validatedData.modelName,
      aspectRatio: validatedData.aspectRatio,
      imageCount: validatedData.n,
      status: 'processing',
    })

    console.log('✅ [GENERATE-AND-SAVE] Created task:', task.id)

    // 4. 更新任务为进行中
    await updateImageGenerationTask(task.id, {
      status: 'processing',
      startedAt: new Date(),
    })

    // 5. 初始化Kling AI客户端
    const accessKey = process.env.KLING_ACCESS_KEY
    const secretKey = process.env.KLING_SECRET_KEY

    if (!accessKey || !secretKey) {
      await updateImageGenerationTask(task.id, {
        status: 'failed',
        errorMessage: 'Kling AI API keys not configured',
        completedAt: new Date(),
      })
      return NextResponse.json({
        success: false,
        error: 'Kling AI API keys not configured'
      }, { status: 500 })
    }

    const klingClient = new KlingAIClient({ accessKey, secretKey })

    // 6. 生成图片
    console.log('🎨 [GENERATE-AND-SAVE] Generating images with Kling AI...')
    const klingTaskId = await klingClient.generateImage({
      prompt: validatedData.prompt,
      negativePrompt: validatedData.negativePrompt,
      n: validatedData.n,
      aspectRatio: validatedData.aspectRatio,
      modelName: validatedData.modelName,
    })

    console.log('⏳ [GENERATE-AND-SAVE] Waiting for generation to complete...')
    
    // 7. 轮询任务状态
    let attempts = 0
    const maxAttempts = 30
    
    const pollTaskStatus = async (): Promise<any> => {
      attempts++
      
      try {
        const status = await klingClient.getTaskStatus(klingTaskId)
        console.log(`⏳ [GENERATE-AND-SAVE] Poll ${attempts}/${maxAttempts}, status: ${status.data.task_status}`)
        
        if (status.data.task_status === 'succeed') {
          return status
        } else if (status.data.task_status === 'failed') {
          throw new Error('Image generation failed')
        } else if (attempts >= maxAttempts) {
          throw new Error('Image generation timeout')
        }
        
        await new Promise(resolve => setTimeout(resolve, 2000))
        return pollTaskStatus()
      } catch (pollError) {
        console.error('❌ [GENERATE-AND-SAVE] Polling error:', pollError)
        throw pollError
      }
    }

    const finalStatus = await pollTaskStatus()
    console.log('✅ [GENERATE-AND-SAVE] Generation completed successfully')

    // 8. 更新任务为完成
    await updateImageGenerationTask(task.id, {
      status: 'completed',
      klingTaskId,
      completedAt: new Date(),
    })

    // 9. 上传图片到MinIO并保存到数据库
    const uploadedImages = []
    
    for (let i = 0; i < finalStatus.data.task_result.images.length; i++) {
      const image = finalStatus.data.task_result.images[i]
      console.log(`📤 [GENERATE-AND-SAVE] Processing image ${i + 1}/${finalStatus.data.task_result.images.length}`)
      
      try {
        // 下载图片
        const imageResponse = await fetch(image.url)
        if (!imageResponse.ok) {
          throw new Error(`Failed to download image: ${imageResponse.statusText}`)
        }
        
        const imageBuffer = Buffer.from(await imageResponse.arrayBuffer())
        
        // 上传到MinIO
        const minioUrl = await uploadImageToMinIO(
          imageBuffer,
          `shot-${validatedData.shotNumber}-image-${Date.now()}-${i + 1}.jpg`,
          'image/jpeg'
        )
        
        console.log(`✅ [GENERATE-AND-SAVE] Uploaded to MinIO: ${minioUrl}`)

        // 保存到数据库
        const savedImage = await createGeneratedImage({
          taskId: task.id,
          imageUrl: image.url, // 原始URL
          imageFilename: `shot-${validatedData.shotNumber}-image-${i + 1}.jpg`,
          imageWidth: image.width || 1024,
          imageHeight: image.height || 1024,
          imageFormat: 'jpg',
          isPrimary: i === 0,
          cdnUrl: minioUrl,
          minioObjectName: minioUrl.split('/').pop(),
          minioBucketName: 'images',
          generationTimeSeconds: 30,
          actualPrompt: validatedData.prompt,
          storagePath: minioUrl,
        })
        
        uploadedImages.push({
          ...savedImage,
          url: minioUrl
        })
        
      } catch (uploadError) {
        console.error(`❌ [GENERATE-AND-SAVE] Error processing image ${i + 1}:`, uploadError)
        
        // 如果上传失败，使用原始URL
        const savedImage = await createGeneratedImage({
          taskId: task.id,
          imageUrl: image.url,
          imageFilename: `shot-${validatedData.shotNumber}-image-${i + 1}.jpg`,
          imageWidth: image.width || 1024,
          imageHeight: image.height || 1024,
          imageFormat: 'jpg',
          isPrimary: i === 0,
          cdnUrl: image.url,
          generationTimeSeconds: 30,
          actualPrompt: validatedData.prompt,
        })
        
        uploadedImages.push({
          ...savedImage,
          url: image.url
        })
      }
    }

    console.log('🎉 [GENERATE-AND-SAVE] Complete workflow finished successfully!')
    console.log('🎉 [GENERATE-AND-SAVE] Generated and saved', uploadedImages.length, 'images')

    return NextResponse.json({
      success: true,
      taskId: task.id,
      shotId: shot.id,
      images: uploadedImages.map(img => ({
        id: img.id,
        url: img.url,
        cdnUrl: img.cdn_url,
        width: img.image_width,
        height: img.image_height,
        isPrimary: img.is_primary,
      })),
      message: '图片生成和保存完成'
    })

  } catch (error) {
    console.error('💥 [GENERATE-AND-SAVE] Error in complete workflow:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: 'Invalid request data',
        details: error.errors,
      }, { status: 400 })
    }

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '图片生成和保存失败',
    }, { status: 500 })
  }
}