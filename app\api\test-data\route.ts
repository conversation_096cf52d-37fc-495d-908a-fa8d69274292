import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

// POST /api/test-data - 创建测试数据
export async function POST(request: NextRequest) {
  try {
    // 创建测试用户
    const testUser = await prisma.users.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        username: '测试用户',
        avatar_url: null,
        created_at: new Date(),
        updated_at: new Date()
      }
    })

    // 创建用户偏好设置
    await prisma.user_preferences.upsert({
      where: { user_id: testUser.id },
      update: {},
      create: {
        user_id: testUser.id,
        default_style: 'modern',
        default_duration: 90,
        favorite_tags: ['爱情', '都市'],
        created_at: new Date(),
        updated_at: new Date()
      }
    })

    // 创建测试项目
    const projects = [
      {
        title: '都市爱情故事',
        description: '一个关于现代都市青年爱情的温馨短剧',
        creation_mode: 'keywords',
        style: 'realistic',
        duration: 225, // 3:45
        status: 'completed',
        script_content: '这是一个关于都市爱情的故事...',
        script_data: {
          title: '都市爱情故事',
          totalDuration: 225,
          style: 'realistic',
          shotCount: 6,
          shots: []
        }
      },
      {
        title: '科幻冒险',
        description: '未来世界的冒险故事，充满科技感',
        creation_mode: 'template',
        style: 'sci-fi',
        duration: 150, // 2:30
        status: 'draft',
        script_content: '在未来的世界里...',
        script_data: {
          title: '科幻冒险',
          totalDuration: 150,
          style: 'sci-fi',
          shotCount: 4,
          shots: []
        }
      },
      {
        title: '搞笑日常',
        description: '轻松幽默的日常生活片段',
        creation_mode: 'free',
        style: 'comedy',
        duration: 105, // 1:45
        status: 'completed',
        script_content: '日常生活中的搞笑瞬间...',
        script_data: {
          title: '搞笑日常',
          totalDuration: 105,
          style: 'comedy',
          shotCount: 3,
          shots: []
        }
      },
      {
        title: '悬疑推理',
        description: '扣人心弦的悬疑推理短剧',
        creation_mode: 'keywords',
        style: 'thriller',
        duration: 260, // 4:20
        status: 'draft',
        script_content: '一个神秘的案件...',
        script_data: {
          title: '悬疑推理',
          totalDuration: 260,
          style: 'thriller',
          shotCount: 8,
          shots: []
        }
      },
      {
        title: '历史传奇',
        description: '古代英雄传奇故事',
        creation_mode: 'template',
        style: 'historical',
        duration: 0,
        status: 'draft',
        script_content: null,
        script_data: null
      }
    ]

    const createdProjects = []
    
    for (const projectData of projects) {
      const project = await prisma.script_projects.create({
        data: {
          user_id: testUser.id,
          title: projectData.title,
          description: projectData.description,
          creation_mode: projectData.creation_mode,
          style: projectData.style,
          duration: projectData.duration,
          status: projectData.status,
          script_content: projectData.script_content,
          script_data: projectData.script_data,
          created_at: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000), // 随机过去30天内的时间
          updated_at: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000)   // 随机过去7天内的时间
        }
      })

      // 为已完成的项目创建一些镜头
      if (projectData.status === 'completed' || projectData.status === 'draft') {
        const shotCount = projectData.script_data?.shotCount || 3
        for (let i = 1; i <= shotCount; i++) {
          await prisma.script_shots.create({
            data: {
              project_id: project.id,
              shot_number: i,
              duration: Math.floor(projectData.duration / shotCount),
              shot_type: '中景',
              location: `场景${i}`,
              characters: ['主角'],
              action: `动作描述${i}`,
              dialogue: `对话内容${i}`,
              camera_movement: '固定',
              lighting: '自然光',
              props: [],
              mood: '轻松',
              sound_effect: '背景音乐',
              transition: '淡入淡出',
              created_at: new Date()
            }
          })
        }

        // 为已完成的项目创建图片生成任务和生成的图片
        if (projectData.status === 'completed') {
          // 为每个场景创建图片生成任务
          const shots = await prisma.script_shots.findMany({
            where: { project_id: project.id },
            orderBy: { shot_number: 'asc' }
          })

          for (const shot of shots) {
            const imageTask = await prisma.image_generation_tasks.create({
              data: {
                project_id: project.id,
                shot_id: shot.id,
                user_id: testUser.id,
                task_name: `${projectData.title} - 镜头${shot.shot_number}`,
                prompt: `${shot.location}, ${shot.action}, ${shot.mood} atmosphere, cinematic composition`,
                negative_prompt: 'low quality, blurry, distorted',
                model_name: 'kling-v1-5',
                aspect_ratio: '16:9',
                image_count: 2,
                image_fidelity: 0.8,
                cfg_scale: 7.5,
                status: 'completed',
                created_at: new Date(),
                completed_at: new Date()
              }
            })

            // 为每个任务创建2张图片
            for (let i = 1; i <= 2; i++) {
              await prisma.generated_images.create({
                data: {
                  task_id: imageTask.id,
                  image_url: `/placeholder.svg?height=400&width=600&text=${encodeURIComponent(`${projectData.title}-镜头${shot.shot_number}-${i}`)}`,
                  image_filename: `${projectData.title.replace(/\s+/g, '_')}_shot${shot.shot_number}_${i}.jpg`,
                  image_width: 600,
                  image_height: 400,
                  image_format: 'jpg',
                  quality_score: 0.85 + Math.random() * 0.1,
                  is_primary: i === 1, // 第一张图片设为主图
                  storage_provider: 'local',
                  cdn_url: `/placeholder.svg?height=400&width=600&text=${encodeURIComponent(`${projectData.title}-镜头${shot.shot_number}-${i}`)}`,
                  generation_time_seconds: 12.5 + Math.random() * 10,
                  actual_prompt: `${shot.location}, ${shot.action}, ${shot.mood} atmosphere, cinematic composition`,
                  created_at: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000) // 随机过去24小时内的时间
                }
              })
            }
          }
        }
      }

      // 为每个项目创建生成配置
      await prisma.script_generation_configs.create({
        data: {
          project_id: project.id,
          theme: projectData.title,
          selected_tags: projectData.creation_mode === 'keywords' ? ['爱情', '都市'] : [],
          custom_keywords: projectData.creation_mode === 'keywords' ? '现代都市,青年,温馨' : null,
          selected_mood: '轻松',
          selected_template: projectData.creation_mode === 'template' ? '都市爱情' : null,
          background_setting: projectData.description,
          character1: '主角',
          character2: '配角',
          supporting_character: null,
          required_scenes: null,
          avoid_elements: null,
          created_at: new Date()
        }
      })

      createdProjects.push(project)
    }

    return NextResponse.json({
      success: true,
      message: '测试数据创建成功',
      user: testUser,
      projects: createdProjects
    })

  } catch (error) {
    console.error('Error creating test data:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '创建测试数据时发生未知错误'
    }, { status: 500 })
  }
}

// DELETE /api/test-data - 清理测试数据
export async function DELETE(request: NextRequest) {
  try {
    const testUser = await prisma.users.findUnique({
      where: { email: '<EMAIL>' }
    })

    if (testUser) {
      // 删除相关数据
      await prisma.$transaction(async (tx) => {
        // 删除生成的图片
        await tx.generated_images.deleteMany({
          where: {
            image_generation_tasks: {
              user_id: testUser.id
            }
          }
        })

        // 删除图片生成任务
        await tx.image_generation_tasks.deleteMany({
          where: { user_id: testUser.id }
        })

        // 删除剧本镜头
        await tx.script_shots.deleteMany({
          where: {
            script_projects: {
              user_id: testUser.id
            }
          }
        })

        // 删除角色
        await tx.script_characters.deleteMany({
          where: {
            script_projects: {
              user_id: testUser.id
            }
          }
        })

        // 删除生成配置
        await tx.script_generation_configs.deleteMany({
          where: {
            script_projects: {
              user_id: testUser.id
            }
          }
        })

        // 删除项目
        await tx.script_projects.deleteMany({
          where: { user_id: testUser.id }
        })

        // 删除用户偏好
        await tx.user_preferences.deleteMany({
          where: { user_id: testUser.id }
        })

        // 删除用户
        await tx.users.delete({
          where: { id: testUser.id }
        })
      })
    }

    return NextResponse.json({
      success: true,
      message: '测试数据清理成功'
    })

  } catch (error) {
    console.error('Error cleaning test data:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : '清理测试数据时发生未知错误'
    }, { status: 500 })
  }
}
