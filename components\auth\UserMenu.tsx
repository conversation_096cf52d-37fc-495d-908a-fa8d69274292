'use client'

import { useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { LogOut, Settings, User, Crown, Sparkles } from 'lucide-react'
import { AuthForm } from './AuthForm'

export function UserMenu() {
  const [showAuthDialog, setShowAuthDialog] = useState(false)
  const { user, databaseUser, signOut } = useAuth()

  const handleSignOut = async () => {
    try {
      const { error } = await signOut()
      if (error) {
        console.error('Sign out error:', error)
      }
    } catch (error) {
      console.error('Sign out failed:', error)
    }
  }

  if (!user) {
    return (
      <>
        <Button
          variant="ghost"
          size="sm"
          className="hidden sm:inline-flex text-gray-600 hover:text-gray-900 hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100 transition-all duration-300 rounded-xl font-medium"
          onClick={() => setShowAuthDialog(true)}
        >
          <User className="w-4 h-4 mr-2" />
          登录
        </Button>
        <Button
          size="sm"
          className="bg-gradient-to-r from-violet-600 via-purple-600 via-blue-600 to-cyan-600 hover:from-violet-700 hover:via-purple-700 hover:via-blue-700 hover:to-cyan-700 text-white shadow-xl hover:shadow-2xl hover:shadow-purple-500/25 transition-all duration-300 transform hover:scale-105 active:scale-95 rounded-xl font-semibold relative overflow-hidden group"
          onClick={() => setShowAuthDialog(true)}
        >
          {/* 按钮光效 */}
          <div className="absolute inset-0 bg-gradient-to-r from-white/20 via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          <div className="relative flex items-center">
            <Sparkles className="w-4 h-4 mr-2" />
            注册
          </div>
        </Button>

        <Dialog open={showAuthDialog} onOpenChange={setShowAuthDialog}>
          <DialogContent className="sm:max-w-md border-0 shadow-2xl bg-gradient-to-br from-violet-50/80 via-purple-50/60 via-blue-50/60 to-cyan-50/80 p-0 overflow-hidden backdrop-blur-xl">
            {/* 背景装饰效果 */}
            <div className="absolute inset-0 bg-gradient-to-br from-violet-100/30 via-purple-100/20 via-blue-100/20 to-cyan-100/30"></div>
            <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-violet-300/20 to-transparent rounded-full blur-3xl"></div>
            <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-cyan-300/20 to-transparent rounded-full blur-2xl"></div>

            <div className="relative p-8">
              <DialogHeader className="text-center mb-8">
                <div className="flex items-center justify-center mb-4">
                  <div className="w-2 h-2 bg-violet-400 rounded-full animate-pulse"></div>
                  <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse delay-100 mx-2"></div>
                  <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse delay-200"></div>
                  <div className="w-2 h-2 bg-cyan-400 rounded-full animate-pulse delay-300 mx-2"></div>
                  <div className="w-2 h-2 bg-teal-400 rounded-full animate-pulse delay-500"></div>
                </div>
                <DialogTitle className="text-3xl font-bold bg-gradient-to-r from-violet-600 via-purple-600 via-blue-600 to-cyan-600 bg-clip-text text-transparent mb-2">
                  欢迎使用
                </DialogTitle>
                <DialogDescription className="text-gray-600 text-lg font-medium">
                  登录或注册账号，开始您的AI短剧创作之旅
                </DialogDescription>
              </DialogHeader>
              <AuthForm onSuccess={() => setShowAuthDialog(false)} />
            </div>
          </DialogContent>
        </Dialog>
      </>
    )
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="relative h-12 w-12 rounded-full hover:bg-gradient-to-br hover:from-violet-50 hover:to-purple-50 transition-all duration-300 ring-2 ring-transparent hover:ring-violet-200/50 hover:shadow-lg group">
          <Avatar className="h-10 w-10 shadow-lg ring-2 ring-white group-hover:ring-violet-200 transition-all duration-300">
            <AvatarImage
              src={databaseUser?.avatar_url || ''}
              alt={databaseUser?.username || user.email || 'User'}
              className="object-cover"
            />
            <AvatarFallback className="bg-gradient-to-br from-violet-500 via-purple-500 via-blue-500 to-cyan-500 text-white font-bold text-sm">
              {(databaseUser?.username || user.email || 'U').charAt(0).toUpperCase()}
            </AvatarFallback>
          </Avatar>
          {/* 在线状态指示器 */}
          <div className="absolute -bottom-0.5 -right-0.5 w-3.5 h-3.5 bg-emerald-500 border-2 border-white rounded-full shadow-sm">
            <div className="w-full h-full bg-emerald-400 rounded-full animate-ping opacity-75"></div>
          </div>
          {/* VIP标识 */}
          <div className="absolute -top-1 -left-1 w-4 h-4 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center shadow-sm">
            <Crown className="w-2.5 h-2.5 text-white" />
          </div>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-72 bg-white/95 backdrop-blur-xl border-gray-200/50 shadow-2xl rounded-2xl p-3" align="end" forceMount>
        <DropdownMenuLabel className="font-normal p-4 bg-gradient-to-r from-violet-50 via-purple-50 to-blue-50 rounded-xl mb-3 relative overflow-hidden">
          {/* 背景装饰 */}
          <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-bl from-violet-200/30 to-transparent rounded-full blur-xl"></div>
          <div className="absolute bottom-0 left-0 w-12 h-12 bg-gradient-to-tr from-blue-200/30 to-transparent rounded-full blur-lg"></div>

          <div className="flex items-center space-x-4 relative z-10">
            <div className="relative">
              <Avatar className="h-12 w-12 ring-2 ring-white shadow-lg">
                <AvatarImage
                  src={databaseUser?.avatar_url || ''}
                  alt={databaseUser?.username || user.email || 'User'}
                  className="object-cover"
                />
                <AvatarFallback className="bg-gradient-to-br from-violet-500 via-purple-500 via-blue-500 to-cyan-500 text-white font-bold">
                  {(databaseUser?.username || user.email || 'U').charAt(0).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              {/* VIP徽章 */}
              <div className="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-full flex items-center justify-center shadow-md">
                <Crown className="w-3 h-3 text-white" />
              </div>
            </div>
            <div className="flex flex-col space-y-1.5 flex-1">
              <p className="text-sm font-bold text-gray-900 leading-none">
                {databaseUser?.username || user.email}
              </p>
              <p className="text-xs text-gray-500 leading-none">
                {user.email}
              </p>
              <div className="flex items-center gap-1 mt-1">
                <div className="w-1.5 h-1.5 bg-emerald-400 rounded-full"></div>
                <span className="text-xs text-emerald-600 font-medium">在线</span>
              </div>
            </div>
          </div>
        </DropdownMenuLabel>
        <DropdownMenuSeparator className="bg-gradient-to-r from-transparent via-gray-200 to-transparent my-2" />
        <DropdownMenuItem className="rounded-xl p-3 hover:bg-gradient-to-r hover:from-violet-50 hover:to-purple-50 transition-all duration-200 cursor-pointer group">
          <User className="mr-3 h-4 w-4 text-violet-500 group-hover:text-violet-600 transition-colors" />
          <span className="text-gray-700 font-medium group-hover:text-gray-900">个人资料</span>
        </DropdownMenuItem>
        <DropdownMenuItem className="rounded-xl p-3 hover:bg-gradient-to-r hover:from-blue-50 hover:to-cyan-50 transition-all duration-200 cursor-pointer group">
          <Settings className="mr-3 h-4 w-4 text-blue-500 group-hover:text-blue-600 transition-colors" />
          <span className="text-gray-700 font-medium group-hover:text-gray-900">设置</span>
        </DropdownMenuItem>
        <DropdownMenuSeparator className="bg-gradient-to-r from-transparent via-gray-200 to-transparent my-2" />
        <DropdownMenuItem
          onClick={handleSignOut}
          className="rounded-xl p-3 hover:bg-gradient-to-r hover:from-red-50 hover:to-pink-50 transition-all duration-200 cursor-pointer text-red-600 hover:text-red-700 group"
        >
          <LogOut className="mr-3 h-4 w-4 group-hover:scale-110 transition-transform" />
          <span className="font-medium">退出登录</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}