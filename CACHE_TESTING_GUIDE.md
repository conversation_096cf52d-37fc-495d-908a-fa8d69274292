# 视频状态缓存功能测试指南

## 测试环境准备

1. 确保项目正在开发模式下运行
2. 打开浏览器开发者工具的控制台
3. 进入视频生成步骤页面

## 测试场景

### 1. 基本缓存功能测试

#### 步骤：
1. 进入视频生成页面，等待自动刷新完成
2. 在控制台中执行：`window.logVideoStatusCache()`
3. 观察缓存状态和条目数量

#### 预期结果：
- 控制台显示缓存调试信息
- 显示已缓存的镜头数量和状态
- 每个缓存条目显示状态、年龄和剩余时间

### 2. 缓存命中测试

#### 步骤：
1. 等待自动刷新完成后，立即点击某个场景的"刷新视频"按钮
2. 观察控制台日志和弹出的提示

#### 预期结果：
- 控制台显示：`💾 [REFRESH-VIDEO] 使用缓存数据`
- 弹出提示显示"（来自缓存）"字样
- 响应速度很快，无需等待API调用

### 3. 缓存过期测试

#### 步骤：
1. 等待缓存过期（processing状态1分钟，其他状态5分钟）
2. 或者在控制台执行以下代码强制过期：
```javascript
// 强制所有缓存过期（仅用于测试）
const videoStatusCache = window.logVideoStatusCache.__cache;
if (videoStatusCache) {
  videoStatusCache.forEach((value, key) => {
    value.expiresAt = Date.now() - 1000; // 设为1秒前过期
  });
}
```
3. 再次点击刷新按钮

#### 预期结果：
- 缓存过期后会重新查询API
- 控制台不再显示"使用缓存数据"
- 弹出提示不再显示"（来自缓存）"

### 4. 缓存状态指示器测试

#### 步骤：
1. 观察场景卡片上的缓存状态指示器
2. 鼠标悬停在黄色的"💾 {秒数}s"徽章上

#### 预期结果：
- 有缓存的场景显示黄色徽章
- 徽章显示缓存年龄（秒数）
- 鼠标悬停显示详细信息（状态和缓存时间）

### 5. 不同状态缓存时间测试

#### 步骤：
1. 找到不同状态的镜头（completed、processing、failed）
2. 观察它们的缓存过期时间

#### 预期结果：
- completed和failed状态：缓存5分钟
- processing状态：缓存1分钟
- 在控制台调试信息中可以看到不同的剩余时间

### 6. 缓存清理测试

#### 步骤：
1. 等待1分钟以上，让自动清理机制运行
2. 或者刷新页面
3. 执行：`window.logVideoStatusCache()`

#### 预期结果：
- 过期的缓存条目被自动清理
- 页面刷新后缓存被完全清空
- 缓存条目数量减少或为0

## 性能测试

### 1. API调用减少测试

#### 步骤：
1. 打开网络面板
2. 进入页面，观察自动刷新的API调用
3. 立即再次手动刷新相同的镜头
4. 比较API调用次数

#### 预期结果：
- 第一次自动刷新：正常的API调用
- 缓存命中时：无额外API调用
- 网络面板显示请求数量减少

### 2. 响应时间测试

#### 步骤：
1. 记录首次刷新的响应时间
2. 记录缓存命中时的响应时间
3. 比较两者差异

#### 预期结果：
- 缓存命中时响应时间显著减少
- 用户体验更加流畅

## 调试技巧

### 1. 查看缓存状态
```javascript
// 查看当前缓存
window.logVideoStatusCache()

// 查看特定镜头的缓存
console.log(videoStatusCache.get('shotId'))
```

### 2. 手动清空缓存
```javascript
// 清空所有缓存（仅用于测试）
setVideoStatusCache(new Map())
```

### 3. 模拟不同状态
```javascript
// 手动添加测试缓存（仅用于测试）
updateVideoStatusCache('test-shot-id', {
  status: 'completed',
  taskId: 'test-task',
  videos: [{ url: 'test.mp4' }]
})
```

## 常见问题排查

### 1. 缓存不生效
- 检查shotId是否存在
- 确认缓存时间设置正确
- 查看控制台是否有错误信息

### 2. 缓存过期太快
- 检查系统时间是否正确
- 确认CACHE_EXPIRY_TIME设置

### 3. 缓存状态指示器不显示
- 确认场景有shotData和shotId
- 检查缓存是否真的存在且未过期

## 预期的控制台日志

正常工作时应该看到以下日志：
```
🔄 [AUTO-REFRESH] 开始批量自动刷新所有镜头的视频状态
💾 [AUTO-REFRESH] 使用缓存数据，镜头 shot-123
✅ [AUTO-REFRESH] 批量自动刷新完成
💾 [AUTO-REFRESH] 当前缓存状态: 3 个镜头已缓存
💾 [REFRESH-VIDEO] 使用缓存数据（30秒前），镜头 shot-123
```
