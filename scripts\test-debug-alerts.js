#!/usr/bin/env node

/**
 * 测试新的调试alert功能
 * 
 * 这个脚本会检查数据库中的任务状态，帮助验证调试信息的准确性
 */

async function testDebugAlerts() {
  console.log('🔍 测试调试Alert功能');
  console.log('='.repeat(50));
  
  const { PrismaClient } = require('../lib/generated/prisma');
  const prisma = new PrismaClient();
  
  try {
    await prisma.$connect();
    
    // 1. 检查有项目ID和镜头ID的任务
    console.log('📋 1. 检查有完整关联信息的任务');
    const tasksWithFullInfo = await prisma.video_generation_tasks.findMany({
      where: {
        AND: [
          { project_id: { not: null } },
          { shot_id: { not: null } }
        ]
      },
      include: {
        generated_videos: true,
        script_projects: {
          select: { title: true }
        },
        script_shots: {
          select: { shot_number: true, location: true }
        }
      },
      orderBy: { created_at: 'desc' },
      take: 3
    });
    
    console.log(`   找到 ${tasksWithFullInfo.length} 个有完整关联信息的任务`);
    tasksWithFullInfo.forEach((task, index) => {
      console.log(`   任务${index + 1}:`);
      console.log(`     ID: ${task.id}`);
      console.log(`     项目: ${task.script_projects?.title || '未知'}`);
      console.log(`     镜头: ${task.script_shots?.shot_number || '未知'} - ${task.script_shots?.location || '未知'}`);
      console.log(`     状态: ${task.status}`);
      console.log(`     Kling任务ID: ${task.kling_task_id || '无'}`);
      console.log(`     视频数量: ${task.generated_videos.length}`);
    });
    
    // 2. 检查缺少Kling任务ID的任务
    console.log('\n📋 2. 检查缺少Kling任务ID的任务');
    const tasksWithoutKlingId = await prisma.video_generation_tasks.findMany({
      where: {
        kling_task_id: null
      },
      include: {
        script_projects: {
          select: { title: true }
        }
      },
      orderBy: { created_at: 'desc' },
      take: 3
    });
    
    console.log(`   找到 ${tasksWithoutKlingId.length} 个缺少Kling任务ID的任务`);
    tasksWithoutKlingId.forEach((task, index) => {
      console.log(`   任务${index + 1}:`);
      console.log(`     ID: ${task.id}`);
      console.log(`     项目: ${task.script_projects?.title || '未知'}`);
      console.log(`     状态: ${task.status}`);
      console.log(`     错误信息: ${task.error_message || '无'}`);
      console.log(`     创建时间: ${new Date(task.created_at).toLocaleString()}`);
    });
    
    // 3. 检查项目和镜头关联情况
    console.log('\n📋 3. 检查项目和镜头关联情况');
    const projectStats = await prisma.video_generation_tasks.groupBy({
      by: ['project_id'],
      _count: {
        id: true
      },
      where: {
        project_id: { not: null }
      }
    });
    
    console.log(`   涉及 ${projectStats.length} 个不同的项目`);
    for (const stat of projectStats) {
      const project = await prisma.script_projects.findUnique({
        where: { id: stat.project_id },
        select: { title: true }
      });
      console.log(`     项目 "${project?.title || '未知'}": ${stat._count.id} 个任务`);
    }
    
    // 4. 检查镜头关联情况
    const shotStats = await prisma.video_generation_tasks.groupBy({
      by: ['shot_id'],
      _count: {
        id: true
      },
      where: {
        shot_id: { not: null }
      }
    });
    
    console.log(`   涉及 ${shotStats.length} 个不同的镜头`);
    for (const stat of shotStats.slice(0, 5)) { // 只显示前5个
      const shot = await prisma.script_shots.findUnique({
        where: { id: stat.shot_id },
        select: { shot_number: true, location: true }
      });
      console.log(`     镜头 ${shot?.shot_number || '未知'} (${shot?.location || '未知'}): ${stat._count.id} 个任务`);
    }
    
    // 5. 检查任务状态分布
    console.log('\n📋 4. 检查任务状态分布');
    const statusStats = await prisma.video_generation_tasks.groupBy({
      by: ['status'],
      _count: {
        id: true
      }
    });
    
    console.log('   任务状态分布:');
    statusStats.forEach(stat => {
      console.log(`     ${stat.status}: ${stat._count.id} 个任务`);
    });
    
    // 6. 生成测试建议
    console.log('\n💡 测试建议:');
    
    if (tasksWithFullInfo.length > 0) {
      const testTask = tasksWithFullInfo[0];
      console.log('   可以测试的场景:');
      console.log(`   • 项目ID: ${testTask.project_id}`);
      console.log(`   • 镜头ID: ${testTask.shot_id}`);
      console.log(`   • 任务状态: ${testTask.status}`);
      console.log(`   • 有Kling任务ID: ${testTask.kling_task_id ? '是' : '否'}`);
    }
    
    if (tasksWithoutKlingId.length > 0) {
      console.log('   可以测试"缺少Kling任务ID"的场景');
    }
    
    const tasksWithoutProjectOrShot = await prisma.video_generation_tasks.count({
      where: {
        OR: [
          { project_id: null },
          { shot_id: null }
        ]
      }
    });
    
    if (tasksWithoutProjectOrShot > 0) {
      console.log(`   有 ${tasksWithoutProjectOrShot} 个任务缺少项目ID或镜头ID，可以测试相关调试功能`);
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

// 运行测试
testDebugAlerts().catch(console.error);
